import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProfileProvider, useProfile } from './contexts/ProfileContext';
import { LiveTVProvider } from './contexts/LiveTVContext';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import Login from './pages/Login';
import ProfilePicker from './pages/Profiles';
import Dashboard from './pages/Dashboard';
import TVShows from './pages/TVShows';
import Anime from './pages/Anime';
import Watchlist from './pages/Watchlist';
import SearchResults from './pages/SearchResults';
import MovieDetails from './pages/MovieDetails';
import ShowDetails from './pages/ShowDetails';
import LiveStreaming from './pages/LiveStreaming';
import ContentDiscoveryPage from './pages/ContentDiscovery';
import Header from './components/Header';

function MainLayout() {
  return (
    <div style={{ minHeight: '100vh', background: '#111', display: 'flex', flexDirection: 'column' }}>
      <Header />
      <div style={{ flex: 1, padding: '2rem', background: '#18181b', minHeight: 0 }}>
        <Routes>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/discovery" element={<ContentDiscoveryPage />} />
          <Route path="/tvshows" element={<TVShows />} />
          <Route path="/anime" element={<Anime />} />
          <Route path="/watchlist" element={<Watchlist />} />
          <Route path="/livestreaming" element={<LiveStreaming />} />
          <Route path="/search" element={<SearchResults />} />
          <Route path="/movie/:id" element={<MovieDetails />} />
          <Route path="/show/:id" element={<ShowDetails />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </div>
    </div>
  );
}

function AppContent() {
  const { user, loading: authLoading } = useAuth();
  const { profile, isProfileLoaded, loading: profileLoading } = useProfile();

  // 1. Still authenticating
  if (authLoading) {
    return <div style={{ color: 'white', background: 'black', minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: 32 }}>Loading... (authLoading: true)</div>;
  }

  // 2. Not authenticated: ONLY show login page, no other UI
  if (!user) {
    return (
      <div style={{ minHeight: '100vh', background: '#111', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Login />
      </div>
    );
  }

  // 3. Profile context still loading
  if (profileLoading || !isProfileLoaded) {
    return <div style={{ color: 'white', background: 'black', minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: 32 }}>Loading profiles... (profileLoading: {String(profileLoading)}, isProfileLoaded: {String(isProfileLoaded)})</div>;
  }

  // 4. No profile selected yet (show profile picker)
  if (!profile) {
    return <ProfilePicker />;
  }

  // 5. Main app layout with only top bar and full-width content
  return <MainLayout />;
}

function App() {
  return (
    <AuthProvider>
      <ProfileProvider>
        <LiveTVProvider>
          <AppContent />
        </LiveTVProvider>
      </ProfileProvider>
    </AuthProvider>
  );
}

export default App; 