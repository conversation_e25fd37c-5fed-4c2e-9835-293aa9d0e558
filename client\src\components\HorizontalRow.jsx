import React, { useRef, useEffect, useCallback } from 'react';

/**
 * HorizontalRow - A reusable, infinite-scrollable horizontal row for media cards.
 * Props:
 *   items: Array of items to render
 *   renderItem: (item, idx) => ReactNode
 *   loadMore: () => void (called when near end)
 *   loading: boolean (show loading indicator at end)
 *   title: string (optional row title)
 *   rowId: string (for a11y and testability)
 */
const HorizontalRow = ({ items, renderItem, loadMore, loading, title, rowId }) => {
  const scrollRef = useRef(null);
  const loadingRef = useRef(null);

  // Infinite scroll: load more when near end
  useEffect(() => {
    if (!scrollRef.current || !loadMore) return;
    const handleScroll = () => {
      const el = scrollRef.current;
      if (!el) return;
      if (el.scrollWidth - el.scrollLeft - el.clientWidth < 400 && !loading) {
        loadMore();
      }
    };
    const el = scrollRef.current;
    el.addEventListener('scroll', handleScroll);
    return () => el.removeEventListener('scroll', handleScroll);
  }, [loadMore, loading]);

  // Keyboard navigation (left/right arrows)
  const handleKeyDown = useCallback((e) => {
    if (!scrollRef.current) return;
    if (e.key === 'ArrowRight') {
      scrollRef.current.scrollBy({ left: 220, behavior: 'smooth' });
    } else if (e.key === 'ArrowLeft') {
      scrollRef.current.scrollBy({ left: -220, behavior: 'smooth' });
    }
  }, []);

  // Mouse wheel horizontal scroll
  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;
    const onWheel = (e) => {
      if (Math.abs(e.deltaX) < Math.abs(e.deltaY)) {
        el.scrollBy({ left: e.deltaY, behavior: 'auto' });
        e.preventDefault();
      }
    };
    el.addEventListener('wheel', onWheel, { passive: false });
    return () => el.removeEventListener('wheel', onWheel);
  }, []);

  return (
    <div className="mb-10">
      {title && <h2 className="text-2xl font-bold text-white mb-4 ml-2">{title}</h2>}
      <div
        ref={scrollRef}
        className="flex gap-4 overflow-x-auto scrollbar-hide pb-4 pt-2 items-center"
        tabIndex={0}
        aria-label={title || rowId}
        id={rowId}
        onKeyDown={handleKeyDown}
        style={{ scrollSnapType: 'x mandatory', outline: 'none' }}
      >
        {items.map((item, idx) => (
          <div
            key={item.id || idx}
            className="flex-shrink-0 w-[200px] max-w-[200px] aspect-[2/3] scroll-snap-align-start"
            style={{ scrollSnapAlign: 'start' }}
          >
            {renderItem(item, idx)}
          </div>
        ))}
        {loading && (
          <div ref={loadingRef} className="w-[200px] h-[300px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HorizontalRow; 