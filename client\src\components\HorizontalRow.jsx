import React, { useRef, useEffect, useCallback } from 'react';

/**
 * HorizontalRow - A reusable, infinite-scrollable horizontal row for media cards.
 * Props:
 *   items: Array of items to render
 *   renderItem: (item, idx) => ReactNode
 *   loadMore: () => void (called when near end)
 *   loading: boolean (show loading indicator at end)
 *   title: string (optional row title)
 *   rowId: string (for a11y and testability)
 */
const HorizontalRow = ({ items, renderItem, loadMore, loading, title, rowId }) => {
  const scrollRef = useRef(null);
  const loadingRef = useRef(null);
  const sentinelRef = useRef(null);
  const observerRef = useRef(null);

  // Infinite scroll: Intersection Observer for robust loadMore
  useEffect(() => {
    if (!loadMore || !sentinelRef.current) return;
    if (observerRef.current) observerRef.current.disconnect();
    observerRef.current = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loading) {
          loadMore();
        }
      },
      { root: scrollRef.current, rootMargin: '200px', threshold: 0.1 }
    );
    observerRef.current.observe(sentinelRef.current);
    return () => observerRef.current && observerRef.current.disconnect();
  }, [loadMore, loading, items.length]);

  // Keyboard navigation (left/right arrows)
  const handleKeyDown = useCallback((e) => {
    if (!scrollRef.current) return;
    if (e.key === 'ArrowRight') {
      scrollRef.current.scrollBy({ left: 220, behavior: 'smooth' });
    } else if (e.key === 'ArrowLeft') {
      scrollRef.current.scrollBy({ left: -220, behavior: 'smooth' });
    }
  }, []);

  // Mouse wheel horizontal scroll (only hijack if can scroll further)
  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;
    const onWheel = (e) => {
      // Only hijack if can scroll horizontally
      const atStart = el.scrollLeft === 0;
      const atEnd = Math.abs(el.scrollWidth - el.scrollLeft - el.clientWidth) < 2;
      const isHorizontal = Math.abs(e.deltaX) > Math.abs(e.deltaY);
      if (!isHorizontal && !(atStart && e.deltaY < 0) && !(atEnd && e.deltaY > 0)) {
        el.scrollBy({ left: e.deltaY, behavior: 'auto' });
        e.preventDefault();
      }
      // Otherwise, let the event bubble for vertical scroll
    };
    el.addEventListener('wheel', onWheel, { passive: false });
    return () => el.removeEventListener('wheel', onWheel);
  }, []);

  return (
    <div className="mb-10">
      {title && <h2 className="text-2xl font-bold text-white mb-4 ml-2">{title}</h2>}
      <div className="overflow-x-auto scrollbar-hide px-12 py-8" style={{ overflowY: 'visible' }}>
        <div
          ref={scrollRef}
          className="flex gap-8 justify-center items-start overflow-visible pb-4 pt-2 -mx-12"
          tabIndex={0}
          aria-label={title || rowId}
          id={rowId}
          onKeyDown={handleKeyDown}
          style={{ scrollSnapType: 'x mandatory', outline: 'none', overflow: 'visible' }}
        >
          {items.map((item, idx) => (
            <div
              key={item.id || idx}
              className="flex-shrink-0 w-[200px] max-w-[200px] aspect-[2/3] scroll-snap-align-start"
              style={{ scrollSnapAlign: 'start', overflow: 'visible' }}
            >
              {renderItem(item, idx)}
            </div>
          ))}
          {/* Sentinel for infinite scroll */}
          <div ref={sentinelRef} style={{ width: 1, height: 1 }} />
          {loading && (
            <div ref={loadingRef} className="w-[200px] h-[300px] flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HorizontalRow; 