// Local storage utility for standalone app behavior
// Uses backend API to store data in local files instead of browser localStorage

import { apiFetch, API_BASE_URL } from './api.js';

const API_BASE = `${API_BASE_URL}/api/user-data`;

// Helper to add auth header if token exists
const buildHeaders = (extra = {}) => {
  const headers = { 'Content-Type': 'application/json', ...extra };
  const token = localStorage.getItem('torvie_jwt');
  if (token && import.meta.env.VITE_REQUIRE_API_AUTH === 'true') {
    headers['Authorization'] = `Bearer ${token}`;
  }
  return headers;
};

export class LocalAppStorage {
  constructor() {
    console.log('🏠 LocalAppStorage: Initialized for standalone app storage');
  }

  // Profile management
  async getProfiles(userId) {
    try {
      console.log(`📂 LocalAppStorage: Loading profiles for user ${userId}`);
      const response = await fetch(`${API_BASE}/profiles/${userId}`, { headers: buildHeaders() });
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ LocalAppStorage: Loaded ${data.profiles?.length || 0} profiles`);
        return data.profiles;
      } else {
        console.log(`📭 LocalAppStorage: No profiles found for user ${userId}`);
        return null;
      }
    } catch (error) {
      console.error('❌ LocalAppStorage: Error loading profiles:', error);
      return null;
    }
  }

  async saveProfiles(userId, profiles) {
    try {
      console.log(`💾 LocalAppStorage: Saving ${profiles.length} profiles for user ${userId}`);
      const response = await fetch(`${API_BASE}/profiles/${userId}`, {
        method: 'POST',
        headers: buildHeaders(),
        body: JSON.stringify({ profiles }),
      });
      
      const data = await response.json();
      if (data.success) {
        console.log(`✅ LocalAppStorage: Successfully saved profiles for user ${userId}`);
        return true;
      } else {
        console.error('❌ LocalAppStorage: Failed to save profiles:', data.error);
        return false;
      }
    } catch (error) {
      console.error('❌ LocalAppStorage: Error saving profiles:', error);
      return false;
    }
  }

  // Current profile management
  async getCurrentProfile(userId) {
    try {
      const response = await fetch(`${API_BASE}/current-profile/${userId}`, { headers: buildHeaders() });
      const data = await response.json();
      
      if (data.success) {
        return data.currentProfile;
      }
      return null;
    } catch (error) {
      console.error('❌ LocalAppStorage: Error loading current profile:', error);
      return null;
    }
  }

  async saveCurrentProfile(userId, currentProfile) {
    try {
      console.log(`💾 LocalAppStorage: Saving current profile for user ${userId}:`, currentProfile ? `"${currentProfile.name}" (ID: ${currentProfile.id})` : 'null');
      const response = await fetch(`${API_BASE}/current-profile/${userId}`, {
        method: 'POST',
        headers: buildHeaders(),
        body: JSON.stringify({ currentProfile }),
      });
      
      const data = await response.json();
      if (data.success) {
        console.log(`✅ LocalAppStorage: Successfully saved current profile for user ${userId}`);
        return true;
      } else {
        console.error(`❌ LocalAppStorage: API returned success=false for saving current profile`);
        return false;
      }
    } catch (error) {
      console.error('❌ LocalAppStorage: Error saving current profile:', error);
      return false;
    }
  }

  // Watchlist management
  async getWatchlist(userId, profileId) {
    try {
      console.log(`📂 LocalAppStorage: Loading watchlist for user ${userId}, profile ${profileId}`);
      const response = await fetch(`${API_BASE}/watchlist/${userId}/${profileId}`, { headers: buildHeaders() });
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ LocalAppStorage: Loaded ${data.watchlist.length} watchlist items`);
        return data.watchlist;
      }
      return [];
    } catch (error) {
      console.error('❌ LocalAppStorage: Error loading watchlist:', error);
      return [];
    }
  }

  async saveWatchlist(userId, profileId, watchlist) {
    try {
      console.log(`💾 LocalAppStorage: Saving ${watchlist.length} items to watchlist for user ${userId}, profile ${profileId}`);
      const response = await fetch(`${API_BASE}/watchlist/${userId}/${profileId}`, {
        method: 'POST',
        headers: buildHeaders(),
        body: JSON.stringify({ watchlist }),
      });
      
      const data = await response.json();
      if (data.success) {
        console.log(`✅ LocalAppStorage: Successfully saved watchlist`);
        return true;
      } else {
        console.error('❌ LocalAppStorage: Failed to save watchlist:', data.error);
        return false;
      }
    } catch (error) {
      console.error('❌ LocalAppStorage: Error saving watchlist:', error);
      return false;
    }
  }

  // Debug function
  async getAllData() {
    try {
      const response = await fetch(`${API_BASE}/debug`, { headers: buildHeaders() });
      const data = await response.json();
      
      if (data.success) {
        console.log('🔍 LocalAppStorage: All stored data:', data);
        return data;
      }
      return null;
    } catch (error) {
      console.error('❌ LocalAppStorage: Error getting debug data:', error);
      return null;
    }
  }

  // Health check
  async isAvailable() {
    try {
      const response = await apiFetch('/api/health');
      return response.ok;
    } catch (error) {
      console.error('❌ LocalAppStorage: Backend not available:', error);
      return false;
    }
  }
}

// Create singleton instance
export const localAppStorage = new LocalAppStorage();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.localAppStorage = localAppStorage;
  console.log('🏠 Local App Storage initialized - data will persist on local machine like a standalone app!');
  console.log('🔧 Debug commands:');
  console.log('  window.localAppStorage.getAllData() - See all stored data');
  console.log('  window.localAppStorage.isAvailable() - Check if backend is running');
} 