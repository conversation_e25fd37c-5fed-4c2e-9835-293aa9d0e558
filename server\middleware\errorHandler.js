import { logger } from '../utils/logger.js';

// Custom error classes
export class AppError extends Error {
  constructor(message, statusCode, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message, details = []) {
    super(message, 400);
    this.details = details;
  }
}

export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404);
  }
}

export class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429);
  }
}

// Error handler middleware
export const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error({
    event: 'error_handler',
    error: err.message,
    stack: err.stack,
    statusCode: err.statusCode || 500,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = new NotFoundError(message);
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = new ValidationError(message);
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = new ValidationError(message);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = new AuthenticationError(message);
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = new AuthenticationError(message);
  }

  // Rate limiting errors
  if (err.statusCode === 429) {
    error = new RateLimitError(err.message);
  }

  // Database connection errors
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    const message = 'Database connection failed';
    error = new AppError(message, 503);
  }

  // Redis connection errors
  if (err.code === 'ECONNREFUSED' && err.message.includes('redis')) {
    const message = 'Cache service unavailable';
    error = new AppError(message, 503);
  }

  // File system errors
  if (err.code === 'ENOENT') {
    const message = 'File not found';
    error = new NotFoundError(message);
  }

  // Network errors
  if (err.code === 'ENOTFOUND' || err.code === 'ETIMEDOUT') {
    const message = 'External service unavailable';
    error = new AppError(message, 503);
  }

  // Default error
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';

  // Don't leak error details in production
  const errorResponse = {
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      details: error.details
    })
  };

  // Add rate limit headers if applicable
  if (statusCode === 429) {
    res.setHeader('Retry-After', '60');
  }

  res.status(statusCode).json(errorResponse);
};

// Async error wrapper
export const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Not found handler
export const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

// Security error handler
export const securityErrorHandler = (err, req, res, next) => {
  // Log security-related errors with higher priority
  if (err.statusCode === 401 || err.statusCode === 403 || err.statusCode === 429) {
    logger.warn({
      event: 'security_violation',
      error: err.message,
      statusCode: err.statusCode,
      path: req.path,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id
    });
  }
  
  next(err);
};

// Graceful shutdown handler
export const gracefulShutdown = (server) => {
  return (signal) => {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);
    
    server.close((err) => {
      if (err) {
        logger.error('Error during server shutdown:', err);
        process.exit(1);
      }
      
      logger.info('Server closed. Exiting process.');
      process.exit(0);
    });
  };
};

// Unhandled rejection handler
export const handleUnhandledRejection = (reason, promise) => {
  logger.error({
    event: 'unhandled_rejection',
    reason: reason?.message || reason,
    stack: reason?.stack
  });
  
  // Close server and exit process
  process.exit(1);
};

// Uncaught exception handler
export const handleUncaughtException = (error) => {
  logger.error({
    event: 'uncaught_exception',
    error: error.message,
    stack: error.stack
  });
  
  // Close server and exit process
  process.exit(1);
};

// Setup global error handlers
export const setupErrorHandlers = () => {
  process.on('unhandledRejection', handleUnhandledRejection);
  process.on('uncaughtException', handleUncaughtException);
}; 