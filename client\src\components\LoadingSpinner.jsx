import React from 'react';

const LoadingSpinner = ({ 
  size = 'medium', 
  color = 'white', 
  text = '', 
  fullScreen = false,
  overlay = false 
}) => {
  const sizeClasses = {
    small: 'w-4 h-4 border-2',
    medium: 'w-8 h-8 border-3',
    large: 'w-12 h-12 border-4'
  };

  const colorClasses = {
    white: 'border-white',
    blue: 'border-blue-600',
    gray: 'border-gray-400'
  };

  const spinnerElement = (
    <div className="flex flex-col items-center justify-center gap-3">
      <div 
        className={`
          ${sizeClasses[size]} 
          ${colorClasses[color]} 
          border-t-transparent 
          rounded-full 
          animate-spin
        `}
        style={{ borderTopColor: 'transparent' }}
      />
      {text && (
        <p className={`text-sm ${color === 'white' ? 'text-white' : 'text-gray-400'}`}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50">
        {spinnerElement}
      </div>
    );
  }

  if (overlay) {
    return (
      <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center z-40 rounded-lg">
        {spinnerElement}
      </div>
    );
  }

  return spinnerElement;
};

// Inline loading spinner for buttons
export const ButtonSpinner = () => (
  <svg 
    className="animate-spin h-4 w-4 text-white" 
    xmlns="http://www.w3.org/2000/svg" 
    fill="none" 
    viewBox="0 0 24 24"
  >
    <circle 
      className="opacity-25" 
      cx="12" 
      cy="12" 
      r="10" 
      stroke="currentColor" 
      strokeWidth="4"
    />
    <path 
      className="opacity-75" 
      fill="currentColor" 
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

// Skeleton loader for content placeholders
export const SkeletonLoader = ({ height = 'h-48', width = 'w-full', rounded = 'rounded-lg' }) => (
  <div className={`${height} ${width} ${rounded} bg-gray-800 animate-pulse`} />
);

export default LoadingSpinner; 