import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { imageLoader, performanceMonitor, debounce } from '../utils/performance';
import { watchlistStorage } from '../utils/watchlistStorage';

// Memoized genre mapping for better performance
const genreIdMap = {
  28: 'Action', 12: 'Adventure', 16: 'Animation', 35: 'Comedy',
  80: 'Crime', 99: 'Documentary', 18: 'Drama', 10751: 'Family',
  14: 'Fantasy', 36: 'History', 27: 'Horror', 10402: 'Music',
  9648: 'Mystery', 10749: 'Romance', 878: 'Science Fiction',
  10770: 'TV Movie', 53: 'Thriller', 10752: 'War', 37: 'Western'
};

// Frosted glass action button component (matching MediaCard)
const QuickActionButton = ({ icon, label, onClick }) => (
  <button 
    title={label} 
    className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm transform hover:scale-110 hover:bg-white/20 transition-all duration-200 border border-white/20"
    onClick={(e) => {
      e.stopPropagation();
      e.preventDefault();
      if (onClick) {
        onClick(e);
      }
    }}
  >
    <div className="flex items-center justify-center w-full h-full">
      {icon}
    </div>
  </button>
);

const OptimizedMediaCard = memo(({ 
  item, 
  showGenre = true, 
  showRating = true, 
  showYear = true,
  showWatchlistButton = true,
  showPlayButton = true,
  showTrailerButton = true,
  className = '',
  onCardClick,
  onPlay,
  onTrailer,
  priority = false, // For above-the-fold content
  onClick // Added onClick prop for compatibility
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const [isInWatchlist, setIsInWatchlist] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const imgRef = useRef(null);
  const cardRef = useRef(null);

  // Performance monitoring
  useEffect(() => {
    performanceMonitor.startTimer(`mediaCard-${item.id}`);
  }, [item.id]);

  // Optimized image loading
  useEffect(() => {
    if (!imgRef.current) return;

    const img = imgRef.current;
    const posterPath = item.poster_path;
    
    if (!posterPath) {
      setImageError(true);
      setIsLoading(false);
      return;
    }

    const imageUrl = `https://image.tmdb.org/t/p/w500${posterPath}`;
    
    // For priority images (above the fold), load immediately
    if (priority) {
      img.src = imageUrl;
      img.onload = () => {
        setImageLoaded(true);
        setIsLoading(false);
        performanceMonitor.endTimer(`mediaCard-${item.id}`);
      };
      img.onerror = () => {
        setImageError(true);
        setIsLoading(false);
      };
    } else {
      // Use lazy loading for other images
      img.dataset.src = imageUrl;
      img.dataset.fallback = `https://placehold.co/500x750/000000/1f2937?text=${encodeURIComponent(item.title || item.name || 'No Title')}`;
      img.classList.add('lazy');
      
      imageLoader.observe(img);
      
      // Listen for when image is loaded by the lazy loader
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
            setImageLoaded(true);
            setIsLoading(false);
            performanceMonitor.endTimer(`mediaCard-${item.id}`);
            observer.disconnect();
          }
        });
      });
      
      observer.observe(img, { attributes: true });
    }

    return () => {
      if (!priority) {
        imageLoader.unobserve(img);
      }
    };
  }, [item.poster_path, item.title, item.name, item.id, priority]);

  // Check watchlist status
  useEffect(() => {
    if (!user || !profile || !showWatchlistButton) return;

    const checkWatchlistStatus = async () => {
      try {
        const watchlist = await watchlistStorage.getWatchlist(user.id, profile.id);
        const isInList = watchlist.some(watchlistItem => 
          watchlistItem.id === item.id && watchlistItem.media_type === item.media_type
        );
        setIsInWatchlist(isInList);
      } catch (error) {
        console.error('Error checking watchlist status:', error);
      }
    };

    checkWatchlistStatus();
  }, [user, profile, item.id, item.media_type, showWatchlistButton]);

  // Debounced hover handler for better performance
  const handleMouseEnter = useCallback(
    debounce(() => setIsHovered(true), 100),
    []
  );

  const handleMouseLeave = useCallback(
    debounce(() => setIsHovered(false), 100),
    []
  );

  // Optimized click handler
  const handleCardClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onClick) {
      onClick(item);
    } else if (onCardClick) {
      onCardClick(item);
    } else {
      const route = item.media_type === 'movie' ? 'movie' : 'show';
      navigate(`/${route}/${item.id}`);
    }
  }, [item, onCardClick, navigate, onClick]);

  // Optimized watchlist toggle
  const handleWatchlistToggle = useCallback(async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!user || !profile) return;

    try {
      if (isInWatchlist) {
        await watchlistStorage.removeFromWatchlist(user.id, profile.id, item.id, item.media_type);
        setIsInWatchlist(false);
      } else {
        await watchlistStorage.addToWatchlist(user.id, profile.id, {
          id: item.id,
          title: item.title || item.name,
          poster_path: item.poster_path,
          media_type: item.media_type,
          vote_average: item.vote_average,
          release_date: item.release_date || item.first_air_date
        });
        setIsInWatchlist(true);
      }
    } catch (error) {
      console.error('Error toggling watchlist:', error);
    }
  }, [user, profile, item, isInWatchlist]);

  // Handle play button
  const handlePlay = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onPlay) {
      onPlay(item);
    } else {
      // Default play behavior - navigate to details page
      const route = item.media_type === 'movie' ? 'movie' : 'show';
      navigate(`/${route}/${item.id}`);
    }
  }, [item, onPlay, navigate]);

  // Handle trailer button
  const handleTrailer = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onTrailer) {
      onTrailer(item);
    } else {
      // Default trailer behavior - navigate to details page
      const route = item.media_type === 'movie' ? 'movie' : 'show';
      navigate(`/${route}/${item.id}`);
    }
  }, [item, onTrailer, navigate]);

  // Get genre name
  const getGenreName = useCallback((genreId) => {
    return genreIdMap[genreId] || 'Unknown';
  }, []);

  // Get year from date
  const getYear = useCallback((dateString) => {
    if (!dateString) return '';
    return new Date(dateString).getFullYear();
  }, []);

  // Format rating
  const formatRating = useCallback((rating) => {
    if (!rating) return '';
    return rating.toFixed(1);
  }, []);

  const title = item.title || item.name || 'No Title';
  const year = showYear ? getYear(item.release_date || item.first_air_date) : '';
  const rating = showRating ? formatRating(item.vote_average) : '';
  const genre = showGenre && item.genre_ids && item.genre_ids.length > 0 
    ? getGenreName(item.genre_ids[0]) 
    : '';

  return (
    <div
      ref={cardRef}
      className={`relative group cursor-pointer transition-all duration-300 transform hover:scale-105 hover:z-10 overflow-visible w-full aspect-[2/3] ${className}`}
      style={{}}
      onMouseEnter={(e) => {
        handleMouseEnter(e);
        if (!isInWatchlist) {
          e.currentTarget.firstChild.style.boxShadow = '0 0 12px 2px rgba(139,92,246,0.55), 0 0 24px 8px rgba(139,92,246,0.25), 0 0 36px 16px rgba(139,92,246,0.12)';
        }
      }}
      onMouseLeave={(e) => {
        handleMouseLeave(e);
        if (!isInWatchlist) {
          e.currentTarget.firstChild.style.boxShadow = '';
        }
      }}
      onClick={handleCardClick}
    >
      {/* Poster with glow and rounded corners */}
      <div
        className="relative rounded-lg overflow-hidden w-full h-full"
        style={{
          boxShadow: isInWatchlist
            ? '0 0 12px 2px rgba(6,182,212,0.55), 0 0 24px 8px rgba(6,182,212,0.25), 0 0 36px 16px rgba(6,182,212,0.12)'
            : undefined,
          background: 'transparent'
        }}
      >
        <img
          ref={imgRef}
          alt={title}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          } ${imageError ? 'hidden' : ''} rounded-lg`}
          loading={priority ? 'eager' : 'lazy'}
        />
        {/* Fallback image */}
        {imageError && (
          <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center rounded-lg">
            <span className="text-gray-400 text-sm text-center px-2">
              {title}
            </span>
          </div>
        )}
        {/* Hover overlay with frosted glass buttons */}
        {isHovered && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 rounded-lg">
            <div className="flex items-center justify-center gap-4">
              {showPlayButton && (
                <QuickActionButton 
                  label="Play" 
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor" className="text-white ml-1">
                      <path d="M8 5v14l11-7z"></path>
                    </svg>
                  }
                  onClick={handlePlay}
                />
              )}
              {showWatchlistButton && user && profile && (
                <QuickActionButton 
                  label={isInWatchlist ? 'Remove from Watchlist' : 'Add to Watchlist'}
                  icon={isInWatchlist ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" fill="rgba(255,255,255,0.15)" />
                      <path d="M7 12h10" stroke="white" strokeWidth="2" strokeLinecap="round" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  )}
                  onClick={handleWatchlistToggle}
                />
              )}
              {showTrailerButton && (
                <QuickActionButton 
                  label="Trailer" 
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                      <polygon points="5,3 19,12 5,21 5,3"/>
                    </svg>
                  }
                  onClick={handleTrailer}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

OptimizedMediaCard.displayName = 'OptimizedMediaCard';

export default OptimizedMediaCard; 