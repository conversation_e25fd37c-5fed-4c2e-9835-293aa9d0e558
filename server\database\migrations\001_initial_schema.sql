-- Migration: 001_initial_schema
-- Description: Create initial database schema for Torvie
-- Created: 2025-01-08

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (enhanced from existing auth)
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Profiles table
CREATE TABLE IF NOT EXISTS profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    avatar_url VARCHAR(500),
    is_kids BOOLEAN DEFAULT FALSE,
    language VARCHAR(10) DEFAULT 'en',
    maturity_rating VARCHAR(10) DEFAULT 'all', -- 'kids', 'teen', 'mature', 'all'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, name)
);

-- Watchlist table
CREATE TABLE IF NOT EXISTS watchlist (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    media_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('movie', 'tv', 'anime')),
    title VARCHAR(500) NOT NULL,
    poster_path VARCHAR(500),
    backdrop_path VARCHAR(500),
    vote_average DECIMAL(3,1),
    vote_count INTEGER,
    release_date DATE,
    overview TEXT,
    genres JSONB,
    runtime INTEGER,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(profile_id, media_id, media_type)
);

-- Watch progress table
CREATE TABLE IF NOT EXISTS watch_progress (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    media_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('movie', 'tv', 'anime')),
    progress_seconds INTEGER NOT NULL DEFAULT 0,
    duration_seconds INTEGER,
    progress_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN duration_seconds > 0 THEN (progress_seconds::DECIMAL / duration_seconds * 100)
            ELSE 0 
        END
    ) STORED,
    completed BOOLEAN DEFAULT FALSE,
    last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    season_number INTEGER,
    episode_number INTEGER,
    episode_title VARCHAR(500),
    UNIQUE(profile_id, media_id, media_type, season_number, episode_number)
);

-- Search history table
CREATE TABLE IF NOT EXISTS search_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    query VARCHAR(500) NOT NULL,
    results_count INTEGER DEFAULT 0,
    search_type VARCHAR(20) DEFAULT 'general', -- 'general', 'movie', 'tv', 'anime', 'advanced'
    filters JSONB,
    searched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Viewing history table
CREATE TABLE IF NOT EXISTS viewing_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    media_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('movie', 'tv', 'anime')),
    title VARCHAR(500) NOT NULL,
    season_number INTEGER,
    episode_number INTEGER,
    watched_duration INTEGER DEFAULT 0, -- seconds watched
    total_duration INTEGER, -- total content duration
    watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review TEXT
);

-- User preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER REFERENCES profiles(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, profile_id, preference_key)
);

-- Content ratings/reviews table
CREATE TABLE IF NOT EXISTS content_ratings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    media_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('movie', 'tv', 'anime')),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(profile_id, media_id, media_type)
);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_user_name ON profiles(user_id, name);

CREATE INDEX IF NOT EXISTS idx_watchlist_profile ON watchlist(profile_id);
CREATE INDEX IF NOT EXISTS idx_watchlist_media ON watchlist(media_id, media_type);
CREATE INDEX IF NOT EXISTS idx_watchlist_added ON watchlist(profile_id, added_at DESC);

CREATE INDEX IF NOT EXISTS idx_watch_progress_profile ON watch_progress(profile_id);
CREATE INDEX IF NOT EXISTS idx_watch_progress_media ON watch_progress(media_id, media_type);
CREATE INDEX IF NOT EXISTS idx_watch_progress_last_watched ON watch_progress(profile_id, last_watched DESC);
CREATE INDEX IF NOT EXISTS idx_watch_progress_completed ON watch_progress(profile_id, completed);

CREATE INDEX IF NOT EXISTS idx_search_history_profile ON search_history(profile_id, searched_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_history_query ON search_history(query);

CREATE INDEX IF NOT EXISTS idx_viewing_history_profile ON viewing_history(profile_id, watched_at DESC);
CREATE INDEX IF NOT EXISTS idx_viewing_history_media ON viewing_history(media_id, media_type);

CREATE INDEX IF NOT EXISTS idx_user_preferences_user_profile ON user_preferences(user_id, profile_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_key ON user_preferences(preference_key);

CREATE INDEX IF NOT EXISTS idx_content_ratings_profile ON content_ratings(profile_id);
CREATE INDEX IF NOT EXISTS idx_content_ratings_media ON content_ratings(media_id, media_type);
CREATE INDEX IF NOT EXISTS idx_content_ratings_public ON content_ratings(is_public) WHERE is_public = TRUE;

-- Updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_ratings_updated_at BEFORE UPDATE ON content_ratings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
