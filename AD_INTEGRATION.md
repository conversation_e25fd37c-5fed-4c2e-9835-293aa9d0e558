# Torvie Ad Integration with Google IMA SDK

## 🎯 Overview

Torvie now includes **Google IMA SDK integration** for video advertising, using Google's public VAST test tags while awaiting Google Ad Manager (GAM) approval. This provides a complete ad testing environment that can be easily switched to production ads later.

## 🚀 Features

### ✅ **Current Implementation**
- **Pre-roll ads** - Ads play before video content
- **Google IMA SDK** - Industry standard ad framework  
- **Test ad integration** - Using Google's public VAST test tags
- **Abstracted configuration** - Easy to switch to production ads
- **Error handling** - Graceful fallback if ads fail
- **Debug tools** - Built-in testing and debugging controls

### 🔧 **Ready for Production**
- **GAM integration ready** - Just swap the ad tag URLs
- **Mid-roll support** - Can be enabled when needed
- **Post-roll support** - Can be enabled when needed
- **VPAID support** - Interactive ads ready
- **Companion ads** - Banner ads alongside video

## 🎮 **Testing the Integration**

### **Test Controls (Ctrl+Shift+A)**
While watching a video, press `Ctrl+Shift+A` to open the ad test controls:
- Test different ad formats (Linear, Skippable, VMAP)
- Toggle between test/production mode
- Enable/disable ad types
- View current configuration

### **Browser Console Logs**
Check the browser console for detailed ad logs:
```
🎬 Initializing Google IMA SDK for ads...
🎯 Torvie Ad Integration Status:
   - Mode: Test Ads (Google VAST)
   - Pre-roll: Enabled
   - Test Controls: Press Ctrl+Shift+A
   - Current Ad Tag: https://pubads.g.doubleclick.net/...
```

### **Visual Indicators**
- **"Ad Playing..."** indicator during ads
- **"IMA Ads Ready"** indicator when integration is active
- **Console logs** for all ad events and errors

## 📁 **File Structure**

```
client/src/
├── utils/adConfig.js          # Ad configuration and URLs
├── components/
│   ├── VideoPlayer.jsx        # Main player with IMA integration
│   └── AdTestControls.jsx     # Debug/testing controls
└── index.html                 # IMA SDK script inclusion
```

## ⚙️ **Configuration**

### **Ad Tags (Test Mode)**
Currently using Google's public VAST test tags:
- **Linear (10s):** Basic pre-roll ad
- **Skippable (30s):** Skippable after 5 seconds  
- **VMAP:** Pre/Mid/Post-roll sequence

### **Switching to Production**
When GAM is approved, update `client/src/utils/adConfig.js`:

```javascript
// Add your real GAM tags here:
AD_TAGS: {
  PRODUCTION_PREROLL: 'https://securepubads.g.doubleclick.net/gampad/ads?...',
  PRODUCTION_MIDROLL: 'https://securepubads.g.doubleclick.net/gampad/ads?...',
}

// Then switch mode:
SETTINGS: {
  USE_TEST_ADS: false, // Set to false for production
}
```

### **Ad Slot Dimensions**
Currently configured for:
- **Linear ads:** 640x360 (matches video player)
- **Non-linear ads:** 640x150 (overlay ads)

## 🔍 **Google Video Suite Inspector**

Test your ad tags using Google's tool:
1. Go to: https://googleads.github.io/googleads-ima-html5/vsi/
2. Paste any ad tag URL from the console logs
3. Click "Test Ad" to verify the ad response
4. Check for VAST compliance and errors

## 🎬 **How It Works**

### **Ad Flow**
1. **Video loads** → IMA SDK initializes
2. **Stream ready** → Request pre-roll ads  
3. **Ad loads** → Pause content, show ad
4. **Ad completes** → Resume content playback
5. **Error handling** → Skip to content if ads fail

### **Event Handling**
- `CONTENT_PAUSE_REQUESTED` → Pause video for ads
- `CONTENT_RESUME_REQUESTED` → Resume video after ads
- `AD_ERROR` → Skip ads, continue with content
- `LOADED` → Handle linear vs non-linear ads

## 🔧 **Development Tips**

### **Testing Different Ad Types**
```javascript
// Test skippable ads
AD_CONFIG.getAdTag('PREROLL_SKIPPABLE')

// Test VMAP (multiple ad breaks)  
AD_CONFIG.getAdTag('MIDROLL')

// Enable mid-roll ads
AD_CONFIG.SETTINGS.ENABLE_MIDROLL = true
```

### **Debugging**
- Check browser console for all ad events
- Use `Ctrl+Shift+A` to access test controls
- Test with ad blocker disabled
- Verify HTTPS compatibility

### **Common Issues**
- **No ads showing:** Check console for errors, verify IMA SDK loaded
- **Ad blocker:** Disable ad blockers during testing
- **HTTPS mixed content:** Ensure ad tags use HTTPS
- **Mobile autoplay:** Ads require user interaction on mobile

## 🎯 **Next Steps**

### **When GAM is Approved:**
1. ✅ Replace test ad tags with real GAM tags
2. ✅ Set `USE_TEST_ADS: false` in config
3. ✅ Enable mid-roll/post-roll ads as needed
4. ✅ Set up companion ads if required
5. ✅ Configure ad scheduling and frequency

### **Potential Enhancements:**
- **Ad analytics** - Track ad performance
- **User preferences** - Allow ad-free premium tiers
- **Contextual targeting** - Match ads to content
- **A/B testing** - Test different ad formats
- **Revenue optimization** - Header bidding integration

---

## 🚀 **Ready to Test!**

Your ad integration is complete and ready for testing. The system will:
- ✅ **Play test ads** using Google's VAST tags
- ✅ **Handle errors gracefully** with content fallback
- ✅ **Switch to production** easily when GAM is approved
- ✅ **Provide debugging tools** for development and testing

**Test it now:** Play any video in Torvie and watch for the pre-roll ad!

Press `Ctrl+Shift+A` during video playback to access advanced testing controls. 