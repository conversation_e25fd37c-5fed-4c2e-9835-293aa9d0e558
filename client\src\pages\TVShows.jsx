import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import OptimizedMediaCard from '../components/OptimizedMediaCard';
import EnhancedVideoPlayer from '../components/EnhancedVideoPlayer';
import TrailerPlayer from '../components/TrailerPlayer';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import { watchlistStorage } from '../utils/watchlistStorage';
import { useMultipleHorizontalScroll } from '../hooks/useHorizontalScroll';
import { apiFetch } from '../utils/api';
import HorizontalRow from '../components/HorizontalRow';
import LoadingSpinner from '../components/LoadingSpinner';

// IMPORTANT: This page uses TMDB data for all display elements (posters, titles, info)
// Torrent data is ONLY used for the play button functionality
// - TMDB: Posters, titles, cast, info, trailers
// - Torrents: Only for the left play button to stream content

const FeaturedHero = ({ show, onWatchNow, isPlayingTrailer, trailer, _onStopTrailer }) => {
    if (!show) return <div className="w-full h-[60vh] bg-black animate-pulse rounded-lg"></div>;
    const backdropUrl = `https://image.tmdb.org/t/p/original${show.backdrop_path}`;
    
    return (
        <div className="w-full flex justify-center py-8">
            <div className="relative max-w-4xl aspect-video w-full rounded-lg overflow-hidden shadow-2xl bg-black">
                {isPlayingTrailer ? (
                    <div className="absolute inset-0 w-full h-full">
                        <iframe
                            src={`https://www.youtube.com/embed/${trailer.key}?autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&iv_load_policy=3&fs=0&vq=hd1080`}
                            title={`${show.name} Trailer`}
                            className="absolute inset-0 w-full h-full"
                            frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                        ></iframe>
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent pointer-events-none"></div>
                    </div>
                ) : (
                    <div className="absolute inset-0 w-full h-full">
                        <div
                            className="w-full h-full bg-cover bg-center"
                            style={{ backgroundImage: `url(${backdropUrl})` }}
                        ></div>
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
                    </div>
                )}
                <div className="relative z-10 p-8 max-w-2xl">
                    <h1 className="text-5xl font-black tracking-tighter">{show.name}</h1>
                    <p className="mt-2 text-gray-300">{show.overview}</p>
                    <div className="flex gap-4 mt-4">
                        <button 
                            className="w-16 h-16 bg-white/10 backdrop-blur-sm text-white rounded-full flex items-center justify-center transform hover:scale-110 hover:bg-white/20 transition-all shadow-lg"
                            onClick={() => onWatchNow && onWatchNow(show)}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="ml-1">
                                <path d="M8 5v14l11-7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

const TVShows = () => {
  const { profile: currentProfile, isProfileLoaded } = useProfile();
  const { user } = useAuth();
  const [trendingShows, setTrendingShows] = useState([]);
  const [heroTrailer, setHeroTrailer] = useState({ isPlaying: false, show: null, trailer: null });
  const [hasPlayedHeroTrailer, setHasPlayedHeroTrailer] = useState(false);
  const [topRatedShows, setTopRatedShows] = useState([]);
  const [topRatedPage, setTopRatedPage] = useState(1);
  const [topRatedHasMore, setTopRatedHasMore] = useState(true);
  const [topRatedLoading, setTopRatedLoading] = useState(false);
  const [genres, setGenres] = useState([]);
  const [genreShows, setGenreShows] = useState({}); // { genreId: [shows] }
  const [loadingGenres, setLoadingGenres] = useState(true);
  const [loadingTrending, setLoadingTrending] = useState(true);
  const [trendingError, setTrendingError] = useState(null);
  const [loadingRows, setLoadingRows] = useState({}); // { genreId: true/false }
  const [genrePages, setGenrePages] = useState({}); // { genreId: page }
  const [genreHasMore, setGenreHasMore] = useState({}); // { genreId: hasMore }
  const [genreLoading, setGenreLoading] = useState({}); // { genreId: loading }
  const [watchlist, setWatchlist] = useState([]);
  const [videoPlayer, setVideoPlayer] = useState({ isOpen: false, magnetLink: null, title: null, contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, show: null, trailer: null });
  const navigate = useNavigate();
  const location = useLocation();
  const genreRowRefs = useRef({});
  const lastCardRefs = useRef({});
  
  // Enable horizontal scrolling with mouse wheel for all rows
  const { setScrollRef } = useMultipleHorizontalScroll([genres.length, topRatedShows.length]);

  // Scroll position restoration
  useEffect(() => {
    // Scroll to top on initial load
    window.scrollTo(0, 0);
    
    // Restore scroll position when returning to the page
    const savedScrollPosition = sessionStorage.getItem('tvshows_scroll_position');
    if (savedScrollPosition && !loadingGenres) {
      setTimeout(() => {
        window.scrollTo(0, parseInt(savedScrollPosition));
      }, 100);
    }

    // Save scroll position when navigating away
    const handleBeforeUnload = () => {
      sessionStorage.setItem('tvshows_scroll_position', window.scrollY.toString());
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        sessionStorage.setItem('tvshows_scroll_position', window.scrollY.toString());
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [loadingGenres]);

  // Fetch trending TV shows for hero section
  useEffect(() => {
    const fetchTrendingShows = async () => {
      setLoadingTrending(true);
      setTrendingError(null);
      
      try {
        console.log('🏠 TVShows: Fetching trending TV shows from TMDB API...');
        const response = await apiFetch('/api/trending/tv?page=1');
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        const showsArray = data.results && Array.isArray(data.results) ? data.results : data;
        
        if (Array.isArray(showsArray) && showsArray.length > 0) {
          setTrendingShows(showsArray);
          console.log(`🏠 TVShows: Successfully loaded ${showsArray.length} trending TV shows`);
        } else {
          console.warn('🏠 TVShows: No trending TV shows found in API response');
          setTrendingShows([]);
        }
      } catch (error) {
        console.error('🏠 TVShows: Error fetching trending TV shows:', error);
        setTrendingError(error.message);
        setTrendingShows([]);
      } finally {
        setLoadingTrending(false);
      }
    };
    
    fetchTrendingShows();
  }, []);

  // Auto-play trailer for featured show
  useEffect(() => {
    if (trendingShows.length > 0 && !loadingTrending && !heroTrailer.isPlaying && !hasPlayedHeroTrailer) {
      // Check if user is returning to the page (has saved scroll position)
      const savedScrollPosition = sessionStorage.getItem('tvshows_scroll_position');
      const isReturningUser = savedScrollPosition && parseInt(savedScrollPosition) > 0;
      
      // Only auto-play if this is a fresh page load (not returning from details)
      if (!isReturningUser) {
        const featuredShow = trendingShows[0];
        setTimeout(() => {
          handleHeroTrailer(featuredShow);
          setHasPlayedHeroTrailer(true);
        }, 1000);
      } else {
        // Mark as played so it doesn't auto-play when returning
        setHasPlayedHeroTrailer(true);
      }
    }
  }, [trendingShows, loadingTrending, hasPlayedHeroTrailer]);

  // Handle scroll to stop trailer
  useEffect(() => {
    const handleScroll = () => {
      if (heroTrailer.isPlaying && window.scrollY > 100) {
        stopHeroTrailer();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [heroTrailer.isPlaying]);

  const handleHeroTrailer = async (show) => {
    try {
      console.log('Fetching trailer for:', show.name);
      // Fetch trailer data for the show
      const response = await apiFetch(`/api/tv/${show.id}/videos`);
      const data = await response.json();
      
      // Find the best trailer - prioritize official cinematic trailers
      const trailers = data.results?.filter(video => 
        video.site === 'YouTube' && 
        (video.type === 'Trailer' || video.type === 'Teaser') &&
        // Filter out portrait/vertical videos (like YouTube Shorts)
        (!video.name?.toLowerCase().includes('short') && 
         !video.name?.toLowerCase().includes('vertical') &&
         !video.name?.toLowerCase().includes('portrait'))
      ) || [];
      
      console.log('Found trailers:', trailers.length);
      
      // Sort by priority: Official Trailer first, then Teaser, then others
      const sortedTrailers = trailers.sort((a, b) => {
        const aPriority = a.type === 'Trailer' ? 1 : 2;
        const bPriority = b.type === 'Trailer' ? 1 : 2;
        
        // If same type, prefer "Official" in the name
        if (aPriority === bPriority) {
          const aIsOfficial = a.name?.toLowerCase().includes('official');
          const bIsOfficial = b.name?.toLowerCase().includes('official');
          if (aIsOfficial && !bIsOfficial) return -1;
          if (!aIsOfficial && bIsOfficial) return 1;
        }
        
        return aPriority - bPriority;
      });
      
      const trailer = sortedTrailers[0];
      
      if (trailer) {
        console.log('Playing trailer:', trailer.name);
        setHeroTrailer({ isPlaying: true, show, trailer });
      } else {
        console.log('No trailer available for:', show.name);
      }
    } catch (error) {
      console.error('Error fetching trailer:', error);
    }
  };

  const stopHeroTrailer = () => {
    setHeroTrailer({ isPlaying: false, show: null, trailer: null });
  };

  useEffect(() => {
    const handleStorage = (e) => {
      if (e.key && e.key.startsWith('torvie_watchlist_') && user && currentProfile && e.key === `torvie_watchlist_${user.id}_${currentProfile.id}`) {
        setWatchlist(JSON.parse(e.newValue || '[]'));
      }
    };
    window.addEventListener('storage', handleStorage);
    return () => window.removeEventListener('storage', handleStorage);
  }, [user, currentProfile]);

  // Update watchlist when profile changes
  useEffect(() => {
    // Don't load watchlist until profile is loaded to avoid clearing on initial render
    if (!isProfileLoaded) {
      console.log('🏠 TVShows: Waiting for profile to load...');
      return;
    }
    
    const loadWatchlist = async () => {
      console.log('🏠 TVShows: Loading watchlist data from standalone app storage');
      console.log('👤 TVShows: Current user:', user);
      console.log('👤 TVShows: Current profile:', currentProfile);
      
      if (user && currentProfile && currentProfile.id) {
        const stored = await watchlistStorage.getWatchlist(user.id, currentProfile.id);
        console.log(`✅ TVShows: Loaded ${stored.length} watchlist items from local app storage`);
        setWatchlist(stored);
      } else {
        console.log(`👤 TVShows: No user or profile, clearing watchlist`);
        setWatchlist([]);
      }
    };
    
    loadWatchlist();
  }, [user, currentProfile, isProfileLoaded]);

  const isInWatchlist = (id) => watchlist.some(item => String(item.id) === String(id));

  const toggleWatchlist = async (show) => {
    if (!user || !currentProfile || !currentProfile.id) {
      console.log('❌ TVShows: No user or current profile for toggleWatchlist');
      return;
    }
    
    console.log(`🏠 TVShows: Toggling watchlist for show "${show.name || show.title}"`);
    
    const exists = watchlist.some(item => String(item.id) === String(show.id));
    let success;
    
    if (exists) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, show.id, show.media_type || 'tv');
      if (success) {
        setWatchlist(prev => prev.filter(item => String(item.id) !== String(show.id)));
        console.log(`➖ TVShows: Removed "${show.name || show.title}" from watchlist`);
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, { ...show, media_type: show.media_type || 'tv' });
      if (success) {
        setWatchlist(prev => [...prev, show]);
        console.log(`➕ TVShows: Added "${show.name || show.title}" to watchlist`);
      }
    }
    
    if (!success) {
      console.error(`❌ TVShows: Failed to toggle watchlist for "${show.name || show.title}"`);
    }
  };

  const handlePlayShow = async (show) => {
    if (!show) {
      return;
    }

    try {
      console.log(`🎬 Searching torrents for TV show: ${show.name}`);
      const response = await apiFetch('/api/search?query=' + encodeURIComponent(show.name) + '&type=tv');
      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        console.log(`🎬 Found ${data.results.length} torrents for ${show.name}`);
        
        // Try torrents in order of seeders (highest first)
        const sortedTorrents = data.results.sort((a, b) => (b.seeders || 0) - (a.seeders || 0));
        
        for (let i = 0; i < Math.min(3, sortedTorrents.length); i++) {
          const torrent = sortedTorrents[i];
          console.log(`🎬 Trying torrent ${i + 1}: ${torrent.title} (${torrent.seeders} seeders)`);
          
          if (!torrent.magnetLink) {
            console.warn(`🎬 Skipping torrent ${i + 1}: No magnet link`);
            continue;
          }
          
          // Try this torrent
          setVideoPlayer({ 
            isOpen: true, 
            magnetLink: torrent.magnetLink, 
            title: show.name,
            contentId: `tv_${show.id}`,
            contentType: 'tv',
            posterUrl: show.poster_path ? `https://image.tmdb.org/t/p/w500${show.poster_path}` : null
          });
          return; // Exit after trying the first valid torrent
        }
        
        console.error(`🎬 No valid torrents found for ${show.name}`);
      } else {
        console.log(`🎬 No torrents found for: ${show.name}`);
      }
    } catch (error) {
      console.error('🎬 Error searching for torrents:', error);
    }
  };

  const handleTrailer = async (show) => {
    if (!show) {
      return;
    }

    try {
      const response = await apiFetch(`/api/tv/${show.id}/videos`);
      const data = await response.json();
      
      const trailers = data.results?.filter(video => 
        video.type === 'Trailer' && 
        video.site === 'YouTube' &&
        (video.name?.toLowerCase().includes('trailer') || video.name?.toLowerCase().includes('official'))
      ) || [];
      
      if (trailers.length > 0) {
        const trailer = trailers[0];
        setTrailerModal({ isOpen: true, show, trailer });
      }
    } catch (error) {
      console.error('Error fetching trailer:', error);
    }
  };

  const closeVideoPlayer = () => {
    setVideoPlayer({ isOpen: false, magnetLink: null, title: null, contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  };

  const closeTrailerModal = () => {
    setTrailerModal({ isOpen: false, show: null, trailer: null });
  };

  const handleShowClick = (show) => {
    // Save current scroll position before navigating
    sessionStorage.setItem('tvshows_scroll_position', window.scrollY.toString());
    navigate(`/show/${show.id}`);
  };

  // Fetch genres and initial shows
  useEffect(() => {
    const fetchGenresAndShows = async () => {
      try {
        const response = await apiFetch('/api/genres?type=tv');
        const data = await response.json();
        if (Array.isArray(data.genres)) {
          setGenres(data.genres);
          
          // Set loading state for all genres
          const loadingObj = data.genres.reduce((acc, genre) => {
            acc[genre.id] = true;
            return acc;
          }, {});
          setLoadingRows(loadingObj);
          
          // Combine all shows from pages 1-3 for each genre
          const promises = data.genres.map(async (genre) => {
            try {
              const pages = await Promise.all([
                apiFetch(`/api/media/genre-tvshows/${genre.id}?page=1`).then(r => r.json()),
                apiFetch(`/api/media/genre-tvshows/${genre.id}?page=2`).then(r => r.json()),
                apiFetch(`/api/media/genre-tvshows/${genre.id}?page=3`).then(r => r.json())
              ]);
              
              // Combine and deduplicate shows
              const allShows = pages.flatMap(page => page.shows || []);
              const uniqueShows = [];
              const seenIds = new Set();
              
              for (const show of allShows) {
                if (!seenIds.has(show.id)) {
                  seenIds.add(show.id);
                  uniqueShows.push(show);
                }
              }
              
              return { genreId: genre.id, shows: uniqueShows };
            } catch (error) {
              console.error(`Error loading shows for genre ${genre.id}:`, error);
              return { genreId: genre.id, shows: [] };
            }
          });
          
          const genreShowsData = await Promise.all(promises);
          const genreShowsObj = genreShowsData.reduce((acc, { genreId, shows }) => {
            acc[genreId] = shows;
            return acc;
          }, {});
          setGenreShows(genreShowsObj);
          
          // Set hasMore state for each genre
          const hasMoreObj = genreShowsData.reduce((acc, { genreId, shows }) => {
            acc[genreId] = shows.length > 0;
            return acc;
          }, {});
          setGenreHasMore(hasMoreObj);
          
          // Clear loading state for all genres
          setLoadingRows({});
        } else {
          setGenres([]);
        }
      } catch {
        setGenres([]);
      } finally {
        setLoadingGenres(false);
      }
    };
    fetchGenresAndShows();
  }, []);

  // Fetch initial top rated shows
  useEffect(() => {
    const fetchTopRated = async () => {
      setTopRatedLoading(true);
      try {
        const response = await apiFetch('/api/top-rated/tv?page=1');
        if (response.ok) {
          const data = await response.json();
          const shows = data.results || [];
          setTopRatedShows(shows);
          setTopRatedHasMore(shows.length >= 20);
        }
      } catch (error) {
        console.error('Error fetching top rated TV shows:', error);
      } finally {
        setTopRatedLoading(false);
      }
    };
    fetchTopRated();
  }, []);

  // Load more top rated shows
  const loadMoreTopRated = async () => {
    if (topRatedLoading || !topRatedHasMore) return;
    setTopRatedLoading(true);
    try {
      const nextPage = topRatedPage + 1;
      const response = await apiFetch('/api/top-rated/tv?page=' + nextPage);
      if (response.ok) {
        const data = await response.json();
        const newShows = data.results || [];
        setTopRatedShows(prev => [...prev, ...newShows]);
        setTopRatedPage(nextPage);
        setTopRatedHasMore(newShows.length >= 20);
      }
    } catch (error) {
      console.error('Error loading more top rated TV shows:', error);
    } finally {
      setTopRatedLoading(false);
    }
  };

  // Fetch more shows for a genre
  const loadMoreGenreShows = async (genreId) => {
    if (genreLoading[genreId] || genreHasMore[genreId] === false) {
      return;
    }
    setGenreLoading(prev => ({ ...prev, [genreId]: true }));
    const nextPage = (genrePages[genreId] || 3) + 1; // Start from page 4 since we load 1-3 initially
    try {
      const res = await apiFetch(`/api/media/genre-tvshows/${genreId}?page=${nextPage}`);
      const data = await res.json();
      const apiShows = data.shows || [];
      const existingIds = new Set((genreShows[genreId] || []).map(m => m.id));
      const newUnique = apiShows.filter(m => !existingIds.has(m.id));
      setGenreShows(prev => ({
        ...prev,
        [genreId]: [...(prev[genreId] || []), ...newUnique]
      }));
      setGenrePages(prev => ({ ...prev, [genreId]: nextPage }));
      setGenreHasMore(prev => ({ ...prev, [genreId]: newUnique.length > 0 }));
    } catch (error) {
      console.error(`Error loading more shows for genre ${genreId}:`, error);
    } finally {
      setGenreLoading(prev => ({ ...prev, [genreId]: false }));
    }
  };

  // Infinite scroll for each row
  const setLastCardRef = (genreId) => (node) => {
    if (lastCardRefs.current[genreId]) {
      lastCardRefs.current[genreId].disconnect();
    }
    if (node) {
      lastCardRefs.current[genreId] = new window.IntersectionObserver(entries => {
        if (entries[0].isIntersecting && !genreLoading[genreId] && genreHasMore[genreId] !== false) {
          loadMoreGenreShows(genreId);
        }
      }, { 
        root: genreRowRefs.current[genreId], 
        threshold: 0.1, 
        rootMargin: '200px' 
      });
      lastCardRefs.current[genreId].observe(node);
    }
  };

  if (loadingGenres) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400 text-xl">Loading TV Shows...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen px-6 py-8">
      <div className="max-w-7xl mx-auto">
        {/* Featured Show Hero Section */}
        {!loadingTrending && !trendingError && trendingShows.length > 0 && (
          <div className="mb-8">
            <FeaturedHero 
              show={trendingShows[0]} 
              onWatchNow={handlePlayShow}
              isPlayingTrailer={heroTrailer.isPlaying} 
              trailer={heroTrailer.trailer} 
              _onStopTrailer={stopHeroTrailer} 
            />
          </div>
        )}

        {loadingTrending && (
          <div className="w-full h-[60vh] bg-black animate-pulse rounded-lg mb-8"></div>
        )}

        {trendingError && (
          <div className="text-center py-16 mb-8">
            <p className="text-red-400 mb-4">Error loading trending shows: {trendingError}</p>
            <button 
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        )}
      </div>

      {/* Netflix-style Genre Rows - Full Width */}
      <div className="space-y-8">
        {/* Trending Shows Row */}
        {!loadingTrending && trendingShows.length > 0 && (
          <HorizontalRow
            items={trendingShows}
            renderItem={(show, idx) => (
              <OptimizedMediaCard
                item={show}
                isInWatchlist={isInWatchlist(show.id)}
                toggleWatchlist={() => toggleWatchlist(show)}
                onClick={() => handleShowClick(show)}
                onPlay={() => handlePlayShow(show)}
                onTrailer={() => handleTrailer(show)}
                className="media-card"
              />
            )}
            loadMore={null} // If you want infinite scroll for trending, implement loadMoreTrending
            loading={loadingTrending}
            title="Trending Shows"
            rowId="trending-shows-row"
          />
        )}

        {/* Top Rated Shows Row */}
        <HorizontalRow
          items={topRatedShows}
          renderItem={(show, idx) => (
            <OptimizedMediaCard
              item={show}
              isInWatchlist={isInWatchlist(show.id)}
              toggleWatchlist={() => toggleWatchlist(show)}
              onClick={() => handleShowClick(show)}
              onPlay={() => handlePlayShow(show)}
              onTrailer={() => handleTrailer(show)}
              className="media-card"
            />
          )}
          loadMore={loadMoreTopRated}
          loading={topRatedLoading}
          title="Top Rated"
          rowId="top-rated-row"
        />

        {/* Genre Rows */}
        {genres.map((genre) => (
          <HorizontalRow
            key={genre.id}
            items={genreShows[genre.id] || []}
            renderItem={(show, idx) => (
              <OptimizedMediaCard
                item={show}
                isInWatchlist={isInWatchlist(show.id)}
                toggleWatchlist={() => toggleWatchlist(show)}
                onClick={() => handleShowClick(show)}
                onPlay={() => handlePlayShow(show)}
                onTrailer={() => handleTrailer(show)}
                className="media-card"
              />
            )}
            loadMore={() => loadMoreGenreShows(genre.id)}
            loading={genreLoading[genre.id]}
            title={genre.name}
            rowId={`genre-row-${genre.id}`}
          />
        ))}
      </div>
      
      {/* Video Player */}
      {videoPlayer.isOpen && (
        <EnhancedVideoPlayer
          magnetLink={videoPlayer.magnetLink}
          title={videoPlayer.title}
          onClose={closeVideoPlayer}
          contentId={videoPlayer.contentId}
          contentType={videoPlayer.contentType}
          posterUrl={videoPlayer.posterUrl}
          episodeInfo={videoPlayer.episodeInfo}
        />
      )}

      {/* Trailer Modal */}
      {trailerModal.isOpen && trailerModal.show && trailerModal.trailer && (
        <div 
          className="fixed inset-0 z-40 cursor-pointer"
          onClick={closeTrailerModal}
        >
          {/* Glass overlay that covers ENTIRE viewport */}
          <div 
            className="fixed backdrop-blur-xl bg-black/70" 
            style={{ 
              top: 0,
              left: 0, 
              right: 0, 
              bottom: 0,
              width: '100vw',
              height: '100vh'
            }}
          />
          {/* Header spacer to keep header visible and clickable */}
          <div 
            className="fixed bg-transparent z-[60]" 
            style={{ 
              top: 0,
              left: 0, 
              right: 0, 
              height: '80px',
              width: '100vw'
            }}
          />
          {/* Trailer container positioned below header */}
          <div 
            className="fixed flex items-center justify-center z-[51]" 
            style={{ 
              top: '80px',
              left: 0,
              right: 0,
              bottom: 0,
              width: '100vw',
              height: 'calc(100vh - 80px)'
            }}
          >
            <div className="w-[80vw] h-[70vh] max-w-5xl flex flex-col rounded-xl overflow-hidden shadow-2xl bg-black relative" onClick={(e) => e.stopPropagation()}>
              {/* Close button in top right corner */}
              <button
                onClick={closeTrailerModal}
                className="absolute top-4 right-4 z-10 p-2 text-white hover:bg-black/50 rounded-full transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              {/* Trailer Video - Full container */}
              <div className="w-full h-full bg-black">
                <TrailerPlayer videoId={trailerModal.trailer.key} title={trailerModal.show.name} posterUrl={trailerModal.show.poster_path ? `https://image.tmdb.org/t/p/w500${trailerModal.show.poster_path}` : undefined} startMuted={false} />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TVShows; 