-- Migration: 002_add_analytics_tables
-- Description: Add analytics and performance tracking tables
-- Created: 2025-01-08

-- User sessions table for analytics
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER REFERENCES profiles(id) ON DELETE SET NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50), -- 'desktop', 'mobile', 'tablet', 'tv'
    browser VARCHAR(100),
    os VARCHAR(100),
    country VARCHAR(2), -- ISO country code
    city VARCHAR(100),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP,
    duration_seconds INTEGER GENERATED ALWAYS AS (
        CASE 
            WHEN ended_at IS NOT NULL THEN EXTRACT(EPOCH FROM (ended_at - started_at))::INTEGER
            ELSE EXTRACT(EPOCH FROM (last_activity - started_at))::INTEGER
        END
    ) STORED
);

-- Content interaction tracking
CREATE TABLE IF NOT EXISTS content_interactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    session_id INTEGER REFERENCES user_sessions(id) ON DELETE SET NULL,
    media_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('movie', 'tv', 'anime')),
    interaction_type VARCHAR(50) NOT NULL, -- 'view', 'play', 'pause', 'stop', 'seek', 'add_watchlist', 'remove_watchlist', 'rate'
    interaction_data JSONB, -- Additional data like seek position, rating value, etc.
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    metric_unit VARCHAR(20), -- 'ms', 'seconds', 'bytes', 'count', etc.
    tags JSONB, -- Additional metadata like endpoint, user_id, etc.
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Error logs table
CREATE TABLE IF NOT EXISTS error_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    session_id INTEGER REFERENCES user_sessions(id) ON DELETE SET NULL,
    error_type VARCHAR(100) NOT NULL, -- 'api_error', 'client_error', 'database_error', etc.
    error_message TEXT NOT NULL,
    error_stack TEXT,
    request_url VARCHAR(500),
    request_method VARCHAR(10),
    request_headers JSONB,
    request_body JSONB,
    response_status INTEGER,
    user_agent TEXT,
    ip_address INET,
    occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Content popularity tracking
CREATE TABLE IF NOT EXISTS content_popularity (
    id SERIAL PRIMARY KEY,
    media_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('movie', 'tv', 'anime')),
    view_count INTEGER DEFAULT 0,
    unique_viewers INTEGER DEFAULT 0,
    total_watch_time INTEGER DEFAULT 0, -- in seconds
    average_rating DECIMAL(3,2),
    rating_count INTEGER DEFAULT 0,
    trending_score DECIMAL(10,4) DEFAULT 0,
    last_viewed TIMESTAMP,
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(media_id, media_type)
);

-- System health metrics
CREATE TABLE IF NOT EXISTS system_health (
    id SERIAL PRIMARY KEY,
    component VARCHAR(100) NOT NULL, -- 'database', 'api', 'cache', 'storage', etc.
    status VARCHAR(20) NOT NULL, -- 'healthy', 'degraded', 'down'
    response_time_ms INTEGER,
    error_rate DECIMAL(5,2), -- percentage
    cpu_usage DECIMAL(5,2), -- percentage
    memory_usage DECIMAL(5,2), -- percentage
    disk_usage DECIMAL(5,2), -- percentage
    additional_metrics JSONB,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for analytics tables
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_profile_id ON user_sessions(profile_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_started_at ON user_sessions(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(last_activity DESC) WHERE ended_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_content_interactions_user_profile ON content_interactions(user_id, profile_id);
CREATE INDEX IF NOT EXISTS idx_content_interactions_media ON content_interactions(media_id, media_type);
CREATE INDEX IF NOT EXISTS idx_content_interactions_type ON content_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_content_interactions_timestamp ON content_interactions(timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_recorded_at ON performance_metrics(recorded_at DESC);

CREATE INDEX IF NOT EXISTS idx_error_logs_type ON error_logs(error_type);
CREATE INDEX IF NOT EXISTS idx_error_logs_occurred_at ON error_logs(occurred_at DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON error_logs(user_id) WHERE user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_content_popularity_media ON content_popularity(media_id, media_type);
CREATE INDEX IF NOT EXISTS idx_content_popularity_trending ON content_popularity(trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_content_popularity_views ON content_popularity(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_content_popularity_rating ON content_popularity(average_rating DESC) WHERE rating_count > 0;

CREATE INDEX IF NOT EXISTS idx_system_health_component ON system_health(component);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON system_health(status);
CREATE INDEX IF NOT EXISTS idx_system_health_checked_at ON system_health(checked_at DESC);

-- Trigger to update content popularity
CREATE OR REPLACE FUNCTION update_content_popularity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update popularity metrics when viewing history is added
    IF TG_OP = 'INSERT' AND TG_TABLE_NAME = 'viewing_history' THEN
        INSERT INTO content_popularity (media_id, media_type, view_count, unique_viewers, total_watch_time, last_viewed)
        VALUES (NEW.media_id, NEW.media_type, 1, 1, COALESCE(NEW.watched_duration, 0), NEW.watched_at)
        ON CONFLICT (media_id, media_type) DO UPDATE SET
            view_count = content_popularity.view_count + 1,
            total_watch_time = content_popularity.total_watch_time + COALESCE(NEW.watched_duration, 0),
            last_viewed = NEW.watched_at,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    -- Update popularity metrics when ratings are added/updated
    IF TG_TABLE_NAME = 'content_ratings' THEN
        INSERT INTO content_popularity (media_id, media_type, rating_count, average_rating)
        VALUES (NEW.media_id, NEW.media_type, 1, NEW.rating)
        ON CONFLICT (media_id, media_type) DO UPDATE SET
            rating_count = (
                SELECT COUNT(*) FROM content_ratings 
                WHERE media_id = NEW.media_id AND media_type = NEW.media_type
            ),
            average_rating = (
                SELECT AVG(rating) FROM content_ratings 
                WHERE media_id = NEW.media_id AND media_type = NEW.media_type
            ),
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply popularity triggers
CREATE TRIGGER update_popularity_on_view AFTER INSERT ON viewing_history
    FOR EACH ROW EXECUTE FUNCTION update_content_popularity();

CREATE TRIGGER update_popularity_on_rating AFTER INSERT OR UPDATE ON content_ratings
    FOR EACH ROW EXECUTE FUNCTION update_content_popularity();

-- Apply updated_at trigger to content_popularity
CREATE TRIGGER update_content_popularity_updated_at BEFORE UPDATE ON content_popularity
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
