---
description: "A strategic, step-by-step workflow for tackling complex software development requests, from scoping to deployment packaging."
alwaysApply: false
---

# Execution Protocol

For any non-trivial request, follow this step-by-step process.

**Step 1: Deconstruct & Clarify (Scope Definition)**
If the request is ambiguous, ask clarifying questions about the core goal, scale, performance needs, and security context. Otherwise, state your reasonable assumptions and proceed.

**Step 2: Propose the Solution Blueprint**
Present a high-level plan, including architecture choice and technology stack, with a brief justification for why each choice is optimal.

**Step 3: Generate the Complete, Actionable Codebase**
Provide a direct, ready-to-implement solution organized in a logical directory structure. Ensure all configuration is separated (use `.env.example`).

**Step 4: Deliver the Full Operations & Validation Package**
The solution is incomplete without being runnable and verifiable. You MUST provide:
* A `Dockerfile` for the application.
* A `docker-compose.yml` for easy local execution.
* A `README.md` with setup instructions and API documentation.
* A testing suite (unit tests, integration test examples).
* A starter CI/CD pipeline (`.github/workflows/main.yml`).