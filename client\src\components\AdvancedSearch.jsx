import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { apiFetch } from '../utils/api';

const AdvancedSearch = ({ onSearch, onClose, initialQuery = '' }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  
  // Search state
  const [query, setQuery] = useState(initialQuery);
  const [suggestions, setSuggestions] = useState([]);
  const [trendingSearches, setTrendingSearches] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Advanced filters
  const [filters, setFilters] = useState({
    mediaType: 'all', // 'all', 'movie', 'tv', 'anime'
    genre: '',
    year: '',
    rating: '',
    language: 'en',
    sortBy: 'relevance', // 'relevance', 'popularity', 'rating', 'date'
    includeAdult: false
  });
  
  // Recommendations
  const [recommendations, setRecommendations] = useState([]);
  
  // Refs
  const searchInputRef = useRef(null);
  
  // Genres for filtering
  const genres = [
    { id: 28, name: 'Action' },
    { id: 12, name: 'Adventure' },
    { id: 16, name: 'Animation' },
    { id: 35, name: 'Comedy' },
    { id: 80, name: 'Crime' },
    { id: 99, name: 'Documentary' },
    { id: 18, name: 'Drama' },
    { id: 10751, name: 'Family' },
    { id: 14, name: 'Fantasy' },
    { id: 36, name: 'History' },
    { id: 27, name: 'Horror' },
    { id: 10402, name: 'Music' },
    { id: 9648, name: 'Mystery' },
    { id: 10749, name: 'Romance' },
    { id: 878, name: 'Science Fiction' },
    { id: 10770, name: 'TV Movie' },
    { id: 53, name: 'Thriller' },
    { id: 10752, name: 'War' },
    { id: 37, name: 'Western' }
  ];
  
  // Years for filtering
  const years = Array.from({ length: 25 }, (_, i) => new Date().getFullYear() - i);
  
  // Ratings for filtering
  const ratings = [
    { value: '', label: 'Any Rating' },
    { value: '9', label: '9+ Stars' },
    { value: '8', label: '8+ Stars' },
    { value: '7', label: '7+ Stars' },
    { value: '6', label: '6+ Stars' }
  ];
  
  // Sort options
  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'popularity', label: 'Popularity' },
    { value: 'rating', label: 'Rating' },
    { value: 'date', label: 'Release Date' }
  ];

  // Load trending searches from TMDB
  const loadTrendingSearches = async () => {
    try {
      const response = await apiFetch('/api/trending-searches');
      if (response.ok) {
        const data = await response.json();
        setTrendingSearches(data.trending || []);
      }
    } catch (error) {
      console.error('Failed to load trending searches:', error);
    }
  };

  // Load recent searches from localStorage
  const loadRecentSearches = () => {
    try {
      const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
      setRecentSearches(recent.slice(0, 10));
    } catch (error) {
      console.error('Failed to load recent searches:', error);
    }
  };

  // Save search to recent searches
  const saveSearch = (searchQuery) => {
    try {
      const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
      const updated = [searchQuery, ...recent.filter(s => s !== searchQuery)].slice(0, 10);
      localStorage.setItem('recentSearches', JSON.stringify(updated));
      setRecentSearches(updated);
    } catch (error) {
      console.error('Failed to save search:', error);
    }
  };

  // Load personalized recommendations
  const loadRecommendations = useCallback(async () => {
    if (!user || !profile) return;
    
    try {
      const response = await apiFetch(`/api/recommendations?userId=${user.id}&profileId=${profile.id}`);
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.recommendations || []);
      }
    } catch (error) {
      console.error('Failed to load recommendations:', error);
    }
  }, [user, profile]);

  // Load trending searches
  useEffect(() => {
    loadTrendingSearches();
    loadRecentSearches();
    if (user && profile) {
      loadRecommendations();
    }
  }, [user, profile, loadRecommendations]);

  // Get search suggestions
  const getSuggestions = useCallback(async (searchQuery) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      setIsLoading(true);
      const response = await apiFetch(`/api/search-suggestions?query=${encodeURIComponent(searchQuery)}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
      }
    } catch (error) {
      console.error('Failed to get suggestions:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Debounced search suggestions
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim()) {
        getSuggestions(query);
      } else {
        setSuggestions([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, getSuggestions]);

  // Handle search submission
  const handleSearch = (searchQuery = query) => {
    if (!searchQuery.trim()) return;

    const searchParams = new URLSearchParams({
      q: searchQuery.trim(),
      ...filters
    });

    saveSearch(searchQuery.trim());
    onSearch(searchQuery.trim(), filters);
    onClose();
    navigate(`/search?${searchParams.toString()}`);
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion);
    handleSearch(suggestion);
  };

  // Handle trending search click
  const handleTrendingClick = (trending) => {
    setQuery(trending);
    handleSearch(trending);
  };

  // Handle recent search click
  const handleRecentClick = (recent) => {
    setQuery(recent);
    handleSearch(recent);
  };

  // Handle recommendation click
  const handleRecommendationClick = (item) => {
    navigate(`/${item.media_type === 'movie' ? 'movie' : 'tv'}/${item.id}`);
    onClose();
  };

  // Clear recent searches
  const clearRecentSearches = () => {
    localStorage.removeItem('recentSearches');
    setRecentSearches([]);
  };

  // Handle filter change
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  // Focus input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[999999] p-4">
      <div className="bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-2xl font-bold text-white">Advanced Search</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Main Search Area */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Search Input */}
            <div className="relative mb-6">
              <input
                ref={searchInputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                onFocus={() => setShowSuggestions(true)}
                placeholder="Search for movies, TV shows, actors, directors..."
                className="w-full px-4 py-3 bg-gray-800 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none text-lg"
              />
              <button
                onClick={() => handleSearch()}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Search
              </button>
            </div>

            {/* Advanced Filters */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
              {/* Media Type */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Type</label>
                <select
                  value={filters.mediaType}
                  onChange={(e) => handleFilterChange('mediaType', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  <option value="all">All</option>
                  <option value="movie">Movies</option>
                  <option value="tv">TV Shows</option>
                  <option value="anime">Anime</option>
                </select>
              </div>

              {/* Genre */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Genre</label>
                <select
                  value={filters.genre}
                  onChange={(e) => handleFilterChange('genre', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  <option value="">Any Genre</option>
                  {genres.map(genre => (
                    <option key={genre.id} value={genre.id}>{genre.name}</option>
                  ))}
                </select>
              </div>

              {/* Year */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Year</label>
                <select
                  value={filters.year}
                  onChange={(e) => handleFilterChange('year', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  <option value="">Any Year</option>
                  {years.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>

              {/* Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Rating</label>
                <select
                  value={filters.rating}
                  onChange={(e) => handleFilterChange('rating', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  {ratings.map(rating => (
                    <option key={rating.value} value={rating.value}>{rating.label}</option>
                  ))}
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 text-white rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              {/* Include Adult */}
              <div className="flex items-center">
                <label className="flex items-center text-sm text-gray-300">
                  <input
                    type="checkbox"
                    checked={filters.includeAdult}
                    onChange={(e) => handleFilterChange('includeAdult', e.target.checked)}
                    className="mr-2 rounded border-gray-600 bg-gray-800 text-blue-600 focus:ring-blue-500"
                  />
                  Include Adult Content
                </label>
              </div>
            </div>

            {/* Search Suggestions */}
            {showSuggestions && (suggestions.length > 0 || isLoading) && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-white mb-3">Suggestions</h3>
                {isLoading ? (
                  <div className="text-gray-400">Loading suggestions...</div>
                ) : (
                  <div className="space-y-2">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 hover:text-white transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Trending Searches */}
            {trendingSearches.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-white mb-3">Trending Searches</h3>
                <div className="flex flex-wrap gap-2">
                  {trendingSearches.map((trending, index) => (
                    <button
                      key={index}
                      onClick={() => handleTrendingClick(trending)}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-full text-sm transition-colors"
                    >
                      {trending}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-white">Recent Searches</h3>
                  <button
                    onClick={clearRecentSearches}
                    className="text-sm text-gray-400 hover:text-white transition-colors"
                  >
                    Clear All
                  </button>
                </div>
                <div className="space-y-2">
                  {recentSearches.map((recent, index) => (
                    <button
                      key={index}
                      onClick={() => handleRecentClick(recent)}
                      className="w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 hover:text-white transition-colors flex items-center"
                    >
                      <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {recent}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Recommendations Sidebar */}
          {recommendations.length > 0 && (
            <div className="w-80 p-6 border-l border-gray-700 overflow-y-auto">
              <h3 className="text-lg font-semibold text-white mb-4">Recommended for You</h3>
              <div className="space-y-3">
                {recommendations.slice(0, 10).map((item) => (
                  <button
                    key={item.id}
                    onClick={() => handleRecommendationClick(item)}
                    className="w-full text-left p-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      {item.poster_path && (
                        <img
                          src={`https://image.tmdb.org/t/p/w92${item.poster_path}`}
                          alt={item.title || item.name}
                          className="w-12 h-18 object-cover rounded"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-white font-medium truncate">
                          {item.title || item.name}
                        </h4>
                        <p className="text-gray-400 text-sm">
                          {item.media_type === 'movie' ? 'Movie' : 'TV Show'}
                        </p>
                        {item.vote_average && (
                          <p className="text-yellow-400 text-sm">★ {item.vote_average.toFixed(1)}</p>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedSearch; 