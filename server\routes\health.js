import express from 'express';
import { healthCheck, testConnection, query } from '../utils/database.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

/**
 * Basic health check endpoint
 */
router.get('/', async (req, res) => {
  try {
    const health = await healthCheck();
    
    const response = {
      status: health.status,
      timestamp: health.timestamp,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: {
        status: health.status,
        duration: health.duration,
        pool: health.pool
      }
    };
    
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(response);
    
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Detailed database health check
 */
router.get('/database', async (req, res) => {
  try {
    const health = await healthCheck();
    
    // Additional database metrics
    const metrics = await query(`
      SELECT 
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM profiles) as total_profiles,
        (SELECT COUNT(*) FROM watchlist) as total_watchlist_items,
        (SELECT COUNT(*) FROM watch_progress) as total_progress_records,
        (SELECT COUNT(*) FROM viewing_history) as total_viewing_records
    `);
    
    const connectionStats = await query(`
      SELECT 
        COUNT(*) as total_connections,
        COUNT(*) FILTER (WHERE state = 'active') as active_connections,
        COUNT(*) FILTER (WHERE state = 'idle') as idle_connections
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `);
    
    const databaseSize = await query(`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as database_size,
        pg_database_size(current_database()) as database_size_bytes
    `);
    
    res.json({
      ...health,
      metrics: metrics.rows[0],
      connections: connectionStats.rows[0],
      size: databaseSize.rows[0]
    });
    
  } catch (error) {
    logger.error('Database health check failed:', error);
    res.status(503).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Database performance metrics
 */
router.get('/database/performance', async (req, res) => {
  try {
    // Query performance stats
    const queryStats = await query(`
      SELECT 
        schemaname,
        tablename,
        seq_scan,
        seq_tup_read,
        idx_scan,
        idx_tup_fetch,
        n_tup_ins,
        n_tup_upd,
        n_tup_del
      FROM pg_stat_user_tables
      ORDER BY seq_scan + idx_scan DESC
      LIMIT 10
    `);
    
    // Index usage stats
    const indexStats = await query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan,
        idx_tup_read,
        idx_tup_fetch
      FROM pg_stat_user_indexes
      WHERE idx_scan > 0
      ORDER BY idx_scan DESC
      LIMIT 10
    `);
    
    // Slow queries (if pg_stat_statements is enabled)
    let slowQueries = [];
    try {
      slowQueries = await query(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements
        WHERE query NOT LIKE '%pg_stat_statements%'
        ORDER BY mean_time DESC
        LIMIT 5
      `);
      slowQueries = slowQueries.rows;
    } catch {
      // pg_stat_statements not available
    }
    
    // Lock information
    const locks = await query(`
      SELECT 
        mode,
        COUNT(*) as count
      FROM pg_locks
      WHERE granted = true
      GROUP BY mode
      ORDER BY count DESC
    `);
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      performance: {
        table_stats: queryStats.rows,
        index_stats: indexStats.rows,
        slow_queries: slowQueries,
        locks: locks.rows
      }
    });
    
  } catch (error) {
    logger.error('Database performance check failed:', error);
    res.status(503).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Database migration status
 */
router.get('/database/migrations', async (req, res) => {
  try {
    // Check if migrations table exists
    const tableExists = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'schema_migrations'
      ) as exists
    `);
    
    if (!tableExists.rows[0].exists) {
      return res.json({
        status: 'no_migrations_table',
        message: 'Schema migrations table does not exist',
        timestamp: new Date().toISOString()
      });
    }
    
    // Get migration status
    const migrations = await query(`
      SELECT 
        version,
        name,
        applied_at,
        checksum
      FROM schema_migrations
      ORDER BY version
    `);
    
    // Get table count
    const tableCount = await query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
    `);
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      migrations: migrations.rows,
      table_count: parseInt(tableCount.rows[0].count),
      latest_migration: migrations.rows[migrations.rows.length - 1] || null
    });
    
  } catch (error) {
    logger.error('Migration status check failed:', error);
    res.status(503).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * System resource usage
 */
router.get('/system', async (req, res) => {
  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    // Convert memory to MB
    const memory = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
      arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024)
    };
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: memory,
      cpu: cpuUsage,
      node_version: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid
    });
    
  } catch (error) {
    logger.error('System health check failed:', error);
    res.status(503).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Readiness probe (for Kubernetes/Docker)
 */
router.get('/ready', async (req, res) => {
  try {
    const connectionTest = await testConnection(1); // Single retry
    
    if (connectionTest.success) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        error: connectionTest.error,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    res.status(503).json({
      status: 'not_ready',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Liveness probe (for Kubernetes/Docker)
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

export default router;
