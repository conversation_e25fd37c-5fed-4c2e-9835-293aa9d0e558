import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { localAppStorage } from '../utils/localStorage';
import { apiFetch } from '../utils/api.js';

const StorageDebug = () => {
  const { user } = useAuth();
  const { profile: currentProfile, profiles } = useProfile();
  const [debugData, setDebugData] = useState(null);
  const [watchProgressData, setWatchProgressData] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadDebugData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Load general debug data
      const response = await apiFetch('/api/user-data/debug');
      const data = await response.json();
      setDebugData(data);

      // Load watch progress for all profiles
      const allWatchProgress = {};
      for (let profileId = 1; profileId <= 6; profileId++) {
        try {
          const progressResponse = await apiFetch(`/api/user-data/watch-progress/${user.id}/${profileId}`);
          const progressData = await progressResponse.json();
          if (progressData.success) {
            allWatchProgress[profileId] = progressData.watchProgress;
          }
        } catch (error) {
          console.error(`Error loading progress for profile ${profileId}:`, error);
          allWatchProgress[profileId] = {};
        }
      }
      setWatchProgressData(allWatchProgress);
      
    } catch (error) {
      console.error('Error loading debug data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDebugData();
  }, [user, currentProfile]);

  const clearAllWatchProgress = async () => {
    if (!user) return;
    
    // Multiple confirmations to prevent accidental deletion
    if (!window.confirm('⚠️ DANGER: This will DELETE ALL Continue Watching data for ALL profiles!\n\nAre you absolutely sure?')) return;
    if (!window.confirm('🚨 FINAL WARNING: This action CANNOT BE UNDONE!\n\nClick OK only if you want to permanently delete ALL watch progress.')) return;
    
    const userInput = prompt('Type "DELETE ALL" to confirm (case sensitive):');
    if (userInput !== 'DELETE ALL') {
      alert('❌ Cancelled - incorrect confirmation text');
      return;
    }
    
    setLoading(true);
    try {
      console.log('🗑️ DEBUG: User confirmed deletion of ALL watch progress');
      
      for (let profileId = 1; profileId <= 6; profileId++) {
        // Get current progress
        const response = await apiFetch(`/api/user-data/watch-progress/${user.id}/${profileId}`);
        const data = await response.json();
        
        if (data.success && data.watchProgress) {
          // Delete each item
          for (const contentId of Object.keys(data.watchProgress)) {
            await apiFetch(`/api/user-data/watch-progress/${user.id}/${profileId}/${contentId}`, {
              method: 'DELETE'
            });
          }
        }
      }
      
      // Reload debug data
      await loadDebugData();
      alert('✅ All watch progress cleared successfully!');
    } catch (error) {
      console.error('Error clearing watch progress:', error);
      alert('❌ Error clearing watch progress');
    } finally {
      setLoading(false);
    }
  };

  // NEW: Delete individual watch progress item
  // eslint-disable-next-line no-unused-vars
  const deleteWatchProgressItem = async (profileId, contentId, title) => {
    if (!user) return;
    
    if (!window.confirm(`Remove "${title}" from Profile ${profileId}?`)) return;
    
    try {
      console.log(`🗑️ DEBUG: Deleting "${title}" from User ${user.id}, Profile ${profileId}`);
      
      const response = await apiFetch(`/api/user-data/watch-progress/${user.id}/${profileId}/${contentId}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      if (data.success) {
        console.log(`✅ Successfully deleted "${title}"`);
        // Reload debug data to refresh the display
        await loadDebugData();
      } else {
        console.error(`❌ Failed to delete "${title}":`, data.error);
        alert(`❌ Failed to delete "${title}"`);
      }
    } catch (error) {
      console.error(`❌ Error deleting "${title}":`, error);
      alert(`❌ Error deleting "${title}"`);
    }
  };

  // eslint-disable-next-line no-unused-vars
  const debugProfileStorage = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    console.log('🔍 DEBUG: Starting profile storage investigation...');
    
    const data = {
      currentUser: user,
      currentProfile: currentProfile,
      profiles: profiles,
      timestamp: new Date().toISOString()
    };

    // Check browser localStorage backups
    try {
      const backup1 = localStorage.getItem('torvie_current_profile_backup');
      const backup2 = localStorage.getItem('torvie_profile_persistent');
      
      data.browserBackups = {
        backup1: backup1 ? JSON.parse(backup1) : null,
        backup2: backup2 ? JSON.parse(backup2) : null
      };
    } catch (error) {
      data.browserBackups = { error: error.message };
    }

    // Check API storage
    try {
      data.apiCurrentProfile = await localAppStorage.getCurrentProfile(user.id);
      data.apiProfiles = await localAppStorage.getProfiles(user.id);
      data.allApiData = await localAppStorage.getAllData();
    } catch (error) {
      data.apiError = error.message;
    }

    setDebugData(data);
    console.log('🔍 DEBUG: Profile storage data:', data);
  };

  // eslint-disable-next-line no-unused-vars
  const clearAllProfileData = async () => {
    if (!user) return;
    
    const confirmed = window.confirm('This will clear ALL profile data. Are you sure?');
    if (!confirmed) return;

    // Clear browser storage
    localStorage.removeItem('torvie_current_profile_backup');
    localStorage.removeItem('torvie_profile_persistent');
    
    // Clear API storage
    try {
      await localAppStorage.saveCurrentProfile(user.id, null);
      await localAppStorage.saveProfiles(user.id, []);
    } catch (error) {
      console.error('Error clearing API data:', error);
    }
    
    alert('All profile data cleared. Please refresh the page.');
  };

  // eslint-disable-next-line no-unused-vars
  const forceSetProfile = async (profileId) => {
    if (!user || !profiles) return;
    
    const targetProfile = profiles.find(p => p.id === parseInt(profileId));
    if (!targetProfile) {
      alert('Profile not found');
      return;
    }

    console.log(`🔧 DEBUG: Force setting profile to ${targetProfile.name}`);
    
    // Save to all storage locations
    try {
      await localAppStorage.saveCurrentProfile(user.id, targetProfile);
      
      // Browser backups
      const backup = { userId: user.id, profile: targetProfile, timestamp: Date.now() };
      localStorage.setItem('torvie_current_profile_backup', JSON.stringify(backup));
      localStorage.setItem('torvie_profile_persistent', JSON.stringify(backup));
      
      alert(`Profile force-set to ${targetProfile.name}. Please refresh to test.`);
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  if (!user) {
    return (
      <div className="bg-gray-900 rounded-lg p-4 mb-6">
        <h3 className="text-white font-bold mb-2">🔧 Profile Storage Debug</h3>
        <p className="text-gray-400">Please log in to view debug information</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-900 rounded-lg p-4 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-bold">🔧 Profile Storage Debug</h3>
        <div className="flex gap-2">
          <button
            onClick={loadDebugData}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
          >
            {loading ? '⏳' : '🔄'} Refresh
          </button>
          <button
            onClick={clearAllWatchProgress}
            disabled={loading}
            className="bg-red-700 hover:bg-red-800 text-white px-3 py-1 rounded text-sm disabled:opacity-50 border border-red-500"
          >
            ⚠️ DANGER: Clear ALL
          </button>
        </div>
      </div>

      {/* Current State */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="bg-gray-800 rounded p-3">
          <h4 className="text-white font-semibold mb-2">📊 Current State</h4>
          <div className="text-sm space-y-1">
            <div className="text-gray-300">
              <span className="text-blue-400">User ID:</span> {user.id}
            </div>
            <div className="text-gray-300">
              <span className="text-blue-400">Current Profile:</span> {currentProfile ? `"${currentProfile.name}" (ID: ${currentProfile.id})` : 'None'}
            </div>
            <div className="text-gray-300">
              <span className="text-blue-400">Active Profiles:</span> {profiles.filter(p => p.active).length}
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded p-3">
          <h4 className="text-white font-semibold mb-2">👥 All Profiles</h4>
          <div className="text-sm space-y-1">
            {profiles.map(profile => (
              <div key={profile.id} className={`text-gray-300 ${currentProfile?.id === profile.id ? 'text-green-400 font-semibold' : ''}`}>
                <span className="text-blue-400">Profile {profile.id}:</span> {profile.active ? `"${profile.name}"` : '(Inactive)'}
                {currentProfile?.id === profile.id && ' ← CURRENT'}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Watch Progress by Profile */}
      {watchProgressData && (
        <div className="bg-gray-800 rounded p-3 mb-4">
          <h4 className="text-white font-semibold mb-2">📺 Continue Watching by Profile</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {Object.entries(watchProgressData).map(([profileId, progress]) => {
              const itemCount = Object.keys(progress).length;
              const profileInfo = profiles.find(p => p.id === parseInt(profileId));
              
              return (
                <div key={profileId} className="bg-gray-700 rounded p-2">
                  <div className="text-white font-medium mb-1">
                    Profile {profileId} 
                    {profileInfo ? ` - "${profileInfo.name}"` : ' (Unknown)'}
                    {parseInt(profileId) === currentProfile?.id && ' ⭐'}
                  </div>
                  <div className="text-gray-300 text-sm mb-2">
                    Items: {itemCount}
                  </div>
                  {itemCount > 0 ? (
                    <div className="space-y-1">
                      {Object.values(progress).map((item, idx) => (
                        <div key={idx} className="text-xs text-gray-400 bg-gray-600 rounded p-1">
                          <div className="font-medium text-gray-200">{item.title}</div>
                          <div>{Math.round(item.watchedPercentage)}% watched</div>
                          <div>{new Date(item.lastWatched).toLocaleString()}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-xs text-gray-500 italic">No items</div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Raw Debug Data */}
      {debugData && (
        <details className="bg-gray-800 rounded p-3">
          <summary className="text-white font-semibold cursor-pointer mb-2">🔍 Raw Storage Data</summary>
          <pre className="text-xs text-gray-300 overflow-auto max-h-60">
            {JSON.stringify(debugData, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
};

export default StorageDebug; 