import express from 'express';
import { getLiveTvChannels } from '../services/liveTvScraper.js';

const router = express.Router();

// GET /api/live-tv - Returns a list of free, legal live TV channels
router.get('/', async (req, res) => {
  console.log('🔴 Live TV Route: GET /api/live-tv requested');
  try {
    const channels = await getLiveTvChannels();
    console.log('🔴 Live TV Route: Returning', channels.length, 'channels');
    res.json(channels);
  } catch (err) {
    console.error('🔴 Live TV Route: Error:', err);
    res.status(500).json({ error: 'Failed to fetch live TV channels' });
  }
});

export default router; 