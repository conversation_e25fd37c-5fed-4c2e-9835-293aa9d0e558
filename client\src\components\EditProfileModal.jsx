import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';

const defaultAvatars = [
  '/avatars/avatar1.png',
  '/avatars/avatar2.png',
  '/avatars/avatar3.png',
  '/avatars/avatar4.png',
];

const EditProfileModal = ({ isOpen, onClose }) => {
  const { profiles, setProfiles, profile: currentProfile, setProfile, saveProfilesManually } = useProfile();
  // eslint-disable-next-line no-unused-vars
  const { user } = useAuth();
  const [editingProfile, setEditingProfile] = useState(null);
  const [newName, setNewName] = useState('');
  const [newAvatar, setNewAvatar] = useState(defaultAvatars[0]);
  const [saving, setSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showProfileSelection, setShowProfileSelection] = useState(false);

  // Check if current profile is the main account
  const isMainAccount = currentProfile?.id === 1 || currentProfile?.name?.includes('Main Account');
  
  // Get active profiles for main account selection (includes main account)
  const activeProfiles = profiles ? profiles.filter(p => p.active) : [];

  useEffect(() => {
    if (isOpen) {
      if (isMainAccount) {
        // Main account starts with profile selection
        setShowProfileSelection(true);
        setEditingProfile(null);
      } else {
        // Regular users edit their own profile directly
        setEditingProfile(currentProfile);
        setNewName(currentProfile?.name?.replace(' - Main Account', '') || '');
        setNewAvatar(currentProfile?.avatar || defaultAvatars[0]);
        setShowProfileSelection(false);
      }
    }
  }, [isOpen, currentProfile, isMainAccount]);

  const handleProfileSelect = (profile) => {
    setEditingProfile(profile);
    setNewName(profile.name?.replace(' - Main Account', '') || '');
    setNewAvatar(profile.avatar || defaultAvatars[0]);
    setShowProfileSelection(false);
  };

  if (!isOpen) return null;

  const handleSave = async (e) => {
    e.preventDefault();
    if (!editingProfile || !newName.trim()) return;
    
    setSaving(true);
    try {
      // Preserve "Main Account" suffix for main account
      const finalName = editingProfile.id === 1 ? 
        `${newName.trim()} - Main Account` : 
        newName.trim();

      const updatedProfiles = profiles.map(p => 
        p.id === editingProfile.id 
          ? { ...p, name: finalName, avatar: newAvatar }
          : p
      );

      // Update the current profile object as well
      const updatedCurrentProfile = { ...editingProfile, name: finalName, avatar: newAvatar };

      const saveSuccess = await saveProfilesManually(updatedProfiles, updatedCurrentProfile);
      
      if (saveSuccess) {
        console.log('✅ Profile edited successfully');
        setProfiles(updatedProfiles);
        setProfile(updatedCurrentProfile);
        onClose();
      } else {
        alert('Failed to save profile changes. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error saving profile:', error);
      alert('Failed to save profile changes');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!editingProfile || !isMainAccount) return;
    
    setDeleting(true);
    try {
      const activeProfilesCount = profiles.filter(p => p.active).length;
      if (activeProfilesCount <= 1) {
        alert('You must have at least one profile. Create another profile before deleting this one.');
        setDeleting(false);
        return;
      }

      // Mark profile as inactive and clear its data
      const updatedProfiles = profiles.map(p => 
        p.id === editingProfile.id 
          ? { ...p, name: '', avatar: p.avatar, active: false } 
          : p
      );
      
      // If deleting current profile, switch to first active profile
      let newCurrentProfile = currentProfile;
      if (currentProfile && currentProfile.id === editingProfile.id) {
        const activeProfiles = updatedProfiles.filter(p => p.active);
        if (activeProfiles.length > 0) {
          newCurrentProfile = activeProfiles[0];
        }
      }
      
      const saveSuccess = await saveProfilesManually(updatedProfiles, newCurrentProfile);
      
      if (saveSuccess) {
        console.log('✅ Profile deleted successfully');
        setProfiles(updatedProfiles);
        setProfile(newCurrentProfile);
        onClose();
      } else {
        alert('Failed to delete profile. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error deleting profile:', error);
      alert('Failed to delete profile');
    } finally {
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return createPortal(
    <div 
      style={{ 
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0, 
        bottom: 0,
        zIndex: 2147483647,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '1rem',
        overflowY: 'auto'
      }}
    >
      <div 
        style={{
          backgroundColor: '#111827',
          borderRadius: '1rem',
          padding: '2rem',
          width: '100%',
          maxWidth: '48rem',
          border: '1px solid rgba(59, 130, 246, 0.4)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          margin: '2rem 0',
          position: 'relative',
          zIndex: 2147483647
        }}
      >
        {showProfileSelection ? (
          <div>
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              Select Profile to Edit
            </h2>
            <p className="text-gray-400 text-center mb-6 text-sm">
              Choose which profile you want to edit or delete
            </p>
            
            <div className="mb-6">
              <style>{`
                .modal-profile-item { 
                  position: relative; 
                }
                .modal-profile-button { 
                  display: flex; 
                  flex-direction: column; 
                  align-items: center; 
                  outline: none; 
                  width: 100%; 
                  transition: transform 0.2s; 
                  background: none; 
                  border: none; 
                  cursor: pointer; 
                }
                .modal-profile-button:hover { 
                  transform: scale(1.05); 
                }
                .modal-profile-avatar-container { 
                  position: relative; 
                  width: 6rem; 
                  height: 6rem; 
                  border-radius: 50%;
                  border: 4px solid transparent; 
                  transition: border-color 0.3s; 
                  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
                  overflow: hidden; 
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
                .modal-profile-button:hover .modal-profile-avatar-container { 
                  border-color: #60a5fa; 
                }
                .modal-profile-avatar-letter { 
                  width: 100%; 
                  height: 100%; 
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 2rem;
                  font-weight: 700;
                  font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
                }
                .modal-profile-name { 
                  font-size: 1.125rem; 
                  font-weight: 600; 
                  transition: color 0.3s; 
                  margin-top: 0.75rem; 
                  color: #9ca3af; 
                }
                .modal-profile-item:hover .modal-profile-name { 
                  color: #60a5fa; 
                }
              `}</style>
              <div className="modal-profiles-grid" style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(3, 1fr)', 
                gap: '2rem',
                justifyContent: 'center',
                justifyItems: 'center',
                width: '100%',
                maxWidth: '600px',
                margin: '0 auto'
              }}>
                {activeProfiles.map((profile) => (
                  <div key={profile.id} className="modal-profile-item">
                    <button
                      onClick={() => handleProfileSelect(profile)}
                      className="modal-profile-button"
                    >
                      <div className="modal-profile-avatar-container">
                        <div className="modal-profile-avatar-letter" style={{
                          background: profile.id === 1 ? '#4471ef' : 
                                    profile.id === 2 ? '#8359e0' : 
                                    profile.id === 3 ? '#e25f8c' : 
                                    profile.id === 4 ? '#f59e0b' : 
                                    profile.id === 5 ? '#10b981' : '#6366f1'
                        }}>
                          {profile.name?.charAt(0)?.toUpperCase() || 'U'}
                        </div>
                      </div>
                      <span className="modal-profile-name">
                        {profile.name?.replace(' - Main Account', '') || 'Unknown'}
                      </span>
                    </button>
                  </div>
                ))}
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="w-full bg-gray-700 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
          </div>
        ) : (
          <div>
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              {isMainAccount && editingProfile ? 
                `Edit ${editingProfile.name?.replace(' - Main Account', '')}${editingProfile.id === 1 ? ' (Main Account)' : ''}` : 
                'Edit Profile'
              }
            </h2>
            
            {isMainAccount && editingProfile && (
              <button
                onClick={() => setShowProfileSelection(true)}
                className="mb-6 text-blue-400 hover:text-blue-300 text-sm flex items-center gap-2"
              >
                ← Back to profile selection
              </button>
            )}
        
        {showDeleteConfirm ? (
          <div className="space-y-6">
            <p className="text-gray-300 text-center">
              Are you sure you want to delete the profile &quot;{editingProfile?.name?.replace(' - Main Account', '')}&quot;? 
              This action cannot be undone.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete Profile'}
              </button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSave} className="space-y-6">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">Profile Name</label>
              <input
                type="text"
                value={newName}
                onChange={e => setNewName(e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter profile name"
                required
              />
              <span className="text-xs text-gray-400">You can use emoji, e.g. &quot;🐉&quot; or &quot;🎬&quot;</span>
              <span className="text-xs text-gray-400">Profile name must be unique (e.g. &quot;Dad&apos;s Account&quot;)</span>
              <span className="text-red-500">Name must be at least 2 characters and cannot contain &quot; or &apos; or emoji.</span>
            </div>
            
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-3">Choose Avatar</label>
              <div className="grid grid-cols-4 gap-3">
                {defaultAvatars.map((avatar, idx) => (
                  <button
                    key={avatar}
                    type="button"
                    onClick={() => setNewAvatar(avatar)}
                    className={`w-16 h-16 rounded-full border-4 transition-all ${
                      newAvatar === avatar 
                        ? 'border-blue-500 scale-110' 
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    <img
                      src={avatar}
                      alt={`Avatar ${idx+1}`}
                      className="w-full h-full rounded-full object-cover bg-gray-800"
                      onError={e => { e.target.style.display = 'none'; }}
                    />
                  </button>
                ))}
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                type="submit"
                className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition-colors"
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-700 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-600 transition-colors"
                disabled={saving}
              >
                Cancel
              </button>
            </div>
            
            {isMainAccount && (
              <div className="pt-4 border-t border-gray-700">
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(true)}
                  className="w-full bg-red-600/20 text-red-400 px-4 py-3 rounded-lg font-medium hover:bg-red-600/30 transition-colors border border-red-500/30"
                >
                  Delete Profile
                </button>
                <p className="text-gray-500 text-xs mt-2 text-center">
                  Only main account holders can delete profiles
                </p>
              </div>
            )}
          </form>
        )}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default EditProfileModal; 