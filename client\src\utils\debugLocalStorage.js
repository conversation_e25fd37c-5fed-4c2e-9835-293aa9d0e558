// Debug utility for localStorage operations
export const debugLocalStorage = {
  // List all Torvie-related keys
  listTorvieKeys() {
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('torvie_')) {
        keys.push(key);
      }
    }
    return keys;
  },

  // Get all Torvie data
  getAllTorvieData() {
    const data = {};
    const keys = this.listTorvieKeys();
    keys.forEach(key => {
      try {
        const value = localStorage.getItem(key);
        data[key] = value ? JSON.parse(value) : value;
      } catch (e) {
        data[key] = localStorage.getItem(key); // Store as string if can't parse
      }
    });
    return data;
  },

  // Log all Torvie data to console
  logAllTorvieData() {
    console.log('🔍 DEBUG: All Torvie localStorage data:');
    const data = this.getAllTorvieData();
    Object.entries(data).forEach(([key, value]) => {
      console.log(`  ${key}:`, value);
    });
    return data;
  },

  // Clear all Torvie data
  clearAllTorvieData() {
    const keys = this.listTorvieKeys();
    keys.forEach(key => localStorage.removeItem(key));
    console.log('🧹 DEBUG: Cleared all Torvie localStorage data');
  },

  // Test localStorage persistence
  testPersistence() {
    const testKey = 'torvie_debug_test';
    const testData = { timestamp: Date.now(), test: true };
    
    // Save test data
    localStorage.setItem(testKey, JSON.stringify(testData));
    console.log('💾 DEBUG: Saved test data:', testData);
    
    // Immediately verify
    const retrieved = localStorage.getItem(testKey);
    console.log('🔍 DEBUG: Retrieved test data:', retrieved ? JSON.parse(retrieved) : null);
    
    // Clean up
    localStorage.removeItem(testKey);
    
    return retrieved !== null;
  },

  // Force save profiles for a user
  forceSaveProfiles(userId, profiles) {
    const key = `torvie_profiles_${userId}`;
    localStorage.setItem(key, JSON.stringify(profiles));
    console.log(`💾 DEBUG: Force saved profiles for user ${userId}:`, profiles);
    
    // Verify immediately
    const verification = localStorage.getItem(key);
    console.log(`✅ DEBUG: Verification for ${key}:`, verification ? JSON.parse(verification) : null);
  },

  // Force save watchlist for user/profile
  forceSaveWatchlist(userId, profileId, watchlist) {
    const key = `torvie_watchlist_${userId}_${profileId}`;
    localStorage.setItem(key, JSON.stringify(watchlist));
    console.log(`💾 DEBUG: Force saved watchlist for user ${userId}, profile ${profileId}:`, watchlist);
    
    // Verify immediately
    const verification = localStorage.getItem(key);
    console.log(`✅ DEBUG: Verification for ${key}:`, verification ? JSON.parse(verification) : null);
  }
};

// Make it available globally in development
if (typeof window !== 'undefined') {
  window.debugLS = debugLocalStorage;
} 