#!/usr/bin/env node

/**
 * Database Migration Script for Torvie
 * Handles database schema migrations with versioning and rollback support
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import pg from 'pg';
import { logger } from '../utils/logger.js';

const { Pool } = pg;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL || 'postgresql://torvie:torvie_password@localhost:5432/torvie',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

const pool = new Pool(dbConfig);

// Migration tracking table
const MIGRATIONS_TABLE = `
  CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    version VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    checksum VARCHAR(64) NOT NULL
  );
`;

/**
 * Calculate checksum for migration file content
 */
function calculateChecksum(content) {
  const crypto = await import('crypto');
  return crypto.createHash('sha256').update(content).digest('hex');
}

/**
 * Get list of migration files
 */
async function getMigrationFiles() {
  const migrationsDir = path.join(__dirname, '../database/migrations');
  
  try {
    const files = await fs.readdir(migrationsDir);
    return files
      .filter(file => file.endsWith('.sql'))
      .sort()
      .map(file => ({
        version: file.replace('.sql', ''),
        name: file.replace(/^\d+_/, '').replace('.sql', '').replace(/_/g, ' '),
        filename: file,
        path: path.join(migrationsDir, file)
      }));
  } catch (error) {
    logger.error('Failed to read migrations directory:', error);
    return [];
  }
}

/**
 * Get applied migrations from database
 */
async function getAppliedMigrations() {
  try {
    const result = await pool.query('SELECT version, checksum FROM schema_migrations ORDER BY version');
    return result.rows;
  } catch (error) {
    // Table doesn't exist yet
    return [];
  }
}

/**
 * Apply a single migration
 */
async function applyMigration(migration) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // Read migration file
    const content = await fs.readFile(migration.path, 'utf8');
    const checksum = calculateChecksum(content);
    
    logger.info(`Applying migration: ${migration.version} - ${migration.name}`);
    
    // Execute migration SQL
    await client.query(content);
    
    // Record migration
    await client.query(
      'INSERT INTO schema_migrations (version, name, checksum) VALUES ($1, $2, $3)',
      [migration.version, migration.name, checksum]
    );
    
    await client.query('COMMIT');
    logger.info(`✅ Migration ${migration.version} applied successfully`);
    
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error(`❌ Failed to apply migration ${migration.version}:`, error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Verify migration integrity
 */
async function verifyMigrations() {
  const migrationFiles = await getMigrationFiles();
  const appliedMigrations = await getAppliedMigrations();
  
  const issues = [];
  
  for (const applied of appliedMigrations) {
    const file = migrationFiles.find(f => f.version === applied.version);
    
    if (!file) {
      issues.push(`Applied migration ${applied.version} not found in files`);
      continue;
    }
    
    const content = await fs.readFile(file.path, 'utf8');
    const currentChecksum = calculateChecksum(content);
    
    if (currentChecksum !== applied.checksum) {
      issues.push(`Migration ${applied.version} has been modified after application`);
    }
  }
  
  return issues;
}

/**
 * Run pending migrations
 */
async function runMigrations() {
  try {
    // Ensure migrations table exists
    await pool.query(MIGRATIONS_TABLE);
    
    // Verify existing migrations
    const issues = await verifyMigrations();
    if (issues.length > 0) {
      logger.warn('Migration integrity issues found:');
      issues.forEach(issue => logger.warn(`  - ${issue}`));
      
      if (process.env.NODE_ENV === 'production') {
        throw new Error('Migration integrity check failed in production');
      }
    }
    
    // Get pending migrations
    const migrationFiles = await getMigrationFiles();
    const appliedMigrations = await getAppliedMigrations();
    const appliedVersions = new Set(appliedMigrations.map(m => m.version));
    
    const pendingMigrations = migrationFiles.filter(m => !appliedVersions.has(m.version));
    
    if (pendingMigrations.length === 0) {
      logger.info('✅ Database is up to date');
      return;
    }
    
    logger.info(`Found ${pendingMigrations.length} pending migrations`);
    
    // Apply pending migrations
    for (const migration of pendingMigrations) {
      await applyMigration(migration);
    }
    
    logger.info('🎉 All migrations applied successfully');
    
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Show migration status
 */
async function showStatus() {
  try {
    await pool.query(MIGRATIONS_TABLE);
    
    const migrationFiles = await getMigrationFiles();
    const appliedMigrations = await getAppliedMigrations();
    const appliedVersions = new Set(appliedMigrations.map(m => m.version));
    
    console.log('\n📊 Migration Status:');
    console.log('==================');
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found');
      return;
    }
    
    migrationFiles.forEach(migration => {
      const status = appliedVersions.has(migration.version) ? '✅ Applied' : '⏳ Pending';
      const applied = appliedMigrations.find(m => m.version === migration.version);
      const appliedAt = applied ? ` (${applied.applied_at})` : '';
      
      console.log(`${status} ${migration.version} - ${migration.name}${appliedAt}`);
    });
    
    const pendingCount = migrationFiles.filter(m => !appliedVersions.has(m.version)).length;
    console.log(`\nTotal: ${migrationFiles.length} migrations, ${pendingCount} pending\n`);
    
  } catch (error) {
    logger.error('Failed to show migration status:', error);
    process.exit(1);
  }
}

/**
 * Create a new migration file
 */
async function createMigration(name) {
  if (!name) {
    console.error('Migration name is required');
    console.log('Usage: npm run migrate:create <migration_name>');
    process.exit(1);
  }
  
  const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const version = `${timestamp}_${name.toLowerCase().replace(/\s+/g, '_')}`;
  const filename = `${version}.sql`;
  
  const migrationsDir = path.join(__dirname, '../database/migrations');
  const filepath = path.join(migrationsDir, filename);
  
  const template = `-- Migration: ${version}
-- Description: ${name}
-- Created: ${new Date().toISOString().slice(0, 10)}

-- Add your migration SQL here
-- Example:
-- CREATE TABLE example (
--     id SERIAL PRIMARY KEY,
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- Don't forget to add indexes if needed
-- CREATE INDEX idx_example_name ON example(name);
`;
  
  try {
    await fs.mkdir(migrationsDir, { recursive: true });
    await fs.writeFile(filepath, template);
    console.log(`✅ Created migration: ${filename}`);
  } catch (error) {
    console.error('Failed to create migration:', error);
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'up':
    case undefined:
      await runMigrations();
      break;
      
    case 'status':
      await showStatus();
      break;
      
    case 'create':
      await createMigration(process.argv[3]);
      break;
      
    default:
      console.log('Usage:');
      console.log('  npm run migrate        - Run pending migrations');
      console.log('  npm run migrate up     - Run pending migrations');
      console.log('  npm run migrate status - Show migration status');
      console.log('  npm run migrate create <name> - Create new migration');
      process.exit(1);
  }
  
  await pool.end();
}

// Handle errors
process.on('unhandledRejection', (error) => {
  logger.error('Unhandled rejection:', error);
  process.exit(1);
});

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logger.error('Migration script failed:', error);
    process.exit(1);
  });
}

export { runMigrations, showStatus, createMigration };
