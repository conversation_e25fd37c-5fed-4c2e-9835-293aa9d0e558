/**
 * Advanced Recommendation Engine
 * Provides personalized content recommendations based on user behavior and content analysis
 */

// Content similarity scoring
class ContentSimilarityEngine {
  constructor() {
    this.genreWeights = {
      'Action': 0.3,
      'Adventure': 0.25,
      'Comedy': 0.2,
      'Drama': 0.3,
      'Horror': 0.15,
      'Romance': 0.2,
      'Sci-Fi': 0.25,
      'Thriller': 0.3,
      'Documentary': 0.1,
      'Animation': 0.15,
      'Family': 0.2,
      'Fantasy': 0.25,
      'Mystery': 0.25,
      'Crime': 0.25,
      'War': 0.2,
      'History': 0.15,
      'Music': 0.1,
      'Western': 0.1
    };
  }

  // Calculate similarity score between two content items
  calculateSimilarity(item1, item2) {
    let score = 0;
    let totalWeight = 0;

    // Genre similarity (weighted)
    if (item1.genre_ids && item2.genre_ids) {
      const commonGenres = item1.genre_ids.filter(id => item2.genre_ids.includes(id));
      const genreScore = commonGenres.length / Math.max(item1.genre_ids.length, item2.genre_ids.length);
      score += genreScore * 0.4;
      totalWeight += 0.4;
    }

    // Release year similarity
    if (item1.release_date && item2.release_date) {
      const year1 = new Date(item1.release_date).getFullYear();
      const year2 = new Date(item2.release_date).getFullYear();
      const yearDiff = Math.abs(year1 - year2);
      const yearScore = Math.max(0, 1 - (yearDiff / 20)); // Decay over 20 years
      score += yearScore * 0.2;
      totalWeight += 0.2;
    }

    // Rating similarity
    if (item1.vote_average && item2.vote_average) {
      const ratingDiff = Math.abs(item1.vote_average - item2.vote_average);
      const ratingScore = Math.max(0, 1 - (ratingDiff / 5)); // Decay over 5 rating points
      score += ratingScore * 0.15;
      totalWeight += 0.15;
    }

    // Popularity similarity
    if (item1.popularity && item2.popularity) {
      const popDiff = Math.abs(item1.popularity - item2.popularity);
      const popScore = Math.max(0, 1 - (popDiff / 100)); // Decay over 100 popularity points
      score += popScore * 0.1;
      totalWeight += 0.1;
    }

    // Language similarity (if available)
    if (item1.original_language && item2.original_language) {
      if (item1.original_language === item2.original_language) {
        score += 0.1;
        totalWeight += 0.1;
      }
    }

    // Runtime similarity (if available)
    if (item1.runtime && item2.runtime) {
      const runtimeDiff = Math.abs(item1.runtime - item2.runtime);
      const runtimeScore = Math.max(0, 1 - (runtimeDiff / 60)); // Decay over 60 minutes
      score += runtimeScore * 0.05;
      totalWeight += 0.05;
    }

    return totalWeight > 0 ? score / totalWeight : 0;
  }

  // Find similar content
  findSimilarContent(targetItem, contentList, limit = 10) {
    return contentList
      .filter(item => item.id !== targetItem.id)
      .map(item => ({
        item,
        similarity: this.calculateSimilarity(targetItem, item)
      }))
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(result => result.item);
  }
}

// User behavior analysis
class UserBehaviorAnalyzer {
  constructor() {
    this.behaviorPatterns = new Map();
  }

  // Analyze user watch history
  analyzeWatchHistory(watchHistory) {
    const patterns = {
      genres: new Map(),
      years: new Map(),
      ratings: new Map(),
      watchTimes: new Map(),
      contentTypes: new Map()
    };

    watchHistory.forEach(entry => {
      // Genre analysis
      if (entry.genre_ids) {
        entry.genre_ids.forEach(genreId => {
          patterns.genres.set(genreId, (patterns.genres.get(genreId) || 0) + 1);
        });
      }

      // Year analysis
      if (entry.release_date) {
        const year = new Date(entry.release_date).getFullYear();
        patterns.years.set(year, (patterns.years.get(year) || 0) + 1);
      }

      // Rating analysis
      if (entry.vote_average) {
        const ratingRange = Math.floor(entry.vote_average);
        patterns.ratings.set(ratingRange, (patterns.ratings.get(ratingRange) || 0) + 1);
      }

      // Watch time analysis
      if (entry.watchTime) {
        const hour = new Date(entry.watchTime).getHours();
        patterns.watchTimes.set(hour, (patterns.watchTimes.get(hour) || 0) + 1);
      }

      // Content type analysis
      const contentType = entry.media_type || 'movie';
      patterns.contentTypes.set(contentType, (patterns.contentTypes.get(contentType) || 0) + 1);
    });

    return patterns;
  }

  // Get user preferences
  getUserPreferences(watchHistory) {
    const patterns = this.analyzeWatchHistory(watchHistory);
    
    return {
      favoriteGenres: Array.from(patterns.genres.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([genreId, count]) => ({ genreId, count })),
      
      favoriteYears: Array.from(patterns.years.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([year, count]) => ({ year, count })),
      
      preferredRatings: Array.from(patterns.ratings.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([rating, count]) => ({ rating, count })),
      
      preferredWatchTimes: Array.from(patterns.watchTimes.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([hour, count]) => ({ hour, count })),
      
      preferredContentTypes: Array.from(patterns.contentTypes.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([type, count]) => ({ type, count }))
    };
  }
}

// Collaborative filtering
class CollaborativeFilter {
  constructor() {
    this.userRatings = new Map();
    this.itemRatings = new Map();
  }

  // Add user rating
  addRating(userId, itemId, rating) {
    if (!this.userRatings.has(userId)) {
      this.userRatings.set(userId, new Map());
    }
    this.userRatings.get(userId).set(itemId, rating);

    if (!this.itemRatings.has(itemId)) {
      this.itemRatings.set(itemId, new Map());
    }
    this.itemRatings.get(itemId).set(userId, rating);
  }

  // Calculate user similarity
  calculateUserSimilarity(user1, user2) {
    const ratings1 = this.userRatings.get(user1);
    const ratings2 = this.userRatings.get(user2);

    if (!ratings1 || !ratings2) return 0;

    const commonItems = Array.from(ratings1.keys()).filter(itemId => ratings2.has(itemId));
    if (commonItems.length < 3) return 0; // Need at least 3 common items

    let sumSquaredDiff = 0;
    commonItems.forEach(itemId => {
      const diff = ratings1.get(itemId) - ratings2.get(itemId);
      sumSquaredDiff += diff * diff;
    });

    const similarity = 1 / (1 + Math.sqrt(sumSquaredDiff / commonItems.length));
    return similarity;
  }

  // Get recommendations for user
  getCollaborativeRecommendations(userId, limit = 10) {
    const userRatings = this.userRatings.get(userId);
    if (!userRatings) return [];

    // Find similar users
    const similarUsers = Array.from(this.userRatings.keys())
      .filter(id => id !== userId)
      .map(id => ({
        userId: id,
        similarity: this.calculateUserSimilarity(userId, id)
      }))
      .filter(user => user.similarity > 0.3)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 10);

    // Get items rated by similar users
    const recommendations = new Map();
    similarUsers.forEach(({ userId: similarUserId, similarity }) => {
      const similarUserRatings = this.userRatings.get(similarUserId);
      similarUserRatings.forEach((rating, itemId) => {
        if (!userRatings.has(itemId) && rating >= 7) { // Only recommend highly rated items
          const currentScore = recommendations.get(itemId) || 0;
          recommendations.set(itemId, currentScore + (rating * similarity));
        }
      });
    });

    return Array.from(recommendations.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([itemId, score]) => ({ itemId, score }));
  }
}

// Trending analysis
class TrendingAnalyzer {
  constructor() {
    this.trendingScores = new Map();
    this.timeDecayFactor = 0.95; // Decay factor per day
  }

  // Update trending scores
  updateTrendingScores(contentList, currentTime = Date.now()) {
    contentList.forEach(item => {
      const ageInDays = (currentTime - new Date(item.release_date).getTime()) / (1000 * 60 * 60 * 24);
      const popularity = item.popularity || 0;
      const rating = item.vote_average || 0;
      
      // Trending score formula: popularity * rating * time_decay
      const trendingScore = popularity * (rating / 10) * Math.pow(this.timeDecayFactor, ageInDays);
      
      this.trendingScores.set(item.id, {
        score: trendingScore,
        timestamp: currentTime,
        popularity,
        rating,
        ageInDays
      });
    });
  }

  // Get trending content
  getTrendingContent(contentList, limit = 20) {
    return contentList
      .map(item => ({
        ...item,
        trendingScore: this.trendingScores.get(item.id)?.score || 0
      }))
      .sort((a, b) => b.trendingScore - a.trendingScore)
      .slice(0, limit);
  }

  // Get trending by category
  getTrendingByCategory(contentList, category, limit = 10) {
    return this.getTrendingContent(contentList, limit)
      .filter(item => {
        if (category === 'all') return true;
        return item.genre_ids && item.genre_ids.some(id => {
          const genreMap = {
            'action': [28, 12],
            'comedy': [35],
            'drama': [18],
            'horror': [27],
            'romance': [10749],
            'sci-fi': [878],
            'thriller': [53],
            'documentary': [99],
            'animation': [16],
            'family': [10751]
          };
          return genreMap[category]?.includes(id);
        });
      });
  }
}

// Main recommendation engine
class RecommendationEngine {
  constructor() {
    this.similarityEngine = new ContentSimilarityEngine();
    this.behaviorAnalyzer = new UserBehaviorAnalyzer();
    this.collaborativeFilter = new CollaborativeFilter();
    this.trendingAnalyzer = new TrendingAnalyzer();
  }

  // Get personalized recommendations
  async getPersonalizedRecommendations(userId, profileId, contentList, options = {}) {
    const {
      limit = 20,
      includeTrending = true,
      includeSimilar = true,
      includeCollaborative = true,
      userWatchHistory = [],
      userRatings = []
    } = options;

    const recommendations = [];

    // 1. Content-based recommendations (similar to watched content)
    if (includeSimilar && userWatchHistory.length > 0) {
      const recentWatched = userWatchHistory.slice(-5); // Last 5 watched items
      recentWatched.forEach(watchedItem => {
        const similar = this.similarityEngine.findSimilarContent(watchedItem, contentList, 5);
        recommendations.push(...similar.map(item => ({
          item,
          score: this.similarityEngine.calculateSimilarity(watchedItem, item),
          type: 'similar',
          reason: `Similar to ${watchedItem.title || watchedItem.name}`
        })));
      });
    }

    // 2. User preference-based recommendations
    if (userWatchHistory.length > 0) {
      const preferences = this.behaviorAnalyzer.getUserPreferences(userWatchHistory);
      
      // Filter by favorite genres
      preferences.favoriteGenres.forEach(({ genreId }) => {
        const genreContent = contentList.filter(item => 
          item.genre_ids && item.genre_ids.includes(genreId)
        );
        recommendations.push(...genreContent.slice(0, 3).map(item => ({
          item,
          score: 0.8,
          type: 'preference',
          reason: 'Based on your favorite genres'
        })));
      });

      // Filter by favorite years
      preferences.favoriteYears.forEach(({ year }) => {
        const yearContent = contentList.filter(item => {
          const itemYear = new Date(item.release_date).getFullYear();
          return Math.abs(itemYear - year) <= 2; // Within 2 years
        });
        recommendations.push(...yearContent.slice(0, 2).map(item => ({
          item,
          score: 0.7,
          type: 'preference',
          reason: 'From your favorite time period'
        })));
      });
    }

    // 3. Collaborative filtering recommendations
    if (includeCollaborative && userRatings.length > 0) {
      // Add user ratings to collaborative filter
      userRatings.forEach(({ itemId, rating }) => {
        this.collaborativeFilter.addRating(userId, itemId, rating);
      });

      const collaborativeRecs = this.collaborativeFilter.getCollaborativeRecommendations(userId, 10);
      collaborativeRecs.forEach(({ itemId, score }) => {
        const item = contentList.find(c => c.id === itemId);
        if (item) {
          recommendations.push({
            item,
            score: score / 10, // Normalize score
            type: 'collaborative',
            reason: 'Recommended by users with similar taste'
          });
        }
      });
    }

    // 4. Trending recommendations
    if (includeTrending) {
      this.trendingAnalyzer.updateTrendingScores(contentList);
      const trending = this.trendingAnalyzer.getTrendingContent(contentList, 10);
      recommendations.push(...trending.map(item => ({
        item,
        score: item.trendingScore / 100, // Normalize score
        type: 'trending',
        reason: 'Trending now'
      })));
    }

    // 5. Remove duplicates and sort by score
    const uniqueRecommendations = new Map();
    recommendations.forEach(rec => {
      if (!uniqueRecommendations.has(rec.item.id)) {
        uniqueRecommendations.set(rec.item.id, rec);
      } else {
        // If duplicate, take the higher score
        const existing = uniqueRecommendations.get(rec.item.id);
        if (rec.score > existing.score) {
          uniqueRecommendations.set(rec.item.id, rec);
        }
      }
    });

    return Array.from(uniqueRecommendations.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  // Get recommendations by category
  async getCategoryRecommendations(category, contentList, limit = 20) {
    this.trendingAnalyzer.updateTrendingScores(contentList);
    return this.trendingAnalyzer.getTrendingByCategory(contentList, category, limit);
  }

  // Get "Because you watched" recommendations
  getBecauseYouWatched(watchedItem, contentList, limit = 10) {
    return this.similarityEngine.findSimilarContent(watchedItem, contentList, limit);
  }

  // Get "Continue watching" recommendations
  getContinueWatching(watchHistory, contentList, limit = 10) {
    return watchHistory
      .filter(entry => entry.progress && entry.progress < 0.9) // Less than 90% watched
      .sort((a, b) => new Date(b.lastWatched) - new Date(a.lastWatched))
      .slice(0, limit)
      .map(entry => {
        const item = contentList.find(c => c.id === entry.id);
        return item ? { ...item, progress: entry.progress } : null;
      })
      .filter(Boolean);
  }

  // Get "New releases" recommendations
  getNewReleases(contentList, limit = 20) {
    const now = new Date();
    const threeMonthsAgo = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
    
    return contentList
      .filter(item => {
        const releaseDate = new Date(item.release_date);
        return releaseDate >= threeMonthsAgo && releaseDate <= now;
      })
      .sort((a, b) => new Date(b.release_date) - new Date(a.release_date))
      .slice(0, limit);
  }

  // Get "Hidden gems" (highly rated but less popular)
  getHiddenGems(contentList, limit = 20) {
    return contentList
      .filter(item => {
        const rating = item.vote_average || 0;
        const popularity = item.popularity || 0;
        return rating >= 7.5 && popularity < 50; // High rating, low popularity
      })
      .sort((a, b) => (b.vote_average || 0) - (a.vote_average || 0))
      .slice(0, limit);
  }
}

// Export the main engine
const recommendationEngine = new RecommendationEngine();

export default recommendationEngine;
export {
  ContentSimilarityEngine,
  UserBehaviorAnalyzer,
  CollaborativeFilter,
  TrendingAnalyzer,
  RecommendationEngine
}; 