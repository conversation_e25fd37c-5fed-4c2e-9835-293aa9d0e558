<!DOCTYPE html>
<html>
<head>
    <title>LocalStorage Test</title>
</head>
<body>
    <h1>LocalStorage Persistence Test</h1>
    <button onclick="saveData()">Save Test Data</button>
    <button onclick="loadData()">Load Test Data</button>
    <button onclick="clearData()">Clear Test Data</button>
    <button onclick="checkWatchlist()">Check Watchlist</button>
    <div id="output"></div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        function saveData() {
            const testData = {
                timestamp: new Date().toISOString(),
                data: 'test watchlist item'
            };
            localStorage.setItem('torvie_watchlist_1', JSON.stringify([testData]));
            localStorage.setItem('test_persistence', 'This should persist');
            log('✅ Saved test data to localStorage');
        }

        function loadData() {
            const watchlist = localStorage.getItem('torvie_watchlist_1');
            const test = localStorage.getItem('test_persistence');
            log('📦 Watchlist data: ' + (watchlist || 'null'));
            log('📦 Test data: ' + (test || 'null'));
        }

        function clearData() {
            localStorage.removeItem('torvie_watchlist_1');
            localStorage.removeItem('test_persistence');
            log('🧹 Cleared test data');
        }

        function checkWatchlist() {
            log('🔍 Checking all localStorage keys:');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                log(`  ${key}: ${value}`);
            }
        }

        // Auto-load on page load
        window.onload = function() {
            log('🚀 Page loaded, checking localStorage...');
            loadData();
        };
    </script>
</body>
</html> 