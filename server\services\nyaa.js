import axios from 'axios';

async function getTrendingAnime() {
  try {
    console.log('🎌 Fetching trending anime from Nyaa.si...');
    
    // Get the main Nyaa.si page which shows recent/popular uploads
    const response = await axios.get('https://nyaa.si/?c=1_2&f=0&s=seeders&o=desc', {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.data) {
      console.log('No results from Nyaa.si trending');
      return [];
    }

    const html = response.data;
    const animeList = [];
    const seenTitles = new Set();

    // Parse the HTML table for anime entries
    const rows = html.match(/<tr[^>]*class="success"[^>]*>.*?<\/tr>/gs) || [];
    
    for (const row of rows.slice(0, 50)) { // Get more entries to filter from
      const nameMatch = row.match(/<a[^>]*href="[^"]*"[^>]*title="([^"]*)"[^>]*>([^<]+)<\/a>/);
      
      if (nameMatch) {
        let title = nameMatch[2].trim();
        
        // Clean up the title to extract anime name
        title = title
          .replace(/\[\w+\]/g, '') // Remove [group] tags
          .replace(/\([^)]*\)/g, '') // Remove (info) tags
          .replace(/\d{3,4}p/g, '') // Remove resolution
          .replace(/S\d+/g, '') // Remove season info
          .replace(/Episode?\s*\d+/ig, '') // Remove episode numbers
          .replace(/Ep\s*\d+/ig, '') // Remove Ep numbers
          .replace(/\s*-\s*\d+/g, '') // Remove - episode numbers
          .replace(/\s+/g, ' ') // Normalize spaces
          .trim();
        
        // Skip if title is too short or we've seen it
        if (title.length < 3 || seenTitles.has(title.toLowerCase())) {
          continue;
        }
        
        // Skip non-anime content
        if (title.toLowerCase().includes('movie') && !title.toLowerCase().includes('anime')) {
          continue;
        }
        
        seenTitles.add(title.toLowerCase());
        animeList.push({
          name: title,
          source: 'Nyaa.si'
        });
        
        // Stop when we have enough unique anime
        if (animeList.length >= 20) {
          break;
        }
      }
    }

    console.log(`🎌 Found ${animeList.length} trending anime from Nyaa.si`);
    return animeList;
    
  } catch (error) {
    console.error('Error fetching trending anime from Nyaa.si:', error.message);
    return [];
  }
}

async function searchNyaa(query, type = 'anime') {
  try {
    // Nyaa.si search API
    const searchUrl = `https://nyaa.si/?f=0&c=0_0&q=${encodeURIComponent(query)}&s=seeders&o=desc`;
    
    const response = await axios.get(searchUrl, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.data) {
      console.log('No results from Nyaa.si');
      return [];
    }

    const html = response.data;
    const torrents = [];

    // Parse the HTML table
    const rows = html.match(/<tr[^>]*class="success"[^>]*>.*?<\/tr>/gs) || [];
    
    for (const row of rows.slice(0, 10)) { // Take top 10
      const nameMatch = row.match(/<a[^>]*href="[^"]*"[^>]*>([^<]+)<\/a>/);
      const sizeMatch = row.match(/<td[^>]*>([0-9.]+ [KMGT]B)<\/td>/);
      const seedersMatch = row.match(/<td[^>]*>([0-9]+)<\/td>/g);
      
      if (nameMatch && sizeMatch && seedersMatch && seedersMatch.length >= 2) {
        const name = nameMatch[1].trim();
        const size = sizeMatch[1];
        const seeders = parseInt(seedersMatch[1].replace(/<[^>]*>/g, '')) || 0;
        const leechers = parseInt(seedersMatch[2].replace(/<[^>]*>/g, '')) || 0;
        
        // Extract magnet link if available
        const magnetMatch = row.match(/href="(magnet:[^"]+)"/);
        const magnetLink = magnetMatch ? magnetMatch[1] : `magnet:?xt=urn:btih:${generateHash(name)}&dn=${encodeURIComponent(name)}`;

        torrents.push({
          title: name,
          magnetLink: magnetLink,
          seeders: seeders,
          leechers: leechers,
          size: size,
          source: 'Nyaa.si'
        });
      }
    }

    // Sort by seeders descending
    torrents.sort((a, b) => b.seeders - a.seeders);
    
    console.log(`Found ${torrents.length} torrents from Nyaa.si`);
    return torrents;
    
  } catch (error) {
    console.error('Error searching Nyaa.si:', error.message);
    return [];
  }
}

// Simple hash generator for magnet links
function generateHash(title) {
  let hash = 0;
  for (let i = 0; i < title.length; i++) {
    const char = title.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16).padStart(40, '0');
}

export { searchNyaa, getTrendingAnime }; 