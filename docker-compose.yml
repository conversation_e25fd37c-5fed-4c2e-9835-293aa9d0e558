services:
  # Frontend service (React + Nginx)
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - NODE_ENV=production
    networks:
      - torvie-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend service (Node.js + Express)
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      - TMDB_API_KEY=${TMDB_API_KEY}
      - CORS_ORIGIN=http://localhost
    volumes:
      # Persist user data
      - torvie-storage:/app/storage
      # Persist media files
      - torvie-media:/app/downloads
    networks:
      - torvie-network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Development service (optional)
  frontend-dev:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    volumes:
      - ./client:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    networks:
      - torvie-network
    profiles:
      - dev
    command: npm run dev

  backend-dev:
    build:
      context: ./server
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3000"
    volumes:
      - ./server:/app
      - /app/node_modules
      - torvie-storage:/app/storage
      - torvie-media:/app/downloads
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret}
      - TMDB_API_KEY=${TMDB_API_KEY}
      - CORS_ORIGIN=http://localhost:5173
    networks:
      - torvie-network
    profiles:
      - dev
    command: npm run dev

volumes:
  torvie-storage:
    driver: local
  torvie-media:
    driver: local

networks:
  torvie-network:
    driver: bridge 