#!/usr/bin/env node

/**
 * Data Migration Script for Torvie
 * Migrates existing JSON file data to PostgreSQL database
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { logger } from '../utils/logger.js';
import { 
  query, 
  transaction, 
  testConnection, 
  createUser, 
  createProfile,
  addToWatchlist,
  saveWatchProgress 
} from '../utils/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const STORAGE_DIR = path.join(__dirname, '../storage');

/**
 * Read JSON file safely
 */
async function readJsonFile(filepath, defaultValue = {}) {
  try {
    const content = await fs.readFile(filepath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    if (error.code === 'ENOENT') {
      logger.info(`File not found: ${filepath}, using default value`);
      return defaultValue;
    }
    logger.error(`<PERSON>rror reading ${filepath}:`, error);
    throw error;
  }
}

/**
 * Check if file exists
 */
async function fileExists(filepath) {
  try {
    await fs.access(filepath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Migrate user profiles from JSON to database
 */
async function migrateProfiles() {
  const profilesFile = path.join(STORAGE_DIR, 'profiles.json');
  
  if (!(await fileExists(profilesFile))) {
    logger.info('No profiles.json file found, skipping profile migration');
    return { migrated: 0, skipped: 0 };
  }
  
  const profilesData = await readJsonFile(profilesFile, { users: {} });
  let migrated = 0;
  let skipped = 0;
  
  logger.info('Starting profile migration...');
  
  for (const [userId, userData] of Object.entries(profilesData.users)) {
    if (!userData.profiles || !Array.isArray(userData.profiles)) {
      continue;
    }
    
    for (const profile of userData.profiles) {
      try {
        // Check if profile already exists
        const existing = await query(
          'SELECT id FROM profiles WHERE user_id = $1 AND name = $2',
          [parseInt(userId), profile.name]
        );
        
        if (existing.rows.length > 0) {
          skipped++;
          continue;
        }
        
        // Create profile
        await createProfile({
          user_id: parseInt(userId),
          name: profile.name,
          avatar_url: profile.avatar || null,
          is_kids: profile.isKids || false
        });
        
        migrated++;
        logger.debug(`Migrated profile: ${profile.name} for user ${userId}`);
        
      } catch (error) {
        logger.error(`Failed to migrate profile ${profile.name} for user ${userId}:`, error);
      }
    }
  }
  
  logger.info(`Profile migration completed: ${migrated} migrated, ${skipped} skipped`);
  return { migrated, skipped };
}

/**
 * Migrate watchlists from JSON to database
 */
async function migrateWatchlists() {
  const watchlistsFile = path.join(STORAGE_DIR, 'watchlists.json');
  
  if (!(await fileExists(watchlistsFile))) {
    logger.info('No watchlists.json file found, skipping watchlist migration');
    return { migrated: 0, skipped: 0 };
  }
  
  const watchlistsData = await readJsonFile(watchlistsFile, { watchlists: {} });
  let migrated = 0;
  let skipped = 0;
  
  logger.info('Starting watchlist migration...');
  
  for (const [key, watchlist] of Object.entries(watchlistsData.watchlists)) {
    // Parse key format: "user_${userId}_profile_${profileId}"
    const match = key.match(/^user_(\d+)_profile_(\d+)$/);
    if (!match) {
      logger.warn(`Invalid watchlist key format: ${key}`);
      continue;
    }
    
    const [, userId, profileId] = match;
    
    if (!Array.isArray(watchlist)) {
      continue;
    }
    
    for (const item of watchlist) {
      try {
        // Check if item already exists in watchlist
        const existing = await query(
          'SELECT id FROM watchlist WHERE profile_id = $1 AND media_id = $2 AND media_type = $3',
          [parseInt(profileId), item.id, item.media_type || 'movie']
        );
        
        if (existing.rows.length > 0) {
          skipped++;
          continue;
        }
        
        // Add to watchlist
        await addToWatchlist({
          user_id: parseInt(userId),
          profile_id: parseInt(profileId),
          media_id: item.id,
          media_type: item.media_type || 'movie',
          title: item.title || item.name || 'Unknown Title',
          poster_path: item.poster_path || null,
          vote_average: item.vote_average || null,
          release_date: item.release_date || item.first_air_date || null
        });
        
        migrated++;
        logger.debug(`Migrated watchlist item: ${item.title || item.name} for profile ${profileId}`);
        
      } catch (error) {
        logger.error(`Failed to migrate watchlist item ${item.id} for profile ${profileId}:`, error);
      }
    }
  }
  
  logger.info(`Watchlist migration completed: ${migrated} migrated, ${skipped} skipped`);
  return { migrated, skipped };
}

/**
 * Migrate watch progress from JSON to database
 */
async function migrateWatchProgress() {
  const progressFile = path.join(STORAGE_DIR, 'watch-progress.json');
  
  if (!(await fileExists(progressFile))) {
    logger.info('No watch-progress.json file found, skipping progress migration');
    return { migrated: 0, skipped: 0 };
  }
  
  const progressData = await readJsonFile(progressFile, { progress: {} });
  let migrated = 0;
  let skipped = 0;
  
  logger.info('Starting watch progress migration...');
  
  for (const [key, progress] of Object.entries(progressData.progress)) {
    // Parse key format: "user_${userId}_profile_${profileId}_${mediaId}_${mediaType}"
    const match = key.match(/^user_(\d+)_profile_(\d+)_(\d+)_(.+)$/);
    if (!match) {
      logger.warn(`Invalid progress key format: ${key}`);
      continue;
    }
    
    const [, userId, profileId, mediaId, mediaType] = match;
    
    try {
      // Check if progress already exists
      const existing = await query(
        'SELECT id FROM watch_progress WHERE profile_id = $1 AND media_id = $2 AND media_type = $3',
        [parseInt(profileId), parseInt(mediaId), mediaType]
      );
      
      if (existing.rows.length > 0) {
        skipped++;
        continue;
      }
      
      // Save watch progress
      await saveWatchProgress({
        user_id: parseInt(userId),
        profile_id: parseInt(profileId),
        media_id: parseInt(mediaId),
        media_type: mediaType,
        progress_seconds: progress.currentTime || 0,
        duration_seconds: progress.duration || null,
        completed: progress.completed || false,
        season_number: progress.seasonNumber || null,
        episode_number: progress.episodeNumber || null
      });
      
      migrated++;
      logger.debug(`Migrated progress for media ${mediaId} (${mediaType}) for profile ${profileId}`);
      
    } catch (error) {
      logger.error(`Failed to migrate progress for ${key}:`, error);
    }
  }
  
  logger.info(`Watch progress migration completed: ${migrated} migrated, ${skipped} skipped`);
  return { migrated, skipped };
}

/**
 * Create backup of JSON files
 */
async function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(STORAGE_DIR, `backup-${timestamp}`);
  
  try {
    await fs.mkdir(backupDir, { recursive: true });
    
    const files = ['profiles.json', 'watchlists.json', 'watch-progress.json'];
    
    for (const file of files) {
      const sourcePath = path.join(STORAGE_DIR, file);
      const backupPath = path.join(backupDir, file);
      
      if (await fileExists(sourcePath)) {
        await fs.copyFile(sourcePath, backupPath);
        logger.info(`Backed up ${file} to ${backupPath}`);
      }
    }
    
    return backupDir;
  } catch (error) {
    logger.error('Failed to create backup:', error);
    throw error;
  }
}

/**
 * Main migration function
 */
async function migrateData() {
  try {
    logger.info('🚀 Starting data migration from JSON to PostgreSQL...');
    
    // Test database connection
    const connectionTest = await testConnection();
    if (!connectionTest.success) {
      throw new Error(`Database connection failed: ${connectionTest.error}`);
    }
    
    // Create backup
    const backupDir = await createBackup();
    logger.info(`✅ Backup created at: ${backupDir}`);
    
    // Run migrations in transaction
    const results = await transaction(async () => {
      const profileResults = await migrateProfiles();
      const watchlistResults = await migrateWatchlists();
      const progressResults = await migrateWatchProgress();
      
      return {
        profiles: profileResults,
        watchlists: watchlistResults,
        progress: progressResults
      };
    });
    
    // Summary
    const totalMigrated = results.profiles.migrated + results.watchlists.migrated + results.progress.migrated;
    const totalSkipped = results.profiles.skipped + results.watchlists.skipped + results.progress.skipped;
    
    logger.info('🎉 Data migration completed successfully!');
    logger.info(`📊 Summary:`);
    logger.info(`  - Profiles: ${results.profiles.migrated} migrated, ${results.profiles.skipped} skipped`);
    logger.info(`  - Watchlists: ${results.watchlists.migrated} migrated, ${results.watchlists.skipped} skipped`);
    logger.info(`  - Progress: ${results.progress.migrated} migrated, ${results.progress.skipped} skipped`);
    logger.info(`  - Total: ${totalMigrated} migrated, ${totalSkipped} skipped`);
    
    return results;
    
  } catch (error) {
    logger.error('❌ Data migration failed:', error);
    throw error;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateData()
    .then(() => {
      logger.info('Migration script completed successfully');
      process.exit(0);
    })
    .catch(error => {
      logger.error('Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateData, migrateProfiles, migrateWatchlists, migrateWatchProgress };
