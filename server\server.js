import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import { assertEnv } from './middleware/requireEnv.js';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import searchRouter from './routes/search.js';
import streamApiRouter from './api/stream.js';
import mediaRouter from './routes/media.js';
import tmdbRouter from './routes/tmdb.js';
import userDataRouter from './routes/userData.js';
import NodeCache from 'node-cache';
import authRouter, { authMiddleware } from './routes/auth.js';
import comprehensiveSearchRouter from './routes/searchComprehensive.js';
import advancedSearchRouter from './routes/searchAdvanced.js';
import liveTvRouter from './routes/liveTv.js';

// Load environment variables
dotenv.config();

// Validate required env vars early – fail fast if mis-configured
assertEnv(['JWT_SECRET', 'TMDB_API_KEY']);

const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET;

const app = express();

// Disable ETag generation globally
app.disable('etag');

// Robust CORS configuration for Docker and local development
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173', 
    'http://localhost', // Docker frontend
    'http://localhost:80', // Docker frontend alternative
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Middleware
app.use(express.json());

// ------------------------------------------------------------------------
// Optional security headers & gzip compression (disabled by setting env vars)
// ------------------------------------------------------------------------
if (process.env.ENABLE_SECURITY_HEADERS !== 'false') {
  app.use(helmet({
    // Disable policies that require HTTPS or break local dev
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false,
  }));
}

if (process.env.ENABLE_COMPRESSION !== 'false') {
  app.use(compression({
    // Skip compressing already-compressed video streams
    filter: (req, res) => {
      const type = res.getHeader('Content-Type') || '';
      if (typeof type === 'string' && type.startsWith('video/')) {
        return false;
      }
      // Fallback to default filter for text/json etc.
      return compression.filter(req, res);
    },
    // Slightly larger threshold to avoid CPU work on tiny payloads
    threshold: 1024
  }));
}

// ---------------------------------------------------------
// Optional rate-limiting for expensive endpoints (search &
// torrent-start). Enabled by default; set ENABLE_RATE_LIMIT
// to "false" to disable during local stress testing.
// ---------------------------------------------------------
if (process.env.ENABLE_RATE_LIMIT !== 'false') {
  const commonLimiterOpts = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,                 // 100 requests per IP
    standardHeaders: true,    // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false      // Disable deprecated `X-RateLimit-*` headers
  };

  // Apply to search aggregator
  const searchLimiter = rateLimit(commonLimiterOpts);
  app.use('/api/search', searchLimiter);

  // Apply to torrent start endpoint only (not video byte-range GETs)
  const streamStartLimiter = rateLimit(commonLimiterOpts);
  app.use('/api/stream/start', streamStartLimiter);
}

// TMDB API configuration
const TMDB_API_KEY = process.env.TMDB_API_KEY;
const TMDB_BASE_URL = 'https://api.themoviedb.org/3';

// --------------------
// Simple in-memory cache for external API calls (10-min TTL)
// --------------------
const apiCache = new NodeCache({ stdTTL: 600, checkperiod: 120 });

async function fetchWithCache(cacheKey, url) {
  const cached = apiCache.get(cacheKey);
  if (cached) return cached;
  const resp = await fetch(url);
  if (!resp.ok) throw new Error(`TMDB API error: ${resp.status} ${resp.statusText}`);
  const data = await resp.json();
  apiCache.set(cacheKey, data);
  return data;
}

// Mount PUBLIC API routes (BEFORE auth middleware)
app.use('/api/auth', authRouter);
app.use('/api/live-tv', liveTvRouter);

// Middleware to set TMDB API key and base URL for /api/media routes
app.use('/api/media', (req, res, next) => {
  req.TMDB_API_KEY = TMDB_API_KEY;
  req.TMDB_BASE_URL = TMDB_BASE_URL;
  next();
});
app.use('/api/media', mediaRouter);

// New consolidated TMDB proxy router (PUBLIC - movies, trending, etc.)
app.use('/api', tmdbRouter); // exposes paths like /api/trending/movies, /api/movies/:id, etc.

// TMDB Discover Anime endpoint - strict Japanese anime filtering (PUBLIC)
app.get('/api/discover-anime', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    
    if (!TMDB_API_KEY) {
      return res.status(500).json({ error: 'TMDB API key not configured' });
    }
    
    console.log(`🎌 Discovering anime page ${page} using TMDB anime keyword...`);
    
    // Use TMDB's dedicated anime keyword (210024) - single page for reliable infinite scroll
    let allResults = [];
    
    try {
      const animeKeywordUrl = `${TMDB_BASE_URL}/discover/tv?api_key=${TMDB_API_KEY}&with_keywords=210024&sort_by=popularity.desc&page=${page}&include_adult=false`;
      const animeResponse = await fetch(animeKeywordUrl);
      
      if (animeResponse.ok) {
        const animeData = await animeResponse.json();
        console.log(`🎌 Found ${animeData.results?.length || 0} anime shows from TMDB page ${page}`);
        allResults.push(...(animeData.results || []));
      }
    } catch (error) {
      console.error('🎌 Anime keyword search page failed:', error.message);
    }
    
    // Simple filtering & response (skip advanced sorting to keep patch small)
    res.json({ page, results: allResults, total_pages: 1, total_results: allResults.length });
  } catch (error) {
    console.error('🎌 Error discovering anime:', error);
    res.status(500).json({ error: 'Failed to discover anime', details: error.message });
  }
});

// Apply auth middleware AFTER public routes
app.use(authMiddleware);

// Mount PROTECTED API routes (AFTER auth middleware)
app.use('/api/stream', streamApiRouter);
app.use('/api/user-data', userDataRouter);

// Legacy inline TMDB endpoints were removed in favour of router in routes/tmdb.js

// Already mounted earlier, ensure single mount present

app.use('/api/search-comprehensive', comprehensiveSearchRouter);
app.use('/api/search-advanced', advancedSearchRouter);

// Health check endpoint (public)
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Favicon endpoint to prevent CORS errors
app.get('/favicon.ico', (req, res) => {
  // Return a simple SVG favicon with proper CORS headers
  const svgFavicon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <text y=".9em" font-size="90">🎬</text>
  </svg>`;
  
  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.send(svgFavicon);
});

// Catch-all 404 handler (must be after all other routes)
app.use((req, res) => {
  res.status(404).json({ error: 'Not found', path: req.originalUrl });
});

// Global error handler (must be last)
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  res.status(500).json({ error: 'Internal server error', details: err.message });
});

// Start server (skip when running under automated test)
if (process.env.NODE_ENV !== 'test') {
  app.listen(PORT, () => {
    console.log(`🚀 Torvie server running on http://localhost:${PORT}`);
  });
}

export default app;