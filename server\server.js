import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import { assertEnv } from './middleware/requireEnv.js';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import pinoHttp from 'pino-http';
import NodeCache from 'node-cache';
import { logger } from './utils/logger.js';
import { testConnection as testDBConnection } from './utils/database.js';
import { getCacheStats } from './utils/cache.js';
import { sanitizeInput, preventPathTraversal } from './middleware/validation.js';

// Import routes
import searchRouter from './routes/search.js';
import streamApiRouter from './api/stream.js';
import mediaRouter from './routes/media.js';
import tmdbRouter from './routes/tmdb.js';
import userDataRouter from './routes/userData.js';
import authRouter, { authMiddleware } from './routes/auth.js';
import comprehensiveSearchRouter from './routes/searchComprehensive.js';
import advancedSearchRouter from './routes/searchAdvanced.js';
import liveTvRouter from './routes/liveTv.js';
import healthRouter from './routes/health.js';

// Load environment variables
dotenv.config();

// Validate required env vars early – fail fast if mis-configured
assertEnv(['JWT_SECRET', 'TMDB_API_KEY']);

// Import database utilities
import { testConnection, healthCheck } from './utils/database.js';
import { runMigrations } from './scripts/migrate.js';

const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET;

const app = express();

// Disable ETag generation globally
app.disable('etag');

// HTTP request logging with Pino
app.use(pinoHttp({
  logger,
  customLogLevel: (req, res, err) => {
    if (res.statusCode >= 400 && res.statusCode < 500) {
      return 'warn';
    }
    if (res.statusCode >= 500 || err) {
      return 'error';
    }
    return 'info';
  },
  customSuccessMessage: (req, res) => {
    return `${req.method} ${req.url} - ${res.statusCode}`;
  },
  customErrorMessage: (req, res, err) => {
    return `${req.method} ${req.url} - ${res.statusCode} - ${err.message}`;
  }
}));

// Robust CORS configuration for Docker and local development
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173', 
    'http://localhost', // Docker frontend
    'http://localhost:80', // Docker frontend alternative
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Security middleware
app.use(preventPathTraversal);
app.use(sanitizeInput);

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ------------------------------------------------------------------------
// Security headers & gzip compression
// ------------------------------------------------------------------------
if (process.env.ENABLE_SECURITY_HEADERS !== 'false') {
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https://image.tmdb.org", "https://*.youtube.com"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'", "https://api.themoviedb.org"],
        frameSrc: ["'self'", "https://www.youtube.com"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
      },
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }));
}

if (process.env.ENABLE_COMPRESSION !== 'false') {
  app.use(compression({
    // Skip compressing already-compressed video streams
    filter: (req, res) => {
      const type = res.getHeader('Content-Type') || '';
      if (typeof type === 'string' && type.startsWith('video/')) {
        return false;
      }
      // Fallback to default filter for text/json etc.
      return compression.filter(req, res);
    },
    // Slightly larger threshold to avoid CPU work on tiny payloads
    threshold: 1024
  }));
}

// ---------------------------------------------------------
// Rate limiting for expensive endpoints
// ---------------------------------------------------------
if (process.env.ENABLE_RATE_LIMIT !== 'false') {
  const commonLimiterOpts = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,                 // 100 requests per IP
    standardHeaders: true,    // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false      // Disable deprecated `X-RateLimit-*` headers
  };

  // Apply to search aggregator
  const searchLimiter = rateLimit(commonLimiterOpts);
  app.use('/api/search', searchLimiter);

  // Apply to torrent start endpoint only (not video byte-range GETs)
  const streamStartLimiter = rateLimit(commonLimiterOpts);
  app.use('/api/stream/start', streamStartLimiter);

  // Stricter rate limiting for auth endpoints
  const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10,                  // 10 attempts per IP
    standardHeaders: true,
    legacyHeaders: false
  });
  app.use('/api/auth', authLimiter);
}

// TMDB API configuration
const TMDB_API_KEY = process.env.TMDB_API_KEY;
const TMDB_BASE_URL = 'https://api.themoviedb.org/3';

// --------------------
// Simple in-memory cache for external API calls (10-min TTL)
// --------------------
const apiCache = new NodeCache({ 
  stdTTL: 600, 
  checkperiod: 120,
  maxKeys: 1000 // Prevent memory leaks
});

async function fetchWithCache(cacheKey, url) {
  const cached = apiCache.get(cacheKey);
  if (cached) return cached;
  const resp = await fetch(url);
  if (!resp.ok) throw new Error(`TMDB API error: ${resp.status} ${resp.statusText}`);
  const data = await resp.json();
  apiCache.set(cacheKey, data);
  return data;
}

// Mount PUBLIC API routes (BEFORE auth middleware)
app.use('/api/auth', authRouter);
app.use('/api/live-tv', liveTvRouter);
app.use('/api/health', healthRouter);

// Middleware to set TMDB API key and base URL for /api/media routes
app.use('/api/media', (req, res, next) => {
  req.TMDB_API_KEY = TMDB_API_KEY;
  req.TMDB_BASE_URL = TMDB_BASE_URL;
  next();
});
app.use('/api/media', mediaRouter);

// New consolidated TMDB proxy router (PUBLIC - movies, trending, etc.)
app.use('/api', tmdbRouter); // exposes paths like /api/trending/movies, /api/movies/:id, etc.

// TMDB Discover Anime endpoint - strict Japanese anime filtering (PUBLIC)
app.get('/api/discover-anime', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    
    if (!TMDB_API_KEY) {
      return res.status(500).json({ error: 'TMDB API key not configured' });
    }
    
    logger.info(`🎌 Discovering anime page ${page} using TMDB anime keyword...`);
    
    // Use TMDB's dedicated anime keyword (210024) - single page for reliable infinite scroll
    let allResults = [];
    
    try {
      const animeKeywordUrl = `${TMDB_BASE_URL}/discover/tv?api_key=${TMDB_API_KEY}&with_keywords=210024&sort_by=popularity.desc&page=${page}&include_adult=false`;
      const animeResponse = await fetch(animeKeywordUrl);
      
      if (animeResponse.ok) {
        const animeData = await animeResponse.json();
        logger.info(`🎌 Found ${animeData.results?.length || 0} anime shows from TMDB page ${page}`);
        allResults.push(...(animeData.results || []));
      }
    } catch (error) {
      logger.error('🎌 Anime keyword search page failed:', error.message);
    }
    
    // Simple filtering & response (skip advanced sorting to keep patch small)
    res.json({ page, results: allResults, total_pages: 1, total_results: allResults.length });
  } catch (error) {
    logger.error('🎌 Error discovering anime:', error);
    res.status(500).json({ error: 'Failed to discover anime', details: error.message });
  }
});

// Apply auth middleware AFTER public routes
app.use(authMiddleware);

// Mount PROTECTED API routes (AFTER auth middleware)
app.use('/api/stream', streamApiRouter);
app.use('/api/user-data', userDataRouter);

// Already mounted earlier, ensure single mount present
app.use('/api/search-comprehensive', comprehensiveSearchRouter);
app.use('/api/search-advanced', advancedSearchRouter);

// Health check endpoint (public)
app.get('/api/health', async (req, res) => {
  try {
    const dbStatus = await testDBConnection();
    const cacheStats = await getCacheStats();
    
    res.json({ 
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: dbStatus ? 'connected' : 'disconnected',
      cache: cacheStats.connected ? 'connected' : 'disconnected',
      uptime: process.uptime(),
      memory: process.memoryUsage()
    });
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(500).json({ 
      status: 'error',
      error: error.message 
    });
  }
});

// System status endpoint (protected)
app.get('/api/system/status', async (req, res) => {
  try {
    const cacheStats = await getCacheStats();
    const dbStatus = await testDBConnection();
    
    res.json({
      database: {
        connected: dbStatus,
        status: dbStatus ? 'healthy' : 'unhealthy'
      },
      cache: {
        connected: cacheStats.connected,
        memory: cacheStats.memory,
        status: cacheStats.connected ? 'healthy' : 'unhealthy'
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      }
    });
  } catch (error) {
    logger.error('System status check failed:', error);
    res.status(500).json({ error: 'Failed to get system status' });
  }
});

// Favicon endpoint to prevent CORS errors
app.get('/favicon.ico', (req, res) => {
  // Return a simple SVG favicon with proper CORS headers
  const svgFavicon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <text y=".9em" font-size="90">🎬</text>
  </svg>`;
  
  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.send(svgFavicon);
});

// Catch-all 404 handler (must be after all other routes)
app.use((req, res) => {
  logger.warn({
    event: '404_not_found',
    path: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  res.status(404).json({ error: 'Not found', path: req.originalUrl });
});

// Global error handler (must be last)
app.use((err, req, res, next) => {
  logger.error({
    event: 'unhandled_error',
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  res.status(500).json({ 
    error: 'Internal server error', 
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully...');
  
  try {
    // Close database connections
    const { closePool } = await import('./utils/database.js');
    await closePool();
    
    // Close Redis connections
    const { closeRedis } = await import('./utils/cache.js');
    await closeRedis();
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
});

// Database initialization and server startup
async function startServer() {
  try {
    // Test database connection
    logger.info('🔍 Testing database connection...');
    const connectionTest = await testConnection();

    if (!connectionTest.success) {
      logger.error('❌ Database connection failed:', connectionTest.error);

      if (process.env.NODE_ENV === 'production') {
        process.exit(1);
      } else {
        logger.warn('⚠️ Continuing without database in development mode');
      }
    } else {
      logger.info('✅ Database connection successful');

      // Run database migrations
      if (process.env.AUTO_MIGRATE !== 'false') {
        logger.info('🔄 Running database migrations...');
        await runMigrations();
        logger.info('✅ Database migrations completed');
      }

      // Log database health
      const health = await healthCheck();
      logger.info('📊 Database health:', {
        status: health.status,
        duration: health.duration,
        pool: health.pool
      });
    }

    // Start HTTP server
    app.listen(PORT, () => {
      logger.info(`🚀 Torvie server running on http://localhost:${PORT}`);
      logger.info(`📋 Health check available at http://localhost:${PORT}/api/health`);
    });

  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start server (skip when running under automated test)
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

export default app;