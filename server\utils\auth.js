import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { logger, securityLogger } from './logger.js';

// Password hashing configuration
const SALT_ROUNDS = 12;
const JWT_EXPIRY = '12h';
const REFRESH_TOKEN_EXPIRY = '7d';

// Hash password with bcrypt
export const hashPassword = async (password) => {
  try {
    const salt = await bcrypt.genSalt(SALT_ROUNDS);
    return await bcrypt.hash(password, salt);
  } catch (error) {
    logger.error({ error: error.message, context: 'hashPassword' });
    throw new Error('Password hashing failed');
  }
};

// Verify password against hash
export const verifyPassword = async (password, hash) => {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    logger.error({ error: error.message, context: 'verifyPassword' });
    return false;
  }
};

// Generate JWT token
export const generateToken = (payload, secret, expiresIn = JWT_EXPIRY) => {
  try {
    return jwt.sign(payload, secret, { 
      expiresIn,
      issuer: 'torvie-server',
      audience: 'torvie-client'
    });
  } catch (error) {
    logger.error({ error: error.message, context: 'generateToken' });
    throw new Error('Token generation failed');
  }
};

// Verify JWT token
export const verifyToken = (token, secret) => {
  try {
    return jwt.verify(token, secret, {
      issuer: 'torvie-server',
      audience: 'torvie-client'
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      securityLogger.warn({ 
        event: 'token_expired',
        token: token.substring(0, 20) + '...'
      });
    } else if (error.name === 'JsonWebTokenError') {
      securityLogger.warn({ 
        event: 'invalid_token',
        token: token.substring(0, 20) + '...'
      });
    }
    throw error;
  }
};

// Generate refresh token
export const generateRefreshToken = (userId, secret) => {
  return generateToken(
    { userId, type: 'refresh' },
    secret,
    REFRESH_TOKEN_EXPIRY
  );
};

// Validate password strength
export const validatePasswordStrength = (password) => {
  const errors = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Rate limiting for authentication attempts
export const createAuthRateLimiter = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  const attempts = new Map();
  
  return (req, res, next) => {
    const key = req.ip;
    const now = Date.now();
    const userAttempts = attempts.get(key) || { count: 0, resetTime: now + windowMs };
    
    // Reset if window has passed
    if (now > userAttempts.resetTime) {
      userAttempts.count = 0;
      userAttempts.resetTime = now + windowMs;
    }
    
    // Check if limit exceeded
    if (userAttempts.count >= maxAttempts) {
      securityLogger.warn({
        event: 'auth_rate_limit_exceeded',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(429).json({
        success: false,
        error: 'Too many authentication attempts. Please try again later.',
        retryAfter: Math.ceil((userAttempts.resetTime - now) / 1000)
      });
    }
    
    // Increment attempt counter
    userAttempts.count++;
    attempts.set(key, userAttempts);
    
    // Clean up old entries periodically
    if (attempts.size > 1000) {
      for (const [k, v] of attempts.entries()) {
        if (now > v.resetTime) {
          attempts.delete(k);
        }
      }
    }
    
    next();
  };
};

// Log authentication events
export const logAuthEvent = (event, userId, ip, success = true, details = {}) => {
  securityLogger.info({
    event,
    userId,
    ip,
    success,
    userAgent: details.userAgent,
    ...details
  });
};

// Validate username format
export const validateUsername = (username) => {
  const errors = [];
  
  if (username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }
  
  if (username.length > 50) {
    errors.push('Username must be less than 50 characters');
  }
  
  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    errors.push('Username can only contain letters, numbers, underscores, and hyphens');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
