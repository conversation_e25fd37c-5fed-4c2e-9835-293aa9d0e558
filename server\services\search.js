// server/services/search.js
// This module now uses multiple torrent sources to provide comprehensive search results.

// IMPORTANT: This service ONLY returns torrent data for playback
// It should NEVER be used for display elements (posters, titles, cast, info)
// Display data should come exclusively from TMDB API endpoints

// 1. Import Dependencies
// ----------------------
import axios from 'axios';
import { searchEZTV } from './eztv.js';
import { search1337x } from './1337x.js';
import { searchPirateBay } from './piratebay.js';
import { searchNyaa } from './nyaa.js';
import { searchRuTracker } from './rutracker.js';

// 2. Define YTS API Configuration
// -------------------------------
const YTS_API_BASE_URL = 'https://yts.mx/api/v2/';

/**
 * The main search function that fetches data from multiple torrent sources.
 * @param {string} query The search query.
 * @param {string} type The type of search ('movie', 'tv', 'anime', or 'general').
 * @param {object} opts Additional options (season, episode, allEpisodes).
 * @returns {Promise<Array>} A combined, sorted list of torrents from multiple sources.
 */
export async function searchTorrents(query, type = 'general', opts = {}) {
    console.log(`🎬 Starting multi-source search for "${query}" (type: ${type})...`);

    let allResults = [];
    const { season, episode, allEpisodes } = opts;

    try {
        // Search different sources based on content type
        const searchPromises = [];

        if (type === 'tv') {
            // TV Shows: EZTV (best for TV), 1337x, The Pirate Bay
            console.log('🎬 Searching TV sources: EZTV, 1337x, The Pirate Bay');
            searchPromises.push(
                searchEZTV(query).catch(err => {
                    console.error('🎬 EZTV search failed:', err.message);
                    return [];
                }),
                search1337x(query, 'tv').catch(err => {
                    console.error('🎬 1337x search failed:', err.message);
                    return [];
                }),
                searchPirateBay(query, 'tv').catch(err => {
                    console.error('🎬 Pirate Bay search failed:', err.message);
                    return [];
                })
            );
        } else if (type === 'anime') {
            // Anime: Nyaa.si (best for anime), 1337x, The Pirate Bay
            console.log('🎬 Searching anime sources: Nyaa.si, 1337x, The Pirate Bay');
            searchPromises.push(
                searchNyaa(query, 'anime').catch(err => {
                    console.error('🎬 Nyaa.si search failed:', err.message);
                    return [];
                }),
                search1337x(query, 'anime').catch(err => {
                    console.error('🎬 1337x search failed:', err.message);
                    return [];
                }),
                searchPirateBay(query, 'anime').catch(err => {
                    console.error('🎬 Pirate Bay search failed:', err.message);
                    return [];
                })
            );
        } else {
            // Movies: YTS (best for movies), 1337x, The Pirate Bay
            console.log('🎬 Searching movie sources: YTS, 1337x, The Pirate Bay');
            searchPromises.push(
                searchYTS(query).catch(err => {
                    console.error('🎬 YTS search failed:', err.message);
                    return [];
                }),
                search1337x(query, 'movie').catch(err => {
                    console.error('🎬 1337x search failed:', err.message);
                    return [];
                }),
                searchPirateBay(query, 'movie').catch(err => {
                    console.error('🎬 Pirate Bay search failed:', err.message);
                    return [];
                })
            );
        }

        // Wait for all searches to complete
        const results = await Promise.allSettled(searchPromises);
        
        // Combine all successful results
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && Array.isArray(result.value)) {
                console.log(`🎬 Source ${index} returned ${result.value.length} results`);
                allResults = allResults.concat(result.value);
            } else {
                console.log(`🎬 Search source ${index} failed:`, result.reason?.message || 'Unknown error');
            }
        });

        console.log(`🎬 Total results before deduplication: ${allResults.length}`);

        // Remove duplicates based on title similarity
        allResults = removeDuplicates(allResults);

        console.log(`🎬 Total results after deduplication: ${allResults.length}`);

        // Sort by seeders (highest first) to prioritize the best torrents
        allResults.sort((a, b) => b.seeders - a.seeders);



        // --- TV: Parse and filter by season/episode if requested ---
        if (type === 'tv') {
            console.log('🎬 Processing TV show results...');
            // Helper regexes
            const epRegexes = [
                /s(\d{1,2})[ ._-]*e(\d{1,2})/i, // S01E01
                /(\d{1,2})x(\d{1,2})/i,         // 1x01
                /season[ ._-]?(\d{1,2})[ ._-]*episode[ ._-]?(\d{1,2})/i, // season 1 episode 1
                /ep?i?s?([0-9]{1,2})[^\d]/i,     // e01, ep01
                /episode[ ._-]?(\d{1,2})/i,      // episode 1
                /part[ ._-]?(\d{1,2})/i          // part 1
            ];
            // Parse season/episode for each torrent
            allResults.forEach(t => {
                let found = false;
                for (const rx of epRegexes) {
                    const m = t.title.match(rx);
                    if (m) {
                        t.parsedSeason = parseInt(m[1], 10) || 1;
                        t.parsedEpisode = parseInt(m[2] || m[1], 10) || 1;
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    t.parsedSeason = null;
                    t.parsedEpisode = null;
                }
            });
            
            const parsedResults = allResults.filter(t => t.parsedSeason && t.parsedEpisode);
            console.log(`🎬 Found ${parsedResults.length} torrents with parsed season/episode info`);
            
            // If allEpisodes is set, return a map of best torrents for each episode
            if (allEpisodes) {
                console.log('🎬 Returning all episodes map...');
                // Map: { 'S1E1': { ...torrent } }
                const bestByEp = {};
                for (const t of allResults) {
                    if (t.parsedSeason && t.parsedEpisode) {
                        const key = `S${t.parsedSeason}E${t.parsedEpisode}`;
                        if (!bestByEp[key] || t.seeders > bestByEp[key].seeders) {
                            bestByEp[key] = t;
                        }
                    }
                }
                console.log(`🎬 Returning ${Object.keys(bestByEp).length} unique episodes`);
                return bestByEp;
            }
            // If season/episode specified, return best match
            if (season && episode) {
                const matches = allResults.filter(t => t.parsedSeason === parseInt(season) && t.parsedEpisode === parseInt(episode));
                console.log(`🎬 Found ${matches.length} matches for S${season}E${episode}`);
                if (matches.length > 0) {
                    // Return the best (highest seeders)
                    return [matches[0]];
                }
                // Fallback: try to find any episode 1 if season not found
                if (parseInt(episode) === 1) {
                    const anyFirstEp = allResults.filter(t => t.parsedEpisode === 1);
                    console.log(`🎬 Fallback: found ${anyFirstEp.length} first episodes`);
                    if (anyFirstEp.length > 0) return [anyFirstEp[0]];
                }
                // Fallback: return empty
                console.log('🎬 No matches found, returning empty array');
                return [];
            }
            // If only season specified, return best S{season}E01
            if (season && !episode) {
                const matches = allResults.filter(t => t.parsedSeason === parseInt(season) && t.parsedEpisode === 1);
                console.log(`🎬 Found ${matches.length} matches for S${season}E01`);
                if (matches.length > 0) return [matches[0]];
                return [];
            }
            // If only episode specified, return best S01E{episode}
            if (!season && episode) {
                const matches = allResults.filter(t => t.parsedSeason === 1 && t.parsedEpisode === parseInt(episode));
                console.log(`🎬 Found ${matches.length} matches for S01E${episode}`);
                if (matches.length > 0) return [matches[0]];
                return [];
            }
            // Default: return best S01E01
            const s1e1 = allResults.filter(t => t.parsedSeason === 1 && t.parsedEpisode === 1);
            console.log(`🎬 Found ${s1e1.length} matches for S01E01`);
            if (s1e1.length > 0) return [s1e1[0]];
            console.log('🎬 No S01E01 found, returning empty array');
            return [];
        }

        // --- Movies: just return best by seeders ---
        console.log(`🎬 Returning ${allResults.length} movie results`);
        return allResults;

    } catch (error) {
        console.error(`🎬 Error in multi-source search: ${error.message}`);
        return [];
    }
}

/**
 * Search YTS API for movies
 */
async function searchYTS(query) {
    try {
        const response = await axios.get(`${YTS_API_BASE_URL}list_movies.json`, {
            params: {
                query_term: query,
                limit: 20,
                sort_by: 'seeds',
            }
        });

        if (!response.data || response.data.data.movie_count === 0) {
            console.log(`No results found on YTS for "${query}".`);
            return [];
        }

        const movies = response.data.data.movies;
        const allTorrents = movies.flatMap(movie => {
            return movie.torrents ? movie.torrents.map(torrent => {
                const title = `${movie.title_long} [${torrent.quality}] [YTS.MX]`;
                const magnetLink = `magnet:?xt=urn:btih:${torrent.hash}&dn=${encodeURIComponent(title)}&tr=udp://open.demonii.com:1337/announce&tr=udp://tracker.openbittorrent.com:80&tr=udp://tracker.opentrackr.org:1337&tr=udp://tracker.coppersurfer.tk:6969&tr=udp://explodie.org:6969`;
                
                // Validate magnet link
                if (!torrent.hash || torrent.hash.length !== 40) {
                    console.warn(`Invalid hash for torrent: ${title}`);
                    return null;
                }
                
                return {
                    title: title,
                    seeders: torrent.seeds || 0,
                    leechers: torrent.peers || 0,
                    size: torrent.size,
                    magnetLink: magnetLink,
                    source: 'YTS',
                    quality: torrent.quality,
                    hash: torrent.hash
                };
            }).filter(Boolean) : []; // Remove null entries
        });

        // Filter out torrents with 0 seeders
        const activeTorrents = allTorrents.filter(t => t.seeders > 0);
        activeTorrents.sort((a, b) => b.seeders - a.seeders);
        
        console.log(`Found ${allTorrents.length} torrents from YTS (${activeTorrents.length} with seeders)`);
        return activeTorrents;

    } catch (error) {
        console.error(`Error fetching data from YTS API: ${error.message}`);
        return [];
    }
}

/**
 * Remove duplicate torrents based on title similarity
 */
function removeDuplicates(torrents) {
    const seen = new Set();
    const unique = [];

    for (const torrent of torrents) {
        // Create a normalized key for comparison
        const normalizedTitle = torrent.title.toLowerCase()
            .replace(/[^\w\s]/g, '') // Remove special characters
            .replace(/\s+/g, ' ') // Normalize spaces
            .trim();

        if (!seen.has(normalizedTitle)) {
            seen.add(normalizedTitle);
            unique.push(torrent);
        }
    }

    return unique;
} 