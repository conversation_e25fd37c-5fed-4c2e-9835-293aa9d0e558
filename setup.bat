@echo off
echo ========================================
echo    Torvie - Setup Script
echo ========================================
echo.

echo Installing client dependencies...
cd client
call npm install
if %errorlevel% neq 0 (
    echo Error installing client dependencies
    pause
    exit /b 1
)

echo.
echo Installing server dependencies...
cd ../server
call npm install
if %errorlevel% neq 0 (
    echo Error installing server dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo To start the application:
echo 1. Open a new terminal and run: cd server && npm run dev
echo 2. Open another terminal and run: cd client && npm run dev
echo.
echo The application will be available at:
echo - Frontend: http://localhost:3000
echo - Backend:  http://localhost:3002
echo.
pause 