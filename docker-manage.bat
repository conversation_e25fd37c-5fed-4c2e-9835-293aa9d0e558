@echo off
echo Torvie Docker Management Script
echo =============================

if "%1"=="start" (
    echo Starting Torvie containers...
    docker-compose up -d
    echo.
    echo Containers started! Access the app at:
    echo Frontend: http://localhost
    echo Backend API: http://localhost:3000
    goto :end
)

if "%1"=="stop" (
    echo Stopping Torvie containers...
    docker-compose down
    echo Containers stopped.
    goto :end
)

if "%1"=="restart" (
    echo Restarting Torvie containers...
    docker-compose down
    docker-compose up -d
    echo.
    echo Containers restarted! Access the app at:
    echo Frontend: http://localhost
    echo Backend API: http://localhost:3000
    goto :end
)

if "%1"=="rebuild" (
    echo Rebuilding and starting Torvie containers...
    docker-compose down
    docker-compose up --build -d
    echo.
    echo Containers rebuilt and started! Access the app at:
    echo Frontend: http://localhost
    echo Backend API: http://localhost:3000
    goto :end
)

if "%1"=="logs" (
    echo Showing container logs...
    docker-compose logs -f
    goto :end
)

if "%1"=="status" (
    echo Container status:
    docker-compose ps
    goto :end
)

if "%1"=="clean" (
    echo Cleaning up Docker resources...
    docker-compose down -v
    docker system prune -f
    echo Cleanup complete.
    goto :end
)

echo Usage: docker-manage.bat [command]
echo.
echo Commands:
echo   start    - Start the containers
echo   stop     - Stop the containers
echo   restart  - Restart the containers
echo   rebuild  - Rebuild and start the containers
echo   logs     - Show container logs
echo   status   - Show container status
echo   clean    - Clean up Docker resources
echo.
echo Examples:
echo   docker-manage.bat start
echo   docker-manage.bat rebuild
echo   docker-manage.bat logs

:end 