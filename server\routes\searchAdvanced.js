import express from 'express';
import fetch from 'node-fetch';
import { async<PERSON>and<PERSON> } from '../middleware/asyncHandler.js';

const router = express.Router();
const TMDB_BASE_URL = 'https://api.themoviedb.org/3';

// Search suggestions endpoint
router.get('/search-suggestions', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  const { query } = req.query;

  if (!TMDB_API_KEY) {
    return res.status(500).json({ error: 'TMDB API key not configured' });
  }

  if (!query || query.trim() === '') {
    return res.json({ suggestions: [] });
  }

  console.log(`🔍 Getting search suggestions for: "${query}"`);

  try {
    // Get suggestions from TMDB multi-search
    const response = await fetch(
      `${TMDB_BASE_URL}/search/multi?api_key=${TMDB_API_KEY}&query=${encodeURIComponent(query)}&include_adult=false&page=1`
    );

    if (!response.ok) {
      throw new Error(`TMDB API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Extract titles and names for suggestions
    const suggestions = new Set();
    
    if (data.results) {
      data.results.slice(0, 10).forEach(item => {
        if (item.media_type === 'movie' && item.title) {
          suggestions.add(item.title);
        } else if (item.media_type === 'tv' && item.name) {
          suggestions.add(item.name);
        } else if (item.media_type === 'person' && item.name) {
          suggestions.add(item.name);
        }
      });
    }

    // Add some common search patterns
    const searchQuery = query.toLowerCase();
    if (searchQuery.includes('movie') || searchQuery.includes('film')) {
      suggestions.add('Action Movies');
      suggestions.add('Comedy Movies');
      suggestions.add('Drama Movies');
    } else if (searchQuery.includes('show') || searchQuery.includes('series')) {
      suggestions.add('TV Shows');
      suggestions.add('Drama Series');
      suggestions.add('Comedy Shows');
    } else if (searchQuery.includes('anime')) {
      suggestions.add('Popular Anime');
      suggestions.add('Action Anime');
      suggestions.add('Romance Anime');
    }

    const finalSuggestions = Array.from(suggestions).slice(0, 8);
    
    console.log(`🔍 Generated ${finalSuggestions.length} suggestions`);
    res.json({ suggestions: finalSuggestions });

  } catch (error) {
    console.error('🔍 Error getting search suggestions:', error);
    res.status(500).json({ error: 'Failed to get search suggestions' });
  }
}));

// Trending searches endpoint
router.get('/trending-searches', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;

  if (!TMDB_API_KEY) {
    return res.status(500).json({ error: 'TMDB API key not configured' });
  }

  console.log('🔥 Getting trending searches...');

  try {
    // Get trending movies and TV shows
    const [moviesResponse, showsResponse] = await Promise.all([
      fetch(`${TMDB_BASE_URL}/trending/movie/week?api_key=${TMDB_API_KEY}`),
      fetch(`${TMDB_BASE_URL}/trending/tv/week?api_key=${TMDB_API_KEY}`)
    ]);

    if (!moviesResponse.ok || !showsResponse.ok) {
      throw new Error('Failed to fetch trending content');
    }

    const [moviesData, showsData] = await Promise.all([
      moviesResponse.json(),
      showsResponse.json()
    ]);

    // Combine and extract trending titles
    const trending = new Set();
    
    // Add trending movies
    if (moviesData.results) {
      moviesData.results.slice(0, 5).forEach(movie => {
        if (movie.title) trending.add(movie.title);
      });
    }

    // Add trending TV shows
    if (showsData.results) {
      showsData.results.slice(0, 5).forEach(show => {
        if (show.name) trending.add(show.name);
      });
    }

    // Add some popular genres and categories
    const popularSearches = [
      'Action Movies',
      'Comedy Shows',
      'Drama Series',
      'Horror Movies',
      'Romance Films',
      'Sci-Fi Movies',
      'Thriller TV Shows',
      'Documentaries'
    ];

    popularSearches.forEach(search => trending.add(search));

    const finalTrending = Array.from(trending).slice(0, 12);
    
    console.log(`🔥 Generated ${finalTrending.length} trending searches`);
    res.json({ trending: finalTrending });

  } catch (error) {
    console.error('🔥 Error getting trending searches:', error);
    res.status(500).json({ error: 'Failed to get trending searches' });
  }
}));

// Personalized recommendations endpoint
router.get('/recommendations', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  const { userId, profileId } = req.query;

  if (!TMDB_API_KEY) {
    return res.status(500).json({ error: 'TMDB API key not configured' });
  }

  console.log(`🎯 Getting recommendations for user ${userId}, profile ${profileId}`);

  try {
    // For now, return popular content as recommendations
    // In a real implementation, this would analyze user watch history and preferences
    
    const [moviesResponse, showsResponse] = await Promise.all([
      fetch(`${TMDB_BASE_URL}/movie/popular?api_key=${TMDB_API_KEY}&page=1`),
      fetch(`${TMDB_BASE_URL}/tv/popular?api_key=${TMDB_API_KEY}&page=1`)
    ]);

    if (!moviesResponse.ok || !showsResponse.ok) {
      throw new Error('Failed to fetch recommendations');
    }

    const [moviesData, showsData] = await Promise.all([
      moviesResponse.json(),
      showsResponse.json()
    ]);

    // Combine and format recommendations
    const recommendations = [];
    
    if (moviesData.results) {
      moviesData.results.slice(0, 5).forEach(movie => {
        recommendations.push({
          ...movie,
          media_type: 'movie',
          title: movie.title
        });
      });
    }

    if (showsData.results) {
      showsData.results.slice(0, 5).forEach(show => {
        recommendations.push({
          ...show,
          media_type: 'tv',
          name: show.name
        });
      });
    }

    // Sort by popularity
    recommendations.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
    
    console.log(`🎯 Generated ${recommendations.length} recommendations`);
    res.json({ recommendations });

  } catch (error) {
    console.error('🎯 Error getting recommendations:', error);
    res.status(500).json({ error: 'Failed to get recommendations' });
  }
}));

// Advanced search with filters endpoint
router.get('/advanced', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  const { 
    query, 
    mediaType = 'all', 
    genre, 
    year, 
    rating, 
    language = 'en',
    sortBy = 'relevance',
    includeAdult = 'false',
    page = 1 
  } = req.query;

  if (!TMDB_API_KEY) {
    return res.status(500).json({ error: 'TMDB API key not configured' });
  }

  if (!query || query.trim() === '') {
    return res.status(400).json({ error: 'Search query is required' });
  }

  console.log(`🔍 Advanced search for: "${query}" with filters`);

  try {
    const searchPromises = [];
    const maxPages = 3;

    // Build search parameters
    const baseParams = {
      api_key: TMDB_API_KEY,
      query: encodeURIComponent(query.trim()),
      include_adult: includeAdult === 'true',
      language: language,
      page: page
    };

    // Add filters
    if (genre) baseParams.with_genres = genre;
    if (year) baseParams.year = year;
    if (rating) baseParams['vote_average.gte'] = rating;

    // Determine sort parameter
    let sortParam = 'popularity.desc';
    if (sortBy === 'rating') sortParam = 'vote_average.desc';
    else if (sortBy === 'date') sortParam = 'primary_release_date.desc';
    else if (sortBy === 'relevance') sortParam = 'popularity.desc';

    baseParams.sort_by = sortParam;

    // Search based on media type
    if (mediaType === 'all' || mediaType === 'movie') {
      for (let p = 1; p <= maxPages; p++) {
        const movieParams = { ...baseParams, page: p };
        searchPromises.push(
          fetch(`${TMDB_BASE_URL}/search/movie?${new URLSearchParams(movieParams)}`)
            .then(r => r.json())
            .then(data => ({ type: 'movie', data }))
            .catch(err => ({ type: 'movie', error: err.message }))
        );
      }
    }

    if (mediaType === 'all' || mediaType === 'tv') {
      for (let p = 1; p <= maxPages; p++) {
        const tvParams = { ...baseParams, page: p };
        searchPromises.push(
          fetch(`${TMDB_BASE_URL}/search/tv?${new URLSearchParams(tvParams)}`)
            .then(r => r.json())
            .then(data => ({ type: 'tv', data }))
            .catch(err => ({ type: 'tv', error: err.message }))
        );
      }
    }

    const results = await Promise.allSettled(searchPromises);
    let movies = [], shows = [];
    let totalMovieResults = 0, totalShowResults = 0;

    results.forEach(r => {
      if (r.status !== 'fulfilled' || !r.value.data) return;
      const { type, data } = r.value;
      
      if (!data.results) return;
      
      if (type === 'movie') {
        movies = movies.concat(data.results.map(item => ({ ...item, media_type: 'movie' })));
        totalMovieResults = Math.max(totalMovieResults, data.total_results || 0);
      } else if (type === 'tv') {
        shows = shows.concat(data.results.map(item => ({ ...item, media_type: 'tv' })));
        totalShowResults = Math.max(totalShowResults, data.total_results || 0);
      }
    });

    // Deduplicate and sort results
    const dedupe = arr => Array.from(arr.reduce((acc, cur) => {
      const existing = acc.get(cur.id);
      if (!existing || (cur.popularity || 0) > (existing.popularity || 0)) {
        acc.set(cur.id, cur);
      }
      return acc;
    }, new Map()).values());

    const finalMovies = dedupe(movies).sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
    const finalShows = dedupe(shows).sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
    
    // Combine results based on media type preference
    let allResults = [];
    if (mediaType === 'movie') {
      allResults = finalMovies;
    } else if (mediaType === 'tv') {
      allResults = finalShows;
    } else {
      allResults = [...finalMovies, ...finalShows].sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
    }

    // Apply rating filter if specified
    if (rating) {
      allResults = allResults.filter(item => (item.vote_average || 0) >= parseFloat(rating));
    }

    console.log(`🔍 Advanced search returned ${allResults.length} results`);
    
    res.json({
      success: true,
      query: query.trim(),
      results: allResults,
      movies: finalMovies,
      shows: finalShows,
      stats: {
        total: allResults.length,
        movies: finalMovies.length,
        shows: finalShows.length,
        totalMovieResults,
        totalShowResults
      },
      filters: {
        mediaType,
        genre,
        year,
        rating,
        language,
        sortBy,
        includeAdult: includeAdult === 'true'
      }
    });

  } catch (error) {
    console.error('🔍 Advanced search error:', error);
    res.status(500).json({ error: 'Advanced search failed' });
  }
}));

export default router; 