// Comprehensive browser diagnostics to identify localStorage clearing issues
export const runBrowserDiagnostics = () => {
  console.log('🔍 BROWSER DIAGNOSTICS - Identifying localStorage clearing causes...');
  console.log('='.repeat(80));
  
  const diagnostics = {
    timestamp: new Date().toISOString(),
    browser: {},
    storage: {},
    settings: {},
    security: {},
    extensions: {}
  };
  
  // 1. Browser Information
  console.log('🌐 BROWSER INFORMATION:');
  diagnostics.browser = {
    userAgent: navigator.userAgent,
    vendor: navigator.vendor,
    platform: navigator.platform,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    language: navigator.language,
    hardwareConcurrency: navigator.hardwareConcurrency
  };
  
  // Detect specific browser
  let browserName = 'Unknown';
  if (navigator.userAgent.includes('Chrome') && !navigator.userAgent.includes('Edge')) {
    browserName = 'Chrome';
  } else if (navigator.userAgent.includes('Firefox')) {
    browserName = 'Firefox';
  } else if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
    browserName = 'Safari';
  } else if (navigator.userAgent.includes('Edge')) {
    browserName = 'Edge';
  }
  
  diagnostics.browser.name = browserName;
  console.log(`  Browser: ${browserName}`);
  console.log(`  User Agent: ${navigator.userAgent}`);
  console.log(`  Platform: ${navigator.platform}`);
  
  // 2. Storage Capabilities
  console.log('\n💾 STORAGE CAPABILITIES:');
  try {
    const testKey = '__storage_test__';
    localStorage.setItem(testKey, 'test');
    const canWrite = localStorage.getItem(testKey) === 'test';
    localStorage.removeItem(testKey);
    
    diagnostics.storage = {
      localStorageAvailable: typeof(Storage) !== "undefined",
      canWrite: canWrite,
      length: localStorage.length,
      keys: []
    };
    
    // Get all keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) diagnostics.storage.keys.push(key);
    }
    
    console.log(`  localStorage Available: ${diagnostics.storage.localStorageAvailable}`);
    console.log(`  Can Write: ${diagnostics.storage.canWrite}`);
    console.log(`  Current Items: ${diagnostics.storage.length}`);
    console.log(`  Torvie Keys: ${diagnostics.storage.keys.filter(k => k.startsWith('torvie_')).length}`);
    
  } catch (error) {
    console.error('  ❌ Storage Error:', error);
    diagnostics.storage.error = error.message;
  }
  
  // 3. Security Context
  console.log('\n🔒 SECURITY CONTEXT:');
  diagnostics.security = {
    protocol: window.location.protocol,
    hostname: window.location.hostname,
    port: window.location.port,
    isSecureContext: window.isSecureContext,
    origin: window.location.origin
  };
  
  console.log(`  Protocol: ${diagnostics.security.protocol}`);
  console.log(`  Hostname: ${diagnostics.security.hostname}`);
  console.log(`  Port: ${diagnostics.security.port}`);
  console.log(`  Secure Context: ${diagnostics.security.isSecureContext}`);
  
  // 4. Check for Incognito/Private Mode (Advanced Detection)
  console.log('\n🕵️ INCOGNITO/PRIVATE MODE DETECTION:');
  
  // Method 1: Storage quota
  if (navigator.storage && navigator.storage.estimate) {
    navigator.storage.estimate().then(estimate => {
      const isLikelyIncognito = estimate.quota < 120000000; // Less than ~120MB usually indicates incognito
      console.log(`  Storage Quota: ${(estimate.quota / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  Likely Incognito (quota-based): ${isLikelyIncognito}`);
      diagnostics.security.likelyIncognito = isLikelyIncognito;
    });
  }
  
  // Method 2: Request file system (Chrome specific)
  try {
    if (window.webkitRequestFileSystem) {
      window.webkitRequestFileSystem(window.TEMPORARY, 1, 
        () => {
          console.log('  FileSystem API: Available (likely normal mode)');
          diagnostics.security.fileSystemAPI = 'available';
        },
        () => {
          console.log('  FileSystem API: Blocked (possibly incognito)');
          diagnostics.security.fileSystemAPI = 'blocked';
        }
      );
    }
  } catch (e) {
    console.log('  FileSystem API: Not supported');
    diagnostics.security.fileSystemAPI = 'not-supported';
  }
  
  // 5. Extension Detection
  console.log('\n🧩 EXTENSION DETECTION:');
  
  // Check for common privacy extensions
  const extensionTests = [
    { name: 'uBlock Origin', test: () => window.uBlockOrigin },
    { name: 'AdBlock', test: () => window.adblock },
    { name: 'Privacy Badger', test: () => window.PRIVACY_BADGER },
    { name: 'Ghostery', test: () => window.Ghostery },
    { name: 'DuckDuckGo', test: () => window.duckduckgo },
    { name: 'Brave Shields', test: () => navigator.brave }
  ];
  
  diagnostics.extensions.detected = [];
  extensionTests.forEach(ext => {
    try {
      if (ext.test()) {
        console.log(`  ✅ Detected: ${ext.name}`);
        diagnostics.extensions.detected.push(ext.name);
      }
    } catch (e) {
      // Silently fail
    }
  });
  
  if (diagnostics.extensions.detected.length === 0) {
    console.log('  No common privacy extensions detected');
  }
  
  // 6. Browser Settings Hints
  console.log('\n⚙️ BROWSER SETTINGS TO CHECK:');
  
  if (browserName === 'Chrome') {
    console.log('  Chrome Settings to check:');
    console.log('    • chrome://settings/privacy → Clear browsing data → "Cookies and other site data"');
    console.log('    • chrome://settings/content/cookies → "Clear cookies and site data when you close all windows"');
    console.log('    • Extensions → Check privacy/ad blocker settings');
  } else if (browserName === 'Firefox') {
    console.log('  Firefox Settings to check:');
    console.log('    • about:preferences#privacy → History → "Clear history when Firefox closes"');
    console.log('    • about:preferences#privacy → "Delete cookies and site data when Firefox is closed"');
    console.log('    • Extensions → Check privacy add-on settings');
  } else if (browserName === 'Edge') {
    console.log('  Edge Settings to check:');
    console.log('    • edge://settings/privacy → "Choose what to clear every time you close the browser"');
    console.log('    • edge://settings/content/cookies → "Clear cookies and site data when you close all windows"');
  }
  
  // 7. Create persistence timestamp
  console.log('\n🕒 CREATING PERSISTENCE TIMESTAMP:');
  const persistenceTest = {
    created: Date.now(),
    browserSession: Math.random().toString(36),
    diagnostics: diagnostics
  };
  
  localStorage.setItem('torvie_browser_diagnostics', JSON.stringify(persistenceTest));
  console.log('  Created persistence test data - close browser and reopen to check if it survives');
  
  // 8. Check for existing persistence data
  const existingTest = localStorage.getItem('torvie_browser_diagnostics');
  if (existingTest) {
    try {
      const parsed = JSON.parse(existingTest);
      const ageMinutes = (Date.now() - parsed.created) / 60000;
      console.log(`  Found existing test data from ${ageMinutes.toFixed(1)} minutes ago`);
      if (ageMinutes > 5) {
        console.log('  🎉 Data survived browser restart! Storage is working correctly.');
      }
    } catch (e) {
      console.log('  Found corrupted test data');
    }
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('🎯 NEXT STEPS:');
  console.log('1. Close this browser completely');
  console.log('2. Reopen browser and navigate back to this page');
  console.log('3. Check console for "Found existing test data" message');
  console.log('4. If no message appears, the issue is browser settings/extensions');
  
  return diagnostics;
};

// Make available globally
if (typeof window !== 'undefined') {
  window.runBrowserDiagnostics = runBrowserDiagnostics;
  
  // Auto-run on import
  console.log('🔍 Running browser diagnostics...');
  runBrowserDiagnostics();
} 