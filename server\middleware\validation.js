import { z } from 'zod';
import { logger } from '../utils/logger.js';

// Common validation schemas
export const loginSchema = z.object({
  username: z.string().min(1, 'Username is required').max(50, 'Username too long'),
  password: z.string().min(1, 'Password is required').max(100, 'Password too long')
});

export const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username too long'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters').max(100, 'Password too long'),
  displayName: z.string().min(1, 'Display name is required').max(100, 'Display name too long')
});

export const searchQuerySchema = z.object({
  q: z.string().min(1, 'Search query is required').max(200, 'Search query too long'),
  page: z.string().optional().transform(val => parseInt(val) || 1),
  mediaType: z.enum(['all', 'movie', 'tv', 'anime']).optional().default('all'),
  genre: z.string().optional(),
  year: z.string().optional().transform(val => parseInt(val) || null),
  rating: z.string().optional().transform(val => parseFloat(val) || null),
  sortBy: z.enum(['relevance', 'popularity', 'rating', 'date']).optional().default('relevance')
});

export const mediaIdSchema = z.object({
  id: z.string().regex(/^\d+$/, 'Invalid media ID').transform(val => parseInt(val))
});

export const streamStartSchema = z.object({
  magnet: z.string().url('Invalid magnet URL').or(z.string().startsWith('magnet:', 'Invalid magnet URL')),
  title: z.string().min(1, 'Title is required').max(200, 'Title too long')
});

export const profileSchema = z.object({
  name: z.string().min(1, 'Profile name is required').max(50, 'Profile name too long'),
  avatar_url: z.string().url('Invalid avatar URL').optional(),
  is_kids: z.boolean().optional().default(false)
});

// Generic validation middleware
export const validate = (schema) => {
  return (req, res, next) => {
    try {
      let data;
      
      // Validate based on request method
      if (req.method === 'GET') {
        data = schema.parse(req.query);
      } else {
        data = schema.parse(req.body);
      }
      
      // Replace validated data
      if (req.method === 'GET') {
        req.query = data;
      } else {
        req.body = data;
      }
      
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        logger.warn({ 
          validationError: error.errors,
          path: req.path,
          method: req.method,
          ip: req.ip 
        });
        
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      
      logger.error({ 
        validationUnexpectedError: error.message,
        path: req.path,
        method: req.method 
      });
      
      return res.status(500).json({
        success: false,
        error: 'Internal validation error'
      });
    }
  };
};

// Sanitize common inputs
export const sanitizeInput = (req, res, next) => {
  const sanitize = (obj) => {
    if (typeof obj !== 'object' || obj === null) return obj;
    
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        // Remove potential XSS vectors
        sanitized[key] = value
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      } else if (typeof value === 'object') {
        sanitized[key] = sanitize(value);
      } else {
        sanitized[key] = value;
      }
    }
    return sanitized;
  };
  
  req.body = sanitize(req.body);
  req.query = sanitize(req.query);
  
  next();
};

// Path traversal protection
export const preventPathTraversal = (req, res, next) => {
  const path = req.path;
  
  // Check for path traversal attempts
  if (path.includes('..') || path.includes('~') || path.includes('//')) {
    logger.warn({ 
      pathTraversalAttempt: path,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(400).json({
      success: false,
      error: 'Invalid path'
    });
  }
  
  next();
};
