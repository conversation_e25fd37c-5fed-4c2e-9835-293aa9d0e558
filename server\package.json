{"name": "torvie-server", "version": "1.0.0", "description": "Backend for the Torvie application.", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint \"**/*.js\" --max-warnings=0 --ignore-path ../.gitignore", "format": "prettier --write .", "test": "jest --runInBand"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^7.0.0", "node-cache": "^5.1.2", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "webtorrent": "^2.2.1"}, "devDependencies": {"nodemon": "^2.0.22", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "jest": "^29.7.0", "supertest": "^6.3.4"}}