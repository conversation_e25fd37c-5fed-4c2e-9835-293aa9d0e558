{"name": "torvie-server", "version": "1.0.0", "description": "Backend for the Torvie application.", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint \"**/*.js\" --max-warnings=0 --ignore-path ../.gitignore", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest --runInBand --coverage", "test:watch": "jest --watch", "migrate": "node scripts/migrate.js", "migrate:status": "node scripts/migrate.js status", "migrate:create": "node scripts/migrate.js create", "migrate:test": "NODE_ENV=test node scripts/migrate.js", "migrate-data": "node scripts/migrate-data.js", "backup": "node scripts/backup.js", "backup:create": "node scripts/backup.js create", "backup:restore": "node scripts/backup.js restore", "backup:list": "node scripts/backup.js list", "backup:cleanup": "node scripts/backup.js cleanup", "db:setup": "node scripts/setup-database.js setup", "db:reset": "node scripts/setup-database.js reset", "db:health": "node scripts/setup-database.js health", "seed": "node scripts/seed.js", "cleanup": "node scripts/cleanup.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.2", "bcrypt": "^5.1.1", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.0.0", "helmet": "^7.0.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "pg": "^8.11.3", "pino": "^8.17.2", "pino-http": "^9.0.0", "pino-pretty": "^10.3.1", "webtorrent": "^2.2.1", "zod": "^3.22.4"}, "devDependencies": {"nodemon": "^3.1.10", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "jest": "^29.7.0", "supertest": "^6.3.4"}}