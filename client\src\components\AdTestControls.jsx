import React from 'react';
import AD_CONFIG from '../utils/adConfig';

const AdTestControls = ({ onClose }) => {
  const testAdTags = [
    { name: 'Linear (10s)', key: 'PREROLL_LINEAR' },
    { name: '<PERSON>ppa<PERSON> (30s)', key: 'PREROLL_SKIPPABLE' },
    { name: 'VMAP (Pre/Mid/Post)', key: 'MIDROLL' }
  ];

  const handleTestAd = (adType) => {
    const adUrl = AD_CONFIG.getAdTag(adType);
    console.log(`🎬 Testing ad type: ${adType}`);
    console.log(`🎬 Ad URL: ${adUrl}`);
    
    // Test if ad URL is reachable
    fetch(adUrl, { method: 'HEAD' })
      .then(response => {
        console.log(`🎬 Ad URL test result:`, {
          status: response.status,
          ok: response.ok,
          headers: Object.fromEntries(response.headers.entries())
        });
        if (response.ok) {
          alert(`✅ ${adType} ad URL is reachable!\nStatus: ${response.status}\nCheck console for details.`);
        } else {
          alert(`⚠️ ${adType} ad URL responded with status: ${response.status}\nCheck console for details.`);
        }
      })
      .catch(error => {
        console.error(`🎬 Ad URL test failed:`, error);
        alert(`❌ ${adType} ad URL test failed.\nError: ${error.message}\nThis might be due to CORS restrictions (normal for test ads).`);
      });
  };

  const toggleTestMode = () => {
    if (AD_CONFIG.SETTINGS.USE_TEST_ADS) {
      AD_CONFIG.enableProductionAds();
      console.log('🎬 Switched to production ads (will fail until you add real GAM tags)');
    } else {
      AD_CONFIG.enableTestAds();
      console.log('🎬 Switched to test ads');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[99999]">
      <div className="bg-gray-900 p-6 rounded-lg max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Ad Test Controls</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">Current Settings</h3>
            <div className="text-sm text-gray-300 space-y-1">
              <p>Mode: {AD_CONFIG.SETTINGS.USE_TEST_ADS ? 'Test Ads' : 'Production Ads'}</p>
              <p>Pre-roll: {AD_CONFIG.SETTINGS.ENABLE_PREROLL ? 'Enabled' : 'Disabled'}</p>
              <p>Mid-roll: {AD_CONFIG.SETTINGS.ENABLE_MIDROLL ? 'Enabled' : 'Disabled'}</p>
              <p>Post-roll: {AD_CONFIG.SETTINGS.ENABLE_POSTROLL ? 'Enabled' : 'Disabled'}</p>
              <p>IMA SDK: {window.google && window.google.ima ? '✅ Ready' : '❌ Not Loaded'}</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-2">Test Ad Types</h3>
            <div className="space-y-2">
              {testAdTags.map((ad) => (
                <button
                  key={ad.key}
                  onClick={() => handleTestAd(ad.key)}
                  className="w-full p-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                >
                  Test {ad.name}
                </button>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-2">Controls</h3>
            <div className="space-y-2">
              <button
                onClick={toggleTestMode}
                className="w-full p-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors"
              >
                Switch to {AD_CONFIG.SETTINGS.USE_TEST_ADS ? 'Production' : 'Test'} Mode
              </button>
              
              <button
                onClick={() => {
                  AD_CONFIG.SETTINGS.ENABLE_PREROLL = !AD_CONFIG.SETTINGS.ENABLE_PREROLL;
                  console.log('🎬 Pre-roll ads:', AD_CONFIG.SETTINGS.ENABLE_PREROLL ? 'Enabled' : 'Disabled');
                  alert(`Pre-roll ads ${AD_CONFIG.SETTINGS.ENABLE_PREROLL ? 'enabled' : 'disabled'}`);
                }}
                className="w-full p-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
              >
                Toggle Pre-roll ({AD_CONFIG.SETTINGS.ENABLE_PREROLL ? 'ON' : 'OFF'})
              </button>
              
              <button
                onClick={() => {
                  console.log('🎬 Manually requesting ads...');
                  // This will trigger a manual ad request if the player supports it
                  const event = new CustomEvent('torvieRequestAds', { 
                    detail: { manual: true }
                  });
                  window.dispatchEvent(event);
                }}
                className="w-full p-2 bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors"
              >
                Force Request Ads
              </button>
              
              <button
                onClick={() => {
                  const adUrl = AD_CONFIG.getAdTag('PREROLL_LINEAR');
                  const vsiUrl = `https://googleads.github.io/googleads-ima-html5/vsi/?tag=${encodeURIComponent(adUrl)}`;
                  window.open(vsiUrl, '_blank');
                  console.log('🎬 Opening Google Video Suite Inspector with current ad tag');
                }}
                className="w-full p-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition-colors"
              >
                Test in Google VSI
              </button>
            </div>
          </div>

          <div className="text-xs text-gray-400 mt-4">
            <p>💡 <strong>Tips:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>Check browser console for detailed ad logs</li>
              <li>Use Google&apos;s Video Suite Inspector to test ad URLs</li>
              <li>Test different ad types to verify integration</li>
              <li>Switch to production mode when GAM is approved</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdTestControls; 