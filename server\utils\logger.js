import pino from 'pino';

// Configure logger based on environment
const logLevel = process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug');

export const logger = pino({
  level: logLevel,
  transport: process.env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname'
    }
  } : undefined,
  formatters: {
    level: (label) => ({ level: label }),
    log: (object) => {
      // Add request context if available
      if (object.req) {
        object.requestId = object.req.id;
        object.method = object.req.method;
        object.url = object.req.url;
        object.ip = object.req.ip;
        object.userAgent = object.req.headers['user-agent'];
      }
      return object;
    }
  },
  serializers: {
    req: pino.stdSerializers.req,
    res: pino.stdSerializers.res,
    err: pino.stdSerializers.err
  }
});

// Create child loggers for different contexts
export const createLogger = (context) => {
  return logger.child({ context });
};

// Security event logger
export const securityLogger = createLogger('security');

// Performance logger
export const performanceLogger = createLogger('performance');

// Database logger
export const dbLogger = createLogger('database');

// Stream logger
export const streamLogger = createLogger('stream');

// Export default logger
export default logger; 