import React, { useState, useEffect, useRef } from 'react';
import LiveStreamPlayer from '../components/LiveStreamPlayer';
import { useLiveTV } from '../contexts/LiveTVContext';

// Helper: get country from lat/lon using OpenStreetMap Nominatim
async function getCountryFromCoords(lat, lon) {
  try {
    const res = await fetch(`https://nominatim.openstreetmap.org/reverse?lat=${lat}&lon=${lon}&format=json`);
    const data = await res.json();
    return data.address && (data.address.country_code || data.address.country);
  } catch {
    return null;
  }
}

// Helper: ISO country code mapping (partial, for demo)
const COUNTRY_CODE_MAP = {
  US: ['US', 'USA', 'United States', 'United States of America'],
  CA: ['CA', 'CAN', 'Canada'],
  GB: ['GB', 'UK', 'United Kingdom', 'Great Britain', 'England', 'Scotland', 'Wales', 'Northern Ireland'],
  AU: ['AU', 'AUS', 'Australia'],
  // Add more as needed
};

function normalizeCountryCode(code) {
  if (!code) return null;
  code = code.toUpperCase();
  if (COUNTRY_CODE_MAP[code]) return code;
  // Try to find by value
  for (const [iso, names] of Object.entries(COUNTRY_CODE_MAP)) {
    if (names.some(n => n.toUpperCase() === code)) return iso;
  }
  return code.length === 2 ? code : null;
}

// Helper to render channel button
function renderChannelButton(ch, idx, isLocal, setSelectedChannel) {
  return (
    <button
      key={ch.url + idx}
      className="group bg-gray-900/80 rounded-xl shadow-lg hover:shadow-2xl hover:ring-2 hover:ring-red-500/60 transition-all flex flex-col items-center p-4 cursor-pointer focus:outline-none focus:ring-2 focus:ring-pink-500"
      onClick={() => setSelectedChannel(ch)}
    >
      <div className="w-20 h-20 rounded-full bg-gray-800 flex items-center justify-center mb-3 overflow-hidden border-2 border-gray-700 group-hover:border-red-500">
        {ch.logo ? (
          <img src={ch.logo} alt={ch.name} className="w-full h-full object-contain" />
        ) : (
          <span className="text-3xl">📺</span>
        )}
      </div>
      <div className="text-lg font-bold text-white text-center truncate w-full mb-1">
        {ch.name}
      </div>
      <div className="text-xs text-gray-400 text-center truncate w-full mb-1">
        {ch.country || ch.language || ch.group}
        {isLocal && (
          <span className="ml-2 inline-block bg-blue-600 text-white text-[10px] px-2 py-0.5 rounded-full align-middle">LOCAL</span>
        )}
      </div>
      <div className="mt-1 text-xs text-pink-400 font-semibold uppercase tracking-wide truncate w-full">
        {ch.group}
      </div>
    </button>
  );
}

const PAGE_SIZE = 40;

const LiveStreaming = () => {
  const { channels, isLoading, error } = useLiveTV();
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [userCountry, setUserCountry] = useState(null);
  const [showAll, setShowAll] = useState(false);
  const [visibleCount, setVisibleCount] = useState(PAGE_SIZE);
  const [search, setSearch] = useState('');
  const containerRef = useRef();

  // Filtered channels based on search
  const filteredChannels = React.useMemo(() => {
    if (!search.trim()) return channels;
    const q = search.trim().toLowerCase();
    return channels.filter(
      ch =>
        (ch.name && ch.name.toLowerCase().includes(q)) ||
        (ch.group && ch.group.toLowerCase().includes(q)) ||
        (ch.country && ch.country.toLowerCase().includes(q))
    );
  }, [channels, search]);

  // Infinite scroll handler
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      if (scrollHeight - scrollTop - clientHeight < 300) {
        setVisibleCount((prev) => Math.min(prev + PAGE_SIZE, filteredChannels.length));
      }
    };
    const ref = containerRef.current;
    if (ref) ref.addEventListener('scroll', handleScroll);
    return () => { if (ref) ref.removeEventListener('scroll', handleScroll); };
  }, [filteredChannels.length]);

  // Reset visibleCount when channels, showAll, userCountry, or search changes
  useEffect(() => {
    setVisibleCount(PAGE_SIZE);
  }, [channels, showAll, userCountry, search]);

  if (isLoading) {
    return null;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black text-white p-4 sm:p-6">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">Live TV</h1>
          <div className="bg-red-900 bg-opacity-50 border border-red-700 rounded-lg p-6">
            <p className="text-red-300">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} style={{ height: 'calc(100vh - 80px)', overflowY: 'auto' }} className="bg-gradient-to-br from-black via-gray-900 to-gray-950 text-white">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-4xl font-black mb-6 tracking-tight bg-gradient-to-r from-red-500 via-pink-500 to-purple-500 bg-clip-text text-transparent drop-shadow-lg">
          🔴 Live TV
        </h1>
        {/* Search Bar */}
        <div className="mb-6 flex justify-center">
          <input
            type="text"
            value={search}
            onChange={e => setSearch(e.target.value)}
            placeholder="Search for a TV station, group, or country..."
            className="w-full max-w-xl px-4 py-2 rounded-full bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 text-lg shadow"
            style={{ boxShadow: '0 2px 16px 0 rgba(0,0,0,0.15)' }}
          />
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
          {(() => {
            const pagedChannels = filteredChannels;
            if (!userCountry) {
              // If no location, show paginated filtered channels
              return pagedChannels.slice(0, visibleCount).map((ch, idx) => renderChannelButton(ch, idx, false, setSelectedChannel));
            }
            // Normalize all channel country codes
            const localChannels = [];
            const otherChannels = [];
            for (let i = 0; i < pagedChannels.length; ++i) {
              const ch = pagedChannels[i];
              const chCountry = normalizeCountryCode(ch.country);
              const isLocal = chCountry && chCountry === userCountry;
              if (isLocal) localChannels.push([ch, i]);
              else otherChannels.push([ch, i]);
            }
            if (!showAll) {
              // Only show local channels, paginated
              return (
                <>
                  {localChannels.length > 0 ? (
                    <>
                      <div className="col-span-full mb-2 mt-2 text-blue-400 font-bold text-lg">Local Channels</div>
                      {localChannels.slice(0, visibleCount).map(([ch, idx]) => renderChannelButton(ch, idx, true, setSelectedChannel))}
                      {localChannels.length > visibleCount && (
                        <div className="col-span-full flex justify-center mt-6">
                          <button
                            className="bg-gray-800 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow"
                            onClick={() => setVisibleCount((prev) => Math.min(prev + PAGE_SIZE, localChannels.length))}
                          >
                            Load More
                          </button>
                        </div>
                      )}
                      {otherChannels.length > 0 && (
                        <div className="col-span-full flex justify-center mt-6">
                          <button
                            className="bg-gray-800 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow"
                            onClick={() => setShowAll(true)}
                          >
                            Show All Channels
                          </button>
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      <div className="col-span-full text-center text-gray-400 my-8">No local channels found. You can view all channels below.</div>
                      {otherChannels.length > 0 && (
                        <div className="col-span-full flex justify-center mt-6">
                          <button
                            className="bg-gray-800 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow"
                            onClick={() => setShowAll(true)}
                          >
                            Show All Channels
                          </button>
                        </div>
                      )}
                    </>
                  )}
                </>
              );
            } else {
              // Show all channels, paginated
              return (
                <>
                  {localChannels.length > 0 && (
                    <div className="col-span-full mb-2 mt-2 text-blue-400 font-bold text-lg">Local Channels</div>
                  )}
                  {localChannels.slice(0, visibleCount).map(([ch, idx]) => renderChannelButton(ch, idx, true, setSelectedChannel))}
                  {otherChannels.length > 0 && (
                    <div className="col-span-full mb-2 mt-4 text-gray-400 font-bold text-lg">All Channels</div>
                  )}
                  {otherChannels.slice(0, Math.max(0, visibleCount - localChannels.length)).map(([ch, idx]) => renderChannelButton(ch, idx, false, setSelectedChannel))}
                  {(localChannels.length + otherChannels.length) > visibleCount && (
                    <div className="col-span-full flex justify-center mt-6">
                      <button
                        className="bg-gray-800 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow"
                        onClick={() => setVisibleCount((prev) => Math.min(prev + PAGE_SIZE, localChannels.length + otherChannels.length))}
                      >
                        Load More
                      </button>
                    </div>
                  )}
                </>
              );
            }
          })()}
        </div>
      </div>
      {/* Modal Player */}
      {selectedChannel && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-lg">
          <div className="relative w-full max-w-2xl mx-auto bg-gray-900 rounded-2xl shadow-2xl p-0 flex flex-col items-center overflow-hidden">
            {/* Close Button */}
            <button
              className="absolute top-4 right-4 text-white bg-black/70 hover:bg-black/90 rounded-full p-2 focus:outline-none z-20"
              onClick={() => setSelectedChannel(null)}
              aria-label="Close"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            {/* Video Player */}
            <div className="w-full aspect-video bg-black flex items-center justify-center relative">
              <LiveStreamPlayer
                streamUrl={selectedChannel.url}
                logo={selectedChannel.logo}
                onNextChannel={() => {
                  if (!channels.length) return;
                  const idx = channels.findIndex(ch => ch.url === selectedChannel.url);
                  const nextIdx = (idx + 1) % channels.length;
                  setSelectedChannel(channels[nextIdx]);
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveStreaming; 