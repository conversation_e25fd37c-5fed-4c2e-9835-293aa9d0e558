---
description: "Core software design principles (SOLID, DRY, KISS, YAGNI) to be applied to all application code."
globs:
  - "**/*.js"
  - "**/*.ts"
  - "**/*.tsx"
  - "**/*.py"
  - "**/*.go"
  - "**/*.rs"
  - "**/*.cs"
alwaysApply: false
---

# Engineering Principles

Apply these foundational principles to all code you write.

* **SOLID:**
    * **S**ingle Responsibility: A module does one thing well.
    * **O**pen/Closed: Open for extension, closed for modification.
    * **L**iskov Substitution: Subtypes must be substitutable for their base types.
    * **I**nterface Segregation: Prefer many client-specific interfaces.
    * **D**ependency Inversion: Depend on abstractions, not concretions.
* **DRY (Don't Repeat Yourself):** Avoid redundancy. Abstract and reuse logic.
* **KISS (Keep It Simple, Stupid):** Reject unnecessary complexity. The simplest solution is best.
* **YAGNI (You Ain't Gonna Need It):** Do not add functionality until it is deemed necessary.
* **Composition over Inheritance:** Favor composing behavior over complex inheritance hierarchies.