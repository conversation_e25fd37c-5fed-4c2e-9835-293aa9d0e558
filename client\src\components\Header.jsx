import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { AnimatedTorvieLogo } from './LoadingScreen';
import LoginModal from './LoginModal';
import RegisterModal from './RegisterModal';
import EditProfileModal from './EditProfileModal';
import PerformanceDashboard from './PerformanceDashboard';

const UserMenu = ({ isOpen, onClose, user, onLogout, profile, onSwitchProfile, onEditProfile }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-70" onClick={onClose}>
      <div
        className="absolute right-8 top-20 transition-all duration-500 ease-out bg-black/90 backdrop-blur-xl rounded-xl shadow-2xl p-6 w-64 border border-purple-500/30"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-cyan-500 to-purple-600 flex items-center justify-center text-black font-bold shadow-lg">
            {profile?.name?.[0]?.toUpperCase() || user?.username?.[0]?.toUpperCase() || 'U'}
          </div>
          <div>
            <h3 className="text-white font-semibold">{profile?.name?.replace(' - Main Account', '') || user?.username}</h3>
            <p className="text-gray-300 text-sm">{user?.email}</p>
          </div>
        </div>
        
        <div className="border-t border-purple-500/30 pt-4 space-y-1">
          <button
            onClick={onSwitchProfile}
            className="block w-full text-left px-3 py-2 text-gray-300 hover:text-cyan-400 hover:bg-purple-900/30 rounded-lg transition-colors"
          >
            Switch Profile
          </button>
          <button
            onClick={onEditProfile}
            className="block w-full text-left px-3 py-2 text-gray-300 hover:text-cyan-400 hover:bg-purple-900/30 rounded-lg transition-colors"
          >
            Edit Profile
          </button>
          <Link 
            to="/watchlist" 
            className="block w-full text-left px-3 py-2 text-gray-300 hover:text-cyan-400 hover:bg-purple-900/30 rounded-lg transition-colors"
            onClick={onClose}
          >
            My Watchlist
          </Link>
          <button
            onClick={onLogout}
            className="block w-full text-left px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
          >
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
};

// Animated Search Button Component
const AnimatedSearchButton = ({ onClick, className }) => {
  const canvasRef = useRef(null);
  const [animationTime, setAnimationTime] = useState(0);
  const animationRef = useRef();

  useEffect(() => {
    const animate = () => {
      setAnimationTime(prev => prev + 0.02);
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (canvasRef.current) {
      renderAnimatedButton();
    }
  }, [animationTime]);

  const renderAnimatedButton = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const size = 40; // Button size
    const dpr = window.devicePixelRatio || 1;
    
    canvas.width = size * dpr;
    canvas.height = size * dpr;
    ctx.scale(dpr, dpr);
    
    ctx.clearRect(0, 0, size, size);

    const centerX = size / 2;
    const centerY = size / 2;
    const radius = 18;
    const strokeWidth = 2;

    // Animation timing
    const time = animationTime * 0.5;
    
    // Same color palettes as logo
    const purpleShades = [
        [138, 43, 226],   // Blue violet
        [75, 0, 130],     // Indigo  
        [147, 0, 211],    // Dark violet
        [123, 104, 238],  // Medium slate blue
        [102, 51, 153]    // Rebecca purple
    ];
    
    const cyanShades = [
        [0, 191, 255],    // Deep sky blue
        [0, 206, 209],    // Dark turquoise
        [64, 224, 208],   // Turquoise
        [0, 255, 255],    // Cyan
        [32, 178, 170]    // Light sea green
    ];
    
    // Same interpolation function as logo
    const interpolateColor = (colorArray, time) => {
        const scaledTime = time % colorArray.length;
        const index1 = Math.floor(scaledTime);
        const index2 = (index1 + 1) % colorArray.length;
        const factor = scaledTime - index1;
        
        const color1 = colorArray[index1];
        const color2 = colorArray[index2];
        
        return [
            Math.round(color1[0] + (color2[0] - color1[0]) * factor),
            Math.round(color1[1] + (color2[1] - color1[1]) * factor),
            Math.round(color1[2] + (color2[2] - color1[2]) * factor)
        ];
    };

    // Create flowing gradient
    ctx.lineWidth = strokeWidth;
    const rotationSpeed = time * 1.5;
    
    const allColors = [...purpleShades, ...cyanShades];
    const color1 = interpolateColor(allColors, time);
    const color2 = interpolateColor(allColors, time + 2);
    const color3 = interpolateColor(allColors, time + 4);
    const color4 = interpolateColor(allColors, time + 6);
    const color5 = interpolateColor(allColors, time + 8);
    
    const gradient = ctx.createConicGradient(rotationSpeed, centerX, centerY);
    gradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(0.15, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.25, `rgb(${color3[0]}, ${color3[1]}, ${color3[2]})`);
    gradient.addColorStop(0.4, `rgb(${color4[0]}, ${color4[1]}, ${color4[2]})`);
    gradient.addColorStop(0.6, `rgb(${color5[0]}, ${color5[1]}, ${color5[2]})`);
    gradient.addColorStop(0.75, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.85, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(1, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    
    // Draw animated circle
    ctx.beginPath();
    ctx.strokeStyle = gradient;
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.stroke();

    // Draw magnifying glass icon
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 1.5;
    
    // Search circle
    ctx.beginPath();
    ctx.arc(centerX - 2, centerY - 2, 6, 0, 2 * Math.PI);
    ctx.stroke();
    
    // Search handle
    ctx.beginPath();
    ctx.moveTo(centerX + 2, centerY + 2);
    ctx.lineTo(centerX + 6, centerY + 6);
    ctx.stroke();
  };

  return (
    <button 
      type="submit" 
      onClick={onClick}
      className={`${className} relative flex items-center justify-center`}
    >
      <canvas
        ref={canvasRef}
        style={{ width: '40px', height: '40px' }}
        className="cursor-pointer"
      />
    </button>
  );
};

// Animated Search Bar Component
const AnimatedSearchBar = ({ children, onSubmit, className }) => {
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [animationTime, setAnimationTime] = useState(0);
  const [dimensions, setDimensions] = useState({ width: 450, height: 40 });
  const animationRef = useRef();

  useEffect(() => {
    const animate = () => {
      setAnimationTime(prev => prev + 0.02);
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setDimensions({ width: rect.width, height: rect.height });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  useEffect(() => {
    if (canvasRef.current) {
      renderAnimatedBorder();
    }
  }, [animationTime, dimensions]);

  const renderAnimatedBorder = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const { width, height } = dimensions;
    const dpr = window.devicePixelRatio || 1;
    
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    ctx.scale(dpr, dpr);
    
    ctx.clearRect(0, 0, width, height);

    const centerX = width / 2;
    const centerY = height / 2;
    const radiusX = (width - 4) / 2;
    const radiusY = (height - 4) / 2;
    const strokeWidth = 2;

    // Animation timing - COUNTER CLOCKWISE
    const time = animationTime * 0.5;
    
    // Same color palettes as logo
    const purpleShades = [
        [138, 43, 226],   // Blue violet
        [75, 0, 130],     // Indigo  
        [147, 0, 211],    // Dark violet
        [123, 104, 238],  // Medium slate blue
        [102, 51, 153]    // Rebecca purple
    ];
    
    const cyanShades = [
        [0, 191, 255],    // Deep sky blue
        [0, 206, 209],    // Dark turquoise
        [64, 224, 208],   // Turquoise
        [0, 255, 255],    // Cyan
        [32, 178, 170]    // Light sea green
    ];
    
    // Same interpolation function as logo
    const interpolateColor = (colorArray, time) => {
        const scaledTime = time % colorArray.length;
        const index1 = Math.floor(scaledTime);
        const index2 = (index1 + 1) % colorArray.length;
        const factor = scaledTime - index1;
        
        const color1 = colorArray[index1];
        const color2 = colorArray[index2];
        
        return [
            Math.round(color1[0] + (color2[0] - color1[0]) * factor),
            Math.round(color1[1] + (color2[1] - color1[1]) * factor),
            Math.round(color1[2] + (color2[2] - color1[2]) * factor)
        ];
    };

    // Create flowing gradient - COUNTER CLOCKWISE (negative rotation)
    ctx.lineWidth = strokeWidth;
    const rotationSpeed = -time * 1.5; // NEGATIVE for counter-clockwise
    
    const allColors = [...purpleShades, ...cyanShades];
    const color1 = interpolateColor(allColors, time);
    const color2 = interpolateColor(allColors, time + 2);
    const color3 = interpolateColor(allColors, time + 4);
    const color4 = interpolateColor(allColors, time + 6);
    const color5 = interpolateColor(allColors, time + 8);
    
    const gradient = ctx.createConicGradient(rotationSpeed, centerX, centerY);
    gradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(0.15, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.25, `rgb(${color3[0]}, ${color3[1]}, ${color3[2]})`);
    gradient.addColorStop(0.4, `rgb(${color4[0]}, ${color4[1]}, ${color4[2]})`);
    gradient.addColorStop(0.6, `rgb(${color5[0]}, ${color5[1]}, ${color5[2]})`);
    gradient.addColorStop(0.75, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.85, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(1, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    
    // Draw animated rounded rectangle border
    ctx.beginPath();
    ctx.strokeStyle = gradient;
    ctx.roundRect(2, 2, width - 4, height - 4, height / 2);
    ctx.stroke();
  };

  return (
    <form onSubmit={onSubmit} className={`${className} relative`} ref={containerRef}>
      <canvas
        ref={canvasRef}
        style={{ 
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1
        }}
      />
      <div className="relative z-10 bg-black/30 backdrop-blur-sm rounded-full px-2 sm:px-4 py-2 flex items-center">
        {children}
      </div>
    </form>
  );
};

// Animated Tab Component
const AnimatedTab = ({ children, onClick, to, className, isActive = false }) => {
  const navigate = useNavigate();
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [animationTime, setAnimationTime] = useState(0);
  const [dimensions, setDimensions] = useState({ width: 100, height: 40 });
  const [isHovered, setIsHovered] = useState(false);
  const animationRef = useRef();

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
    if (to) {
      navigate(to);
    }
  };

  useEffect(() => {
    const animate = () => {
      setAnimationTime(prev => prev + 0.02);
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setDimensions({ width: rect.width, height: rect.height });
      }
    };

    updateDimensions();
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, []);

  useEffect(() => {
    if (canvasRef.current && (isHovered || isActive)) {
      renderAnimatedBorder();
    } else if (canvasRef.current) {
      // Clear canvas when not hovered
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
  }, [animationTime, dimensions, isHovered, isActive]);

  const renderAnimatedBorder = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const { width, height } = dimensions;
    const dpr = window.devicePixelRatio || 1;
    
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    ctx.scale(dpr, dpr);
    
    ctx.clearRect(0, 0, width, height);

    const centerX = width / 2;
    const centerY = height / 2;
    const strokeWidth = 2;

    // Animation timing - same direction as search button (clockwise)
    const time = animationTime * 0.5;
    
    // Same color palettes as logo
    const purpleShades = [
        [138, 43, 226],   // Blue violet
        [75, 0, 130],     // Indigo  
        [147, 0, 211],    // Dark violet
        [123, 104, 238],  // Medium slate blue
        [102, 51, 153]    // Rebecca purple
    ];
    
    const cyanShades = [
        [0, 191, 255],    // Deep sky blue
        [0, 206, 209],    // Dark turquoise
        [64, 224, 208],   // Turquoise
        [0, 255, 255],    // Cyan
        [32, 178, 170]    // Light sea green
    ];
    
    // Same interpolation function as logo
    const interpolateColor = (colorArray, time) => {
        const scaledTime = time % colorArray.length;
        const index1 = Math.floor(scaledTime);
        const index2 = (index1 + 1) % colorArray.length;
        const factor = scaledTime - index1;
        
        const color1 = colorArray[index1];
        const color2 = colorArray[index2];
        
        return [
            Math.round(color1[0] + (color2[0] - color1[0]) * factor),
            Math.round(color1[1] + (color2[1] - color1[1]) * factor),
            Math.round(color1[2] + (color2[2] - color1[2]) * factor)
        ];
    };

    // Create flowing gradient - clockwise like search button
    ctx.lineWidth = strokeWidth;
    const rotationSpeed = time * 1.5; // Positive for clockwise
    
    const allColors = [...purpleShades, ...cyanShades];
    const color1 = interpolateColor(allColors, time);
    const color2 = interpolateColor(allColors, time + 2);
    const color3 = interpolateColor(allColors, time + 4);
    const color4 = interpolateColor(allColors, time + 6);
    const color5 = interpolateColor(allColors, time + 8);
    
    const gradient = ctx.createConicGradient(rotationSpeed, centerX, centerY);
    gradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(0.15, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.25, `rgb(${color3[0]}, ${color3[1]}, ${color3[2]})`);
    gradient.addColorStop(0.4, `rgb(${color4[0]}, ${color4[1]}, ${color4[2]})`);
    gradient.addColorStop(0.6, `rgb(${color5[0]}, ${color5[1]}, ${color5[2]})`);
    gradient.addColorStop(0.75, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.85, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(1, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    
    // Draw animated rounded rectangle border
    ctx.beginPath();
    ctx.strokeStyle = gradient;
    ctx.roundRect(1, 1, width - 2, height - 2, (height - 2) / 2);
    ctx.stroke();
  };

  return (
    <button 
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`${className} relative transition-all duration-200`}
      ref={containerRef}
    >
      <canvas
        ref={canvasRef}
        style={{ 
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1,
          opacity: isHovered || isActive ? 1 : 0,
          transition: 'opacity 0.3s ease'
        }}
      />
      <span className="relative z-10">
        {children}
      </span>
    </button>
  );
};

const Header = () => {
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [registerModalOpen, setRegisterModalOpen] = useState(false);
  const [editProfileModalOpen, setEditProfileModalOpen] = useState(false);
  const [performanceDashboardOpen, setPerformanceDashboardOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuth();
  const { profile } = useProfile();

  // eslint-disable-next-line no-unused-vars
  const [radiusX, setRadiusX] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [radiusY, setRadiusY] = useState(0);

  // Restore search value from sessionStorage when on search page
  useEffect(() => {
    if (location.pathname.startsWith('/search')) {
      const lastSearchQuery = sessionStorage.getItem('lastSearchQuery');
      if (lastSearchQuery) {
        setSearchValue(lastSearchQuery);
      }
    } else if (!location.pathname.startsWith('/movie/') && 
               !location.pathname.startsWith('/show/')) {
      // Clear search bar and sessionStorage when leaving search/details pages
      setSearchValue('');
      sessionStorage.removeItem('lastSearchQuery');
      sessionStorage.removeItem('lastSearchResults');
    }
  }, [location.pathname]);

  // Handler for tab clicks to clear scroll position and scroll to top
  const handleTabClick = (tab) => {
    setMobileMenuOpen(false); // Close mobile menu when navigating
    if (tab === 'movies') {
      sessionStorage.removeItem('dashboard_scroll_position');
      window.scrollTo(0, 0);
      navigate('/dashboard');
    } else if (tab === 'shows') {
      sessionStorage.removeItem('tvshows_scroll_position');
      window.scrollTo(0, 0);
      navigate('/tvshows');
    } else if (tab === 'anime') {
      sessionStorage.removeItem('anime_scroll_position');
      window.scrollTo(0, 0);
      navigate('/anime');
    } else if (tab === 'discovery') {
      window.scrollTo(0, 0);
      navigate('/discovery');
    } else if (tab === 'live') {
      window.scrollTo(0, 0);
      navigate('/livestreaming');
    }
    // Remove or comment out 'games' tab if not implemented
  };

  const handleLogout = async () => {
    await logout();
    setUserMenuOpen(false);
    setMobileMenuOpen(false);
  };

  const handleSwitchProfile = () => {
    setUserMenuOpen(false);
    setMobileMenuOpen(false);
    navigate('/profiles');
  };

  const handleEditProfile = () => {
    setUserMenuOpen(false);
    setEditProfileModalOpen(true);
  };

  const handleAuthClick = () => {
    if (isAuthenticated) {
      setUserMenuOpen(!userMenuOpen);
    } else {
      setLoginModalOpen(true);
    }
  };

  return (
    <header className="backdrop-blur-xl bg-black shadow-lg h-16 sm:h-20 flex items-center px-2 sm:px-4 lg:px-6 justify-between sticky top-0 z-50 border-b border-cyan-500/20">
      {/* Left Side: Logo + Search Bar */}
      <div className="flex items-center gap-2 flex-shrink-0">
        {/* Logo */}
        <div className="flex items-center flex-shrink-0 -ml-28">
          <Link to="/dashboard" className="flex items-center flex-shrink-0">
            <AnimatedTorvieLogo width={400} height={150} scale={1.2} />
          </Link>
        </div>
        
        {/* Search Bar - Next to logo */}
        <AnimatedSearchBar
          onSubmit={(e) => {
            e.preventDefault();
            if (searchValue.trim()) {
              navigate(`/search?q=${encodeURIComponent(searchValue.trim())}`);
            }
          }}
          className="min-w-[40px] sm:min-w-[300px] max-w-[40px] sm:max-w-[450px] -ml-20"
        >
          <input
            type="text"
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            placeholder="Search..."
            className="bg-transparent flex-1 outline-none text-white placeholder:text-gray-400 text-xs sm:text-sm min-w-0 hidden sm:block"
          />
          <AnimatedSearchButton 
            onClick={(e) => {
              e.preventDefault();
              if (searchValue.trim()) {
                navigate(`/search?q=${encodeURIComponent(searchValue.trim())}`);
              }
            }}
            className="ml-0 sm:ml-1 flex-shrink-0"
          />
        </AnimatedSearchBar>
      </div>
      
      {/* Center: Page Navigation Tabs */}
      <div className="flex items-center gap-3 flex-shrink-0 mx-auto">
        {/* Mobile Menu Button */}
        <button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="lg:hidden text-white p-2 hover:bg-cyan-500/20 rounded-lg transition-colors flex-shrink-0"
          aria-label="Toggle menu"
        >
          <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
        
        {/* Desktop Nav Tabs - Centered between search and user */}
        <nav className="hidden lg:flex gap-3 flex-shrink-0">
          <AnimatedTab 
            onClick={() => handleTabClick('movies')} 
            className="text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          >
            MOVIES
          </AnimatedTab>
          <AnimatedTab 
            onClick={() => handleTabClick('shows')} 
            className="text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          >
            TV SHOWS
          </AnimatedTab>
          <AnimatedTab 
            onClick={() => handleTabClick('anime')} 
            className="text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          >
            ANIME
          </AnimatedTab>
          <AnimatedTab 
            onClick={() => handleTabClick('discovery')} 
            className="text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          >
            🎯 DISCOVERY
          </AnimatedTab>
          <AnimatedTab 
            onClick={() => handleTabClick('live')} 
            className="text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          >
            🔴 LIVE TV
          </AnimatedTab>
          {/* Remove or comment out GAMES tab if not implemented */}
        </nav>
      </div>
      
      {/* Right Side: Discovery + Watchlist + User */}
      <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
        {/* Content Discovery Toggle */}
        <button
          onClick={() => {
            const currentDiscovery = localStorage.getItem('showDiscovery') !== 'false';
            localStorage.setItem('showDiscovery', (!currentDiscovery).toString());
            window.dispatchEvent(new CustomEvent('toggleDiscovery', { detail: !currentDiscovery }));
          }}
          className="hidden md:block text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          title="Toggle Content Discovery"
        >
          🎯
        </button>

        {/* Content Insights Toggle */}
        <button
          onClick={() => {
            const currentInsights = localStorage.getItem('showInsights') === 'true';
            localStorage.setItem('showInsights', (!currentInsights).toString());
            window.dispatchEvent(new CustomEvent('toggleInsights', { detail: !currentInsights }));
          }}
          className="hidden md:block text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          title="Toggle Content Insights"
        >
          📊
        </button>

        {/* Performance Dashboard */}
        <button
          onClick={() => setPerformanceDashboardOpen(true)}
          className="hidden md:block text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
          title="Performance Dashboard"
        >
          🚀
        </button>

        {/* Watchlist */}
        {isAuthenticated && (
          <AnimatedTab
            to="/watchlist"
            className="hidden md:block text-white font-semibold px-3 py-2 rounded-full hover:bg-cyan-500/20 hover:text-cyan-300 transition text-sm whitespace-nowrap"
            onClick={() => setMobileMenuOpen(false)}
          >
            WATCHLIST
          </AnimatedTab>
        )}
        
        {/* User Avatar */}
        <div
          className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-br from-cyan-500 to-purple-600 flex items-center justify-center text-black font-bold shadow-lg cursor-pointer border-2 border-transparent hover:border-cyan-400 transition flex-shrink-0 text-xs sm:text-sm"
          onClick={handleAuthClick}
          title={isAuthenticated ? (profile?.name || "Profile") : "Sign In"}
        >
          {isAuthenticated ? (profile?.name?.[0]?.toUpperCase() || user?.username?.[0]?.toUpperCase() || 'U') : '?'}
        </div>
      </div>
      
      {/* Mobile Menu Overlay - Enhanced */}
      {mobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black/80" onClick={() => setMobileMenuOpen(false)}>
          <div 
            className="absolute top-16 sm:top-20 left-0 right-0 bg-black/95 backdrop-blur-xl border-b border-cyan-500/20 p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <nav className="flex flex-col gap-3">
              <AnimatedTab 
                onClick={() => handleTabClick('movies')} 
                className="text-white font-semibold px-4 py-3 rounded-lg hover:bg-cyan-500/20 hover:text-cyan-300 transition text-left w-full"
              >
                MOVIES
              </AnimatedTab>
              <AnimatedTab 
                onClick={() => handleTabClick('shows')} 
                className="text-white font-semibold px-4 py-3 rounded-lg hover:bg-cyan-500/20 hover:text-cyan-300 transition text-left w-full"
              >
                TV SHOWS
              </AnimatedTab>
              <AnimatedTab 
                onClick={() => handleTabClick('anime')} 
                className="text-white font-semibold px-4 py-3 rounded-lg hover:bg-cyan-500/20 hover:text-cyan-300 transition text-left w-full"
              >
                ANIME
              </AnimatedTab>
              <AnimatedTab 
                onClick={() => handleTabClick('live')} 
                className="text-white font-semibold px-4 py-3 rounded-lg hover:bg-cyan-500/20 hover:text-cyan-300 transition text-left w-full"
              >
                🔴 LIVE TV
              </AnimatedTab>
              <AnimatedTab 
                onClick={() => handleTabClick('discovery')} 
                className="text-white font-semibold px-4 py-3 rounded-lg hover:bg-cyan-500/20 hover:text-cyan-300 transition text-left w-full"
              >
                🎯 DISCOVERY
              </AnimatedTab>
              {/* Remove or comment out GAMES tab if not implemented */}
              {isAuthenticated && (
                <AnimatedTab
                  to="/watchlist"
                  className="text-white font-semibold px-4 py-3 rounded-lg hover:bg-cyan-500/20 hover:text-cyan-300 transition text-left w-full block md:hidden"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  WATCHLIST
                </AnimatedTab>
              )}
            </nav>
          </div>
        </div>
      )}
      
      {/* Modals */}
      <UserMenu 
        isOpen={userMenuOpen} 
        onClose={() => setUserMenuOpen(false)} 
        user={user} 
        profile={profile}
        onLogout={handleLogout}
        onSwitchProfile={handleSwitchProfile}
        onEditProfile={handleEditProfile}
      />
      
      <LoginModal 
        isOpen={loginModalOpen} 
        onClose={() => setLoginModalOpen(false)}
        onSwitchToRegister={() => {
          setLoginModalOpen(false);
          setRegisterModalOpen(true);
        }}
      />
      
      <RegisterModal 
        isOpen={registerModalOpen} 
        onClose={() => setRegisterModalOpen(false)}
        onSwitchToLogin={() => {
          setRegisterModalOpen(false);
          setLoginModalOpen(true);
        }}
      />
      
      <EditProfileModal 
        isOpen={editProfileModalOpen} 
        onClose={() => setEditProfileModalOpen(false)}
      />
      
      <PerformanceDashboard
        isOpen={performanceDashboardOpen}
        onClose={() => setPerformanceDashboardOpen(false)}
      />
    </header>
  );
};

export default Header; 