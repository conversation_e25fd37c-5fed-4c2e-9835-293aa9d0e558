name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'

jobs:
  # Security audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          cd server
          npm ci
          
      - name: Run security audit
        run: |
          cd server
          npm audit --audit-level=moderate
          
      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: HEAD~1

  # Backend tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: torvie_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install backend dependencies
        run: |
          cd server
          npm ci
          
      - name: Setup test environment
        run: |
          cd server
          echo "DATABASE_URL=postgresql://postgres:test_password@localhost:5432/torvie_test" >> .env.test
          echo "REDIS_URL=redis://localhost:6379" >> .env.test
          echo "JWT_SECRET=test-jwt-secret" >> .env.test
          echo "TMDB_API_KEY=test-api-key" >> .env.test
          echo "NODE_ENV=test" >> .env.test
          
      - name: Run database migrations
        run: |
          cd server
          npm run migrate:test
        env:
          DATABASE_URL: postgresql://postgres:test_password@localhost:5432/torvie_test
          
      - name: Run backend tests
        run: |
          cd server
          npm test -- --coverage --watchAll=false
        env:
          DATABASE_URL: postgresql://postgres:test_password@localhost:5432/torvie_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
          TMDB_API_KEY: test-api-key
          NODE_ENV: test
          
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./server/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Frontend tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install frontend dependencies
        run: |
          cd client
          npm ci
          
      - name: Run frontend tests
        run: |
          cd client
          npm test -- --coverage --watchAll=false
          
      - name: Upload frontend coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./client/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # Linting
  lint:
    name: Lint Code
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          cd server && npm ci
          cd ../client && npm ci
          
      - name: Lint backend
        run: |
          cd server
          npm run lint
          
      - name: Lint frontend
        run: |
          cd client
          npm run lint
          
      - name: Check formatting
        run: |
          cd server && npm run format:check
          cd ../client && npm run format:check

  # Build and test Docker images
  docker-build:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [lint, backend-tests, frontend-tests]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Build backend image
        run: |
          cd server
          docker build -t torvie-backend:test .
          
      - name: Build frontend image
        run: |
          cd client
          docker build -t torvie-frontend:test .
          
      - name: Test Docker Compose
        run: |
          docker-compose -f docker-compose.yml config

  # Security scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [docker-build]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'torvie-backend:test'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Performance testing
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: [docker-build]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install artillery
        run: npm install -g artillery
        
      - name: Run performance tests
        run: |
          artillery run server/tests/performance/load-test.yml
        env:
          TARGET_URL: http://localhost:3000

  # Deploy to staging (on main branch)
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [security-audit, backend-tests, frontend-tests, lint, docker-build, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your staging deployment logic here
          # Example: kubectl apply -f k8s/staging/
          # or: docker-compose -f docker-compose.staging.yml up -d

  # Deploy to production (manual trigger)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [security-audit, backend-tests, frontend-tests, lint, docker-build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'workflow_dispatch'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your production deployment logic here
          # Example: kubectl apply -f k8s/production/
          # or: docker-compose -f docker-compose.prod.yml up -d

  # Notify on failure
  notify-failure:
    name: Notify on Failure
    runs-on: ubuntu-latest
    needs: [security-audit, backend-tests, frontend-tests, lint, docker-build, security-scan]
    if: failure()
    
    steps:
      - name: Notify failure
        run: |
          echo "One or more jobs failed. Check the logs for details."
          # Add your notification logic here (Slack, email, etc.) 