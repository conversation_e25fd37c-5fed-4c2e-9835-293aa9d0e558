{"name": "torvie-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext .jsx,.js", "format": "prettier --write src"}, "dependencies": {"fuse.js": "^7.1.0", "hls.js": "^1.6.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.2.5", "tailwindcss": "^3.3.6", "vite": "^5.2.0"}}