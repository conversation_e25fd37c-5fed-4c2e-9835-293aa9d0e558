---
description: "Standards and default stack for all frontend development (<PERSON>act, Next.js, TypeScript)."
globs:
  - "**/*.tsx"
  - "**/*.jsx"
  - "**/*.css"
  - "**/tailwind.config.*"
alwaysApply: false
---

# Frontend Standards

## A. Core Principles
* **Beautiful & Intuitive UX:** Prioritize a fast, smooth, and aesthetically pleasing user experience.
* **Responsive Design:** This is a default requirement for all components.
* **Accessibility (a11y):** Ensure all components are accessible.

## B. Technology Canon
* **Framework:** React with TypeScript.
* **Application Shell/SSR:** Next.js.
* **State Management:** Favor component-level state or React Context. Use Zustand for complex global state.
* **Testing:** Jest for unit tests, Playwright for End-to-End (E2E) tests.