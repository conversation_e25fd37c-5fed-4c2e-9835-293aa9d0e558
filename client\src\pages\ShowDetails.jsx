import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import MediaCard from '../components/MediaCard';
import EnhancedVideoPlayer from '../components/EnhancedVideoPlayer';
import TrailerPlayer from '../components/TrailerPlayer';
import ActorModal from '../components/ActorModal';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import { watchlistStorage } from '../utils/watchlistStorage';
import { apiFetch } from '../utils/api';

const genreIdMap = { 
  10759: "Action & Adventure", 16: "Animation", 35: "Comedy", 80: "Crime", 
  99: "Documentary", 18: "Drama", 10751: "Family", 10762: "Kids", 9648: "Mystery", 
  10763: "News", 10764: "Reality", 10765: "Sci-Fi & Fantasy", 10766: "Soap", 
  10767: "Talk", 10768: "War & Politics", 37: "Western" 
};

const GenrePill = ({ genreId }) => (
    <span className="text-sm bg-gray-700/50 text-gray-300 px-3 py-1 rounded-full">
        {genreIdMap[genreId] || 'Genre'}
    </span>
);

const ShowDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { profile: currentProfile } = useProfile();
  const { user } = useAuth();
  const [show, setShow] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [credits, setCredits] = useState(null);
  const [videos, setVideos] = useState([]);
  const [isInWatchlist, setIsInWatchlist] = useState(false);
  const [episodes, setEpisodes] = useState({}); // { season: { episode: torrent } }
  const [loadingEpisodes, setLoadingEpisodes] = useState(false);
  const [videoPlayer, setVideoPlayer] = useState({ isOpen: false, magnetLink: null, title: null, contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  const [seasons, setSeasons] = useState([]); // TMDB season metadata
  const [seasonEpisodes, setSeasonEpisodes] = useState({}); // { season: [episodes] }
  const [actorModal, setActorModal] = useState({ isOpen: false, actorId: null });
  const [selectedSeason, setSelectedSeason] = useState(1); // Track selected season

  useEffect(() => {
    fetchShowDetails();
    fetchExtraData();
  }, [id]);

  // Fetch episodes when show data is loaded
  useEffect(() => {
    if (show && show.name) {
      fetchAllEpisodes();
    }
  }, [show]);

  // Set default selected season when seasons are loaded
  useEffect(() => {
    if (seasons.length > 0 && !seasons.find(s => s.season_number === selectedSeason)) {
      // If the current selected season doesn't exist, select the first available season
      setSelectedSeason(seasons[0].season_number);
    }
  }, [seasons, selectedSeason]);

  // Watchlist effect: check if show is in watchlist
  useEffect(() => {
    const checkWatchlist = async () => {
      if (show && show.id && user && currentProfile && currentProfile.id) {
        console.log(`🏠 ShowDetails: Checking watchlist for show "${show.name}"`);
        const inList = await watchlistStorage.isInWatchlist(user.id, currentProfile.id, show.id, 'tv');
        console.log(`📋 ShowDetails: Show "${show.name}" ${inList ? 'IS' : 'IS NOT'} in watchlist`);
        setIsInWatchlist(inList);
      } else {
        console.log(`📋 ShowDetails: No user/profile or show, setting watchlist to false`);
        setIsInWatchlist(false);
      }
    };
    
    checkWatchlist();
  }, [show, user, currentProfile]);

  const toggleWatchlist = async () => {
    if (!show || !show.id || !user || !currentProfile || !currentProfile.id) {
      console.log('❌ ShowDetails: Cannot toggle watchlist - missing data');
      return;
    }
    
    console.log(`🏠 ShowDetails: Toggling watchlist for show "${show.name}"`);
    
    let success;
    if (isInWatchlist) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, show.id, 'tv');
      if (success) {
        setIsInWatchlist(false);
        console.log(`➖ ShowDetails: Removed "${show.name}" from watchlist`);
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, { ...show, media_type: 'tv' });
      if (success) {
        setIsInWatchlist(true);
        console.log(`➕ ShowDetails: Added "${show.name}" to watchlist`);
      }
    }
    
    if (!success) {
      console.error(`❌ ShowDetails: Failed to toggle watchlist for "${show.name}"`);
    }
  };

  const fetchShowDetails = async () => {
    try {
      setLoading(true);
      const response = await apiFetch(`/tv/${id}`);
      if (!response.ok) throw new Error("Show not found");
      const data = await response.json();
      setShow(data);
      if (data && data.seasons) {
        fetchAllSeasonsAndEpisodes(data.id, data.seasons);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchExtraData = async () => {
    try {
      // Credits
      const creditsRes = await apiFetch(`/tv/${id}/credits`);
      const creditsData = await creditsRes.json();
      setCredits(creditsData);
      // Videos
      const videosRes = await apiFetch(`/tv/${id}/videos`);
      const videosData = await videosRes.json();
      setVideos(videosData.results || []);
    } catch (err) {
      // Fail silently for extra data
    }
  };

  const fetchAllEpisodes = async () => {
    if (!show || !show.name) return;
    
    setLoadingEpisodes(true);
    try {
      const response = await apiFetch(`/search?query=${encodeURIComponent(show.name)}&type=tv&allEpisodes=true`);
      const data = await response.json();
      
      if (data.results && Object.keys(data.results).length > 0) {
        const organizedEpisodes = {};
        Object.entries(data.results).forEach(([key, torrent]) => {
          const match = key.match(/S(\d+)E(\d+)/i);
          if (match) {
            const season = parseInt(match[1]);
            const episode = parseInt(match[2]);
            if (!organizedEpisodes[season]) organizedEpisodes[season] = {};
            organizedEpisodes[season][episode] = torrent;
          }
        });
        setEpisodes(organizedEpisodes);
      }
    } catch (error) {
      console.error('Error fetching episodes:', error);
    } finally {
      setLoadingEpisodes(false);
    }
  };

  const handlePlayEpisode = (season, episode) => {
    const torrent = episodes[season]?.[episode];
    if (torrent) {
      setVideoPlayer({ 
        isOpen: true, 
        magnetLink: torrent.magnet, 
        title: `${show.name} S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}`,
        contentId: `tv_${show.id}_s${season}_e${episode}`, // Unique ID for each episode
        contentType: 'tv',
        posterUrl: show.poster_path ? `https://image.tmdb.org/t/p/w500${show.poster_path}` : null,
        episodeInfo: { season, episode, episodeTitle: `S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}` }
      });
    }
  };

  const closeVideoPlayer = () => {
    setVideoPlayer({ isOpen: false, magnetLink: null, title: null, contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  };

  const handleActorClick = (actorId) => {
    setActorModal({ isOpen: true, actorId });
  };

  const closeActorModal = () => {
    setActorModal({ isOpen: false, actorId: null });
  };

  // Helper to get genres from both show.genres and show.genre_ids
  const getGenres = () => {
    if (show.genres && Array.isArray(show.genres) && show.genres.length > 0) {
      return show.genres.map(g => g.name);
    }
    if (show.genre_ids && Array.isArray(show.genre_ids) && show.genre_ids.length > 0) {
      return show.genre_ids.map(id => genreIdMap[id] || id);
    }
    return [];
  };

  // Helper to get the best video (trailer > teaser > clip)
  const getBestVideo = () => {
    if (!videos || !Array.isArray(videos)) return null;
    const trailer = videos.find(v => v.type === 'Trailer' && v.site === 'YouTube');
    if (trailer) return trailer;
    const teaser = videos.find(v => v.type === 'Teaser' && v.site === 'YouTube');
    if (teaser) return teaser;
    const clip = videos.find(v => v.type === 'Clip' && v.site === 'YouTube');
    if (clip) return clip;
    return null;
  };

  // Fetch all seasons and their episodes from TMDB
  const fetchAllSeasonsAndEpisodes = async (showId, tmdbSeasons) => {
    const allSeasons = [];
    const allSeasonEpisodes = {};
    for (const season of tmdbSeasons) {
      if (season.season_number === 0) continue; // skip specials
      const res = await apiFetch(`/tv/${showId}/season/${season.season_number}`);
      const data = await res.json();
      allSeasons.push(season);
      allSeasonEpisodes[season.season_number] = data.episodes || [];
    }
    setSeasons(allSeasons);
    setSeasonEpisodes(allSeasonEpisodes);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading show details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <p className="text-red-400 mb-4">{error}</p>
          <button 
            onClick={() => window.history.back()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const backdropUrl = show.backdrop_path ? `https://image.tmdb.org/t/p/original${show.backdrop_path}` : 'https://placehold.co/1920x1080?text=No+Backdrop';
  const posterUrl = show.poster_path ? `https://image.tmdb.org/t/p/w500${show.poster_path}` : 'https://placehold.co/300x450?text=No+Image';

  return (
    <div className="p-4 sm:p-6">
      {/* Hero Section */}
      <div className="relative h-[60vh] rounded-lg overflow-hidden mb-8">
        <div 
          className="absolute inset-0 bg-cover bg-center" 
          style={{backgroundImage: `url(${backdropUrl})`}}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
        
        {/* Back Button */}
        <button 
          onClick={() => {
            // Check if user came from search results
            const lastSearchQuery = sessionStorage.getItem('lastSearchQuery');
            if (lastSearchQuery) {
              navigate(`/search?q=${encodeURIComponent(lastSearchQuery)}`);
            } else {
              navigate('/shows');
            }
          }}
          className="absolute top-6 left-6 z-10 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>

        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="flex flex-col md:flex-row gap-8 items-center min-h-[340px]">
            {/* Poster */}
            <div className="flex-shrink-0 overflow-visible" style={{ padding: '40px 32px', margin: '0 24px' }}>
              <MediaCard
                media={show}
                showActions={false}
                className="w-48 h-72"
              />
            </div>
            
            {/* Show Info */}
            <div className="flex-1">
              <h1 className="text-5xl font-black tracking-tighter mb-4">{show.name}</h1>
              <div className="flex flex-wrap gap-2 mb-4">
                {getGenres().map(genre => (
                  <GenrePill key={genre} genreId={genre} />
                ))}
              </div>
              <p className="text-lg text-gray-300 max-w-3xl mb-6">{show.overview}</p>
              
              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4">
                <button 
                  className="bg-white text-black font-bold py-3 px-8 rounded-lg flex items-center gap-2 transform hover:scale-105 transition-transform"
                  onClick={() => {
                    // Play the first available episode (S01E01)
                    const firstSeason = Object.keys(episodes).sort((a, b) => parseInt(a) - parseInt(b))[0];
                    if (firstSeason) {
                      const firstEpisode = Object.keys(episodes[firstSeason]).sort((a, b) => parseInt(a) - parseInt(b))[0];
                      if (firstEpisode) {
                        handlePlayEpisode(parseInt(firstSeason), parseInt(firstEpisode));
                      } else {
                        alert('No episodes available for this show.');
                      }
                    } else {
                      alert('No episodes available for this show.');
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"></path>
                  </svg>
                  Watch Now
                </button>
                <button
                  className={`bg-gray-800 text-white font-bold py-3 px-8 rounded-lg flex items-center gap-2 hover:bg-gray-700 transition-colors ${isInWatchlist ? 'border border-yellow-400' : ''}`}
                  onClick={toggleWatchlist}
                >
                  {isInWatchlist ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="yellow">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  )}
                  {isInWatchlist ? 'In Watchlist' : 'Add to Watchlist'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Trailer Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Trailer</h2>
        <div className="bg-gray-900/50 rounded-lg p-4 flex items-center justify-center w-full">
          {(() => {
            const bestVideo = getBestVideo();
            if (bestVideo) {
              return (
                <div className="w-full aspect-video" style={{ maxWidth: '1200px', height: '600px' }}>
                  <TrailerPlayer videoId={bestVideo.key} title={bestVideo.name} />
                </div>
              );
            } else {
              return <p className="text-gray-400 text-center">No trailer, teaser, or clip available.</p>;
            }
          })()}
        </div>
      </div>

      {/* Cast & Crew */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Cast & Crew</h2>
        <div className="bg-gray-900/50 rounded-lg p-6">
          {/* Cast */}
          {credits && credits.cast && credits.cast.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Top Cast</h3>
              <div className="flex flex-wrap gap-4 mb-4">
                {credits.cast.slice(0, 10).map(actor => (
                  <div 
                    key={actor.cast_id || actor.credit_id} 
                    className="flex flex-col items-center w-24 cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => handleActorClick(actor.id)}
                  >
                    <img
                      src={actor.profile_path ? `https://image.tmdb.org/t/p/w185${actor.profile_path}` : 'https://placehold.co/80x120?text=No+Image'}
                      alt={actor.name}
                      className="w-20 h-28 object-cover rounded-lg mb-1 bg-gray-800"
                    />
                    <span className="text-xs text-white font-bold text-center truncate w-full">{actor.name}</span>
                    <span className="text-xs text-gray-400 text-center truncate w-full">{actor.character}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          {/* Crew */}
          {credits && credits.crew && credits.crew.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Crew</h3>
              <div className="flex flex-wrap gap-4">
                {credits.crew.filter(c => c.job === 'Creator').slice(0, 5).map(creator => (
                  <div key={creator.credit_id} className="flex flex-col items-center w-24">
                    <span className="text-xs text-white font-bold text-center">Creator</span>
                    <span className="text-xs text-gray-400 text-center truncate w-full">{creator.name}</span>
                  </div>
                ))}
                {credits.crew.filter(c => c.job === 'Director').slice(0, 5).map(director => (
                  <div key={director.credit_id} className="flex flex-col items-center w-24">
                    <span className="text-xs text-white font-bold text-center">Director</span>
                    <span className="text-xs text-gray-400 text-center truncate w-full">{director.name}</span>
                  </div>
                ))}
                {credits.crew.filter(c => c.job === 'Writer' || c.job === 'Screenplay').slice(0, 5).map(writer => (
                  <div key={writer.credit_id} className="flex flex-col items-center w-24">
                    <span className="text-xs text-white font-bold text-center">{writer.job}</span>
                    <span className="text-xs text-gray-400 text-center truncate w-full">{writer.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          {/* If both cast and crew are empty, show message */}
          {(!credits || ((!credits.cast || credits.cast.length === 0) && (!credits.crew || credits.crew.length === 0))) && (
            <p className="text-gray-400 mb-2">No cast or crew information available.</p>
          )}
        </div>
      </div>

      {/* Episodes Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Episodes</h2>
        <div className="bg-gray-900/50 rounded-lg p-6">
          {loadingEpisodes ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-gray-400">Loading episodes...</p>
            </div>
          ) : seasons.length > 0 ? (
            <div className="space-y-6">
              {/* Season Selector */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between border-b border-gray-700 pb-4">
                <div className="flex flex-wrap gap-2">
                  {seasons.map(season => {
                    const episodeCount = seasonEpisodes[season.season_number]?.length || 0;
                    const availableCount = (seasonEpisodes[season.season_number] || []).filter(ep => 
                      episodes[season.season_number]?.[ep.episode_number]
                    ).length;
                    
                    return (
                      <button
                        key={season.season_number}
                        onClick={() => setSelectedSeason(season.season_number)}
                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                          selectedSeason === season.season_number
                            ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
                        }`}
                      >
                        Season {season.season_number}
                        <span className="ml-1 text-xs opacity-75">
                          ({availableCount}/{episodeCount})
                        </span>
                      </button>
                    );
                  })}
                </div>
                
                {/* Season Info */}
                {(() => {
                  const currentSeason = seasons.find(s => s.season_number === selectedSeason);
                  const episodeCount = seasonEpisodes[selectedSeason]?.length || 0;
                  const availableCount = (seasonEpisodes[selectedSeason] || []).filter(ep => 
                    episodes[selectedSeason]?.[ep.episode_number]
                  ).length;
                  
                  return currentSeason && (
                    <div className="text-right">
                      <h3 className="text-xl font-bold text-white">
                        Season {selectedSeason}
                      </h3>
                      <p className="text-sm text-gray-400">
                        {episodeCount} episodes • {availableCount} available to stream
                      </p>
                      {currentSeason.air_date && (
                        <p className="text-xs text-gray-500">
                          Aired: {new Date(currentSeason.air_date).getFullYear()}
                        </p>
                      )}
                    </div>
                  );
                })()}
              </div>

              {/* Selected Season Episodes */}
              {(() => {
                const currentSeasonEpisodes = seasonEpisodes[selectedSeason] || [];
                
                if (currentSeasonEpisodes.length === 0) {
                  return (
                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">📺</div>
                      <h3 className="text-xl text-white mb-2">No Episodes Found</h3>
                      <p className="text-gray-400">Season {selectedSeason} episodes are not available yet.</p>
                    </div>
                  );
                }

                return (
                  <div className="space-y-4">
                    {/* Episodes Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      {currentSeasonEpisodes.map(ep => {
                        const torrent = episodes[selectedSeason]?.[ep.episode_number];
                        const stillUrl = ep.still_path 
                          ? `https://image.tmdb.org/t/p/w500${ep.still_path}` 
                          : 'https://placehold.co/500x281?text=No+Image';
                        
                        return (
                          <div 
                            key={ep.id} 
                            className="bg-gray-800 rounded-lg overflow-hidden shadow-lg flex flex-col h-full group cursor-pointer transform hover:scale-105 transition-all duration-300 hover:shadow-xl"
                          >
                            <div className="relative">
                              <img 
                                src={stillUrl} 
                                alt={ep.name} 
                                className="w-full h-40 object-cover" 
                              />
                              {/* Episode Number Overlay */}
                              <div className="absolute top-2 left-2 bg-black/70 text-white text-xs font-bold px-2 py-1 rounded">
                                E{ep.episode_number}
                              </div>
                              
                              {/* Play Button Overlay */}
                              {torrent && (
                                <div className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                  <button 
                                    title="Play Episode"
                                    className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm transform hover:scale-110 hover:bg-white/30 transition-all duration-200 border-2 border-white/30"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                      handlePlayEpisode(selectedSeason, ep.episode_number);
                                    }}
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="text-white ml-1">
                                      <path d="M8 5v14l11-7z"></path>
                                    </svg>
                                  </button>
                                </div>
                              )}
                              
                              {/* Availability Badge */}
                              <div className={`absolute top-2 right-2 text-xs font-bold px-2 py-1 rounded ${
                                torrent ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                              }`}>
                                {torrent ? '✓ Available' : '✗ Unavailable'}
                              </div>
                            </div>
                            
                            <div className="flex-1 flex flex-col p-4">
                              <div className="flex justify-between items-start mb-2">
                                <h4 className="text-white font-bold text-sm leading-tight flex-1 mr-2">
                                  {ep.name}
                                </h4>
                                {torrent && (
                                  <span className="text-green-400 text-xs whitespace-nowrap">
                                    {torrent.seeders} seeders
                                  </span>
                                )}
                              </div>
                              
                              <p className="text-gray-400 text-xs mb-3 line-clamp-3 flex-1">
                                {ep.overview || 'No summary available.'}
                              </p>
                              
                              <div className="mt-auto space-y-2">
                                <div className="flex justify-between items-center text-xs">
                                  <span className="text-gray-500">
                                    {ep.air_date ? new Date(ep.air_date).toLocaleDateString() : 'TBA'}
                                  </span>
                                  {ep.runtime && (
                                    <span className="text-gray-500">
                                      {ep.runtime}m
                                    </span>
                                  )}
                                </div>
                                
                                {ep.vote_average > 0 && (
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <svg className="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                      </svg>
                                      <span className="text-xs text-gray-400">
                                        {ep.vote_average.toFixed(1)}
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    
                    {/* Season Statistics */}
                    <div className="mt-6 pt-4 border-t border-gray-700">
                      <div className="flex flex-wrap gap-6 text-sm text-gray-400">
                        <div>
                          <span className="text-white font-medium">{currentSeasonEpisodes.length}</span> episodes total
                        </div>
                        <div>
                          <span className="text-green-400 font-medium">
                            {currentSeasonEpisodes.filter(ep => episodes[selectedSeason]?.[ep.episode_number]).length}
                          </span> available to stream
                        </div>
                        <div>
                          <span className="text-red-400 font-medium">
                            {currentSeasonEpisodes.filter(ep => !episodes[selectedSeason]?.[ep.episode_number]).length}
                          </span> not available
                        </div>
                        {(() => {
                          const totalRuntime = currentSeasonEpisodes.reduce((acc, ep) => acc + (ep.runtime || 0), 0);
                          return totalRuntime > 0 && (
                            <div>
                              <span className="text-white font-medium">{Math.round(totalRuntime / 60)}h {totalRuntime % 60}m</span> total runtime
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📺</div>
              <h3 className="text-xl text-white mb-2">No Episodes Available</h3>
              <p className="text-gray-400">Episodes for this show are not available yet.</p>
            </div>
          )}
        </div>
      </div>

      {/* Video Player */}
      {videoPlayer.isOpen && (
        <EnhancedVideoPlayer
          magnetLink={videoPlayer.magnetLink}
          title={videoPlayer.title}
          onClose={closeVideoPlayer}
          contentId={videoPlayer.contentId}
          contentType={videoPlayer.contentType}
          posterUrl={videoPlayer.posterUrl}
          episodeInfo={videoPlayer.episodeInfo}
        />
      )}

      {/* Actor Modal */}
      <ActorModal
        isOpen={actorModal.isOpen}
        onClose={closeActorModal}
        actorId={actorModal.actorId}
      />
    </div>
  );
};

export default ShowDetails; 