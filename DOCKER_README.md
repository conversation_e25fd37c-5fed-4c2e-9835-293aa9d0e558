# Torvie Docker Setup

This document provides instructions for running Torvie using Docker for both production and development environments.

## Prerequisites

- Docker and Docker Compose installed
- TMDB API key (optional, for enhanced content discovery)

## Quick Start

### 1. Environment Setup

```bash
# Copy the example environment file
cp env.example .env

# Edit .env and set your configuration
# At minimum, update JWT_SECRET and TMDB_API_KEY
```

### 2. Production Deployment

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Access the application
# Frontend: http://localhost
# Backend API: http://localhost:3000
```

### 3. Development Environment

```bash
# Start development services with hot reloading
docker-compose --profile dev up -d

# Access development servers
# Frontend: http://localhost:5173
# Backend: http://localhost:3001
```

## Service Architecture

### Production Services
- **frontend**: React app served by <PERSON>inx (port 80)
- **backend**: Node.js/Express API (port 3000)

### Development Services
- **frontend-dev**: React dev server with hot reloading (port 5173)
- **backend-dev**: Node.js with nodemon (port 3001)

## Docker Commands

### Basic Operations
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Rebuild and start
docker-compose up -d --build

# View service status
docker-compose ps

# View logs
docker-compose logs [service-name]

# Execute commands in containers
docker-compose exec backend npm run test
docker-compose exec frontend ls -la
```

### Development Commands
```bash
# Start development environment
docker-compose --profile dev up -d

# View development logs
docker-compose --profile dev logs -f

# Rebuild development containers
docker-compose --profile dev up -d --build
```

### Data Management
```bash
# Backup data volumes
docker run --rm -v torvie_storage:/data -v $(pwd):/backup alpine tar czf /backup/torvie-storage-backup.tar.gz -C /data .

# Restore data volumes
docker run --rm -v torvie_storage:/data -v $(pwd):/backup alpine tar xzf /backup/torvie-storage-backup.tar.gz -C /data

# Remove all data (WARNING: This will delete all user data)
docker-compose down -v
```

## Environment Variables

### Required
- `JWT_SECRET`: Secret key for JWT token signing
- `TMDB_API_KEY`: TMDB API key for content discovery

### Optional
- `NODE_ENV`: Environment (production/development)
- `PORT`: Backend port (default: 3000)
- `CORS_ORIGIN`: Allowed CORS origin

## Volumes

- `torvie-storage`: User profiles, watchlists, and progress data
- `torvie-media`: Downloaded media files

## Health Checks

Both services include health checks:
- Frontend: HTTP GET to `/health`
- Backend: HTTP GET to `/api/health`

Check health status:
```bash
docker-compose ps
```

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   netstat -tulpn | grep :80
   netstat -tulpn | grep :3000
   
   # Modify ports in docker-compose.yml if needed
   ```

2. **Permission issues**
   ```bash
   # Fix volume permissions
   docker-compose down
   sudo chown -R $USER:$USER ./server/storage ./server/downloads
   docker-compose up -d
   ```

3. **Build failures**
   ```bash
   # Clean build cache
   docker system prune -a
   docker-compose build --no-cache
   ```

4. **Service won't start**
   ```bash
   # Check logs for errors
   docker-compose logs [service-name]
   
   # Check health status
   docker-compose ps
   ```

### Performance Optimization

1. **Build optimization**
   ```bash
   # Use build cache
   docker-compose build --parallel
   
   # Multi-stage builds are already configured
   ```

2. **Resource limits**
   ```bash
   # Add to docker-compose.yml services
   deploy:
     resources:
       limits:
         memory: 1G
         cpus: '0.5'
   ```

## Production Deployment

### Security Considerations
1. Change default JWT_SECRET
2. Set up proper CORS origins
3. Use HTTPS in production
4. Configure rate limiting
5. Set up proper logging

### Scaling
```bash
# Scale backend service
docker-compose up -d --scale backend=3

# Use external load balancer for frontend
```

### Monitoring
```bash
# View resource usage
docker stats

# Monitor logs
docker-compose logs -f --tail=100
```

## Development Workflow

1. **Start development environment**
   ```bash
   docker-compose --profile dev up -d
   ```

2. **Make code changes** - Hot reloading is enabled

3. **Run tests**
   ```bash
   docker-compose exec backend-dev npm test
   ```

4. **Build for production**
   ```bash
   docker-compose build
   ```

## Support

For issues related to Docker setup:
1. Check the troubleshooting section
2. Review logs: `docker-compose logs`
3. Verify environment configuration
4. Check Docker and Docker Compose versions 