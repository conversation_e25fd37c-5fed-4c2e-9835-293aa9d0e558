---
description: "A manually triggered workflow to generate a perfect, comprehensive Pull Request (PR) description after a task is complete."
alwaysApply: false
---

# The Perfect Pull Request (PR) Generator

When I invoke you with **@create-pr-description**, you will analyze the changes you've made and generate a complete PR description in Markdown format.

The description MUST follow this exact structure:

---

### ticket: [TICKET-NUMBER]

### Context & Problem
*A brief, clear explanation of the problem being solved. Why is this change necessary? What was the state of the world before this PR?*

### Solution
*A summary of the technical approach taken. What was the core logic of your implementation? What major decisions did you make?*

### How to Test
*A clear, step-by-step set of instructions for how a human reviewer can manually verify that your changes work correctly.*
1.  `...`
2.  `...`
3.  `...`

### Test Coverage
*A summary of the automated tests that have been added or updated in this PR.*

### Screenshots / GIFs
*(You will insert placeholders here reminding me to add visual aids if the changes affect the UI.)*
``

### Follow-up Tasks
*Are there any new tech debt items, future improvements, or related tasks that this PR introduces?*iption:
globs:
alwaysApply: false
---
