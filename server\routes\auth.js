import express from 'express';
import { 
  hashPassword, 
  verifyPassword, 
  generateToken, 
  verifyToken,
  generateRefreshToken,
  validatePasswordStrength,
  validateUsername,
  createAuthRateLimiter,
  logAuthEvent
} from '../utils/auth.js';
import { createUser, getUserByUsername } from '../utils/database.js';
import { validate, loginSchema, registerSchema } from '../middleware/validation.js';
import { sanitizeInput } from '../middleware/validation.js';
import { logger, securityLogger } from '../utils/logger.js';

const router = express.Router();
const DEV_AUTH_DISABLED = process.env.DEV_AUTH === 'false';

// In-memory storage for refresh tokens (in production, use Redis or database)
const refreshTokens = new Map();

// Rate limiter for authentication attempts
const authRateLimiter = createAuthRateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes

// POST /api/auth/register - Register new user
router.post('/register', 
  sanitizeInput,
  validate(registerSchema),
  async (req, res) => {
    if (DEV_AUTH_DISABLED) {
      return res.status(503).json({ 
        success: false, 
        error: 'Registration disabled in DEV_AUTH=false mode' 
      });
    }

    try {
      const { username, email, password, displayName } = req.body;
      const JWT_SECRET = process.env.JWT_SECRET;
      
      if (!JWT_SECRET) {
        logger.error('JWT_SECRET not configured');
        return res.status(500).json({ 
          success: false, 
          error: 'Server misconfiguration' 
        });
      }

      // Validate password strength
      const passwordValidation = validatePasswordStrength(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Password validation failed',
          details: passwordValidation.errors
        });
      }

      // Validate username format
      const usernameValidation = validateUsername(username);
      if (!usernameValidation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Username validation failed',
          details: usernameValidation.errors
        });
      }

      const existingUser = await getUserByUsername(username);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: 'Username already exists'
        });
      }

      // Hash password
      const hashedPassword = await hashPassword(password);

      // Save user to database
      const dbUser = await createUser({
        username,
        email,
        password_hash: hashedPassword,
        display_name: displayName || username
      });

      // Generate tokens
      const accessToken = generateToken(
        { id: dbUser.id, username: dbUser.username },
        JWT_SECRET
      );
      
      const refreshToken = generateRefreshToken(dbUser.id, JWT_SECRET);
      
      // Store refresh token
      refreshTokens.set(refreshToken, {
        userId: dbUser.id,
        createdAt: new Date()
      });

      // Log successful registration
      logAuthEvent('user_registered', dbUser.id, req.ip, true, {
        username: dbUser.username,
        userAgent: req.get('User-Agent')
      });

      res.json({
        success: true,
        user: {
          id: dbUser.id,
          username: dbUser.username,
          email: dbUser.email,
          displayName: dbUser.display_name
        },
        accessToken,
        refreshToken
      });

    } catch (error) {
      logger.error({ 
        error: error.message, 
        context: 'register',
        ip: req.ip 
      });
      
      res.status(500).json({
        success: false,
        error: 'Registration failed'
      });
    }
  }
);

// POST /api/auth/login - Authenticate user
router.post('/login', 
  sanitizeInput,
  validate(loginSchema),
  authRateLimiter,
  async (req, res) => {
    if (DEV_AUTH_DISABLED) {
      return res.status(503).json({ 
        success: false, 
        error: 'Authentication disabled in DEV_AUTH=false mode' 
      });
    }

    try {
      const { username, password } = req.body;
      const JWT_SECRET = process.env.JWT_SECRET;
      
      if (!JWT_SECRET) {
        logger.error('JWT_SECRET not configured');
        return res.status(500).json({ 
          success: false, 
          error: 'Server misconfiguration' 
        });
      }

      // Get user from database
      const user = await getUserByUsername(username);
      
      if (!user) {
        logAuthEvent('login_failed', null, req.ip, false, {
          username,
          reason: 'user_not_found',
          userAgent: req.get('User-Agent')
        });
        
        return res.status(401).json({ 
          success: false, 
          error: 'Invalid credentials' 
        });
      }

      // Verify password
      const isValidPassword = await verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        logAuthEvent('login_failed', user.id, req.ip, false, {
          username: user.username,
          reason: 'invalid_password',
          userAgent: req.get('User-Agent')
        });
        
        return res.status(401).json({ 
          success: false, 
          error: 'Invalid credentials' 
        });
      }

      // Generate tokens
      const accessToken = generateToken(
        { id: user.id, username: user.username },
        JWT_SECRET
      );
      
      const refreshToken = generateRefreshToken(user.id, JWT_SECRET);
      
      // Store refresh token
      refreshTokens.set(refreshToken, {
        userId: user.id,
        createdAt: new Date()
      });

      // Log successful login
      logAuthEvent('login_successful', user.id, req.ip, true, {
        username: user.username,
        userAgent: req.get('User-Agent')
      });

      res.json({
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          displayName: user.display_name
        },
        accessToken,
        refreshToken
      });

    } catch (error) {
      logger.error({ 
        error: error.message, 
        context: 'login',
        ip: req.ip 
      });
      
      res.status(500).json({
        success: false,
        error: 'Authentication failed'
      });
    }
  }
);

// POST /api/auth/refresh - Refresh access token
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const JWT_SECRET = process.env.JWT_SECRET;
    
    if (!refreshToken || !JWT_SECRET) {
      return res.status(400).json({
        success: false,
        error: 'Invalid refresh token'
      });
    }

    // Verify refresh token
    const payload = verifyToken(refreshToken, JWT_SECRET);
    
    if (payload.type !== 'refresh') {
      return res.status(400).json({
        success: false,
        error: 'Invalid token type'
      });
    }

    // Check if refresh token exists in storage
    const storedToken = refreshTokens.get(refreshToken);
    if (!storedToken) {
      return res.status(401).json({
        success: false,
        error: 'Refresh token not found'
      });
    }

    // Get user from database
    const user = { id: payload.userId, username: 'user' }; // Temporary

    // Generate new access token
    const newAccessToken = generateToken(
      { id: user.id, username: user.username },
      JWT_SECRET
    );

    res.json({
      success: true,
      accessToken: newAccessToken
    });

  } catch (error) {
    logger.error({ 
      error: error.message, 
      context: 'refresh_token',
      ip: req.ip 
    });
    
    res.status(401).json({
      success: false,
      error: 'Invalid refresh token'
    });
  }
});

// POST /api/auth/logout - Logout user
router.post('/logout', (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    if (refreshToken) {
      // Remove refresh token from storage
      refreshTokens.delete(refreshToken);
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error({ 
      error: error.message, 
      context: 'logout',
      ip: req.ip 
    });
    
    res.status(500).json({
      success: false,
      error: 'Logout failed'
    });
  }
});

// Auth middleware exported for use after login route is mounted
export function authMiddleware(req, res, next) {
  if (DEV_AUTH_DISABLED) return next();

  const PUBLIC_PATHS = [
    '/api/health',
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/live-tv',
    '/api/trending',
    '/api/discover',
    '/api/discover-anime',
    '/api/top-rated',
    '/api/movies',
    '/api/tv',
    '/api/genres',
    '/api/media',
    '/api/person',
    '/api/search',
    '/favicon.ico',
  ];
  
  if (
    req.method === 'OPTIONS' ||
    PUBLIC_PATHS.some(p => req.path === p || req.path.startsWith(p + '/'))
  ) {
    return next();
  }

  const auth = req.headers['authorization'] || '';
  const JWT_SECRET = process.env.JWT_SECRET;
  const token = auth.startsWith('Bearer ') ? auth.slice(7) : null;
  
  if (!token) {
    securityLogger.warn({
      event: 'missing_token',
      ip: req.ip,
      path: req.path,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(401).json({ 
      success: false, 
      error: 'Missing token' 
    });
  }
  
  if (!JWT_SECRET) {
    logger.error('JWT_SECRET not configured');
    return res.status(500).json({ 
      success: false, 
      error: 'Server misconfiguration' 
    });
  }
  
  try {
    const payload = verifyToken(token, JWT_SECRET);
    req.user = payload;
    return next();
  } catch (error) {
    securityLogger.warn({
      event: 'invalid_token',
      ip: req.ip,
      path: req.path,
      userAgent: req.get('User-Agent'),
      error: error.message
    });
    
    return res.status(403).json({ 
      success: false, 
      error: 'Invalid or expired token' 
    });
  }
}

export default router; 