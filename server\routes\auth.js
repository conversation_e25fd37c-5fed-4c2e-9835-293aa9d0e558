import express from 'express';
import jwt from 'jsonwebtoken';

const router = express.Router();
const DEV_AUTH_DISABLED = process.env.DEV_AUTH === 'false';

// Simple predefined users – mirrors original inline list.
const PREDEFINED_USERS = [
  { id: 1, username: 'Backwood', password: 'Backwood420$' },
  { id: 2, username: 'user2', password: 'user123' },
  { id: 3, username: 'user3', password: 'user123' },
  { id: 4, username: 'user4', password: 'user123' },
  { id: 5, username: 'user5', password: 'user123' },
  { id: 6, username: 'user6', password: 'user123' },
];

// POST /api/auth/login – issues JWT
router.post('/login', (req, res) => {
  if (DEV_AUTH_DISABLED) return res.status(503).json({ success: false, error: 'Auth disabled in DEV_AUTH=false mode' });

  const { username, password } = req.body || {};
  const JWT_SECRET = process.env.JWT_SECRET;
  if (!JWT_SECRET) return res.status(500).json({ success: false, error: 'Server misconfiguration' });
  const user = PREDEFINED_USERS.find(u => u.username === username && u.password === password);
  if (!user) return res.status(401).json({ success: false, error: 'Invalid credentials' });
  const token = jwt.sign({ id: user.id, username: user.username }, JWT_SECRET, { expiresIn: '12h' });
  res.json({ success: true, token });
});

// Auth middleware exported for use after login route is mounted
export function authMiddleware(req, res, next) {
  if (DEV_AUTH_DISABLED) return next();

  const PUBLIC_PATHS = [
    '/api/health',
    '/api/auth/login',
    '/api/live-tv',
    '/api/trending',     // Allow trending content without auth
    '/api/discover',     // Allow content discovery without auth  
    '/api/discover-anime', // Allow anime discovery without auth
    '/api/top-rated',    // Allow top-rated content without auth
    '/api/movies',       // Allow movie details without auth
    '/api/tv',           // Allow TV show details without auth
    '/api/genres',       // Allow genre list without auth
    '/api/media',        // Allow media browsing without auth
    '/api/person',       // Allow actor details without auth
    '/api/search',       // Allow search without auth (for browsing)
    '/favicon.ico',      // Allow favicon requests without auth
  ];
  if (
    req.method === 'OPTIONS' ||
    PUBLIC_PATHS.some(p => req.path === p || req.path.startsWith(p + '/'))
  ) {
    return next();
  }
  const auth = req.headers['authorization'] || '';
  const JWT_SECRET = process.env.JWT_SECRET;
  const token = auth.startsWith('Bearer ') ? auth.slice(7) : null;
  if (!token) return res.status(401).json({ success: false, error: 'Missing token' });
  if (!JWT_SECRET) return res.status(500).json({ success: false, error: 'Server misconfiguration' });
  try {
    const payload = jwt.verify(token, JWT_SECRET);
    req.user = payload;
    return next();
  } catch {
    return res.status(403).json({ success: false, error: 'Invalid or expired token' });
  }
}

export default router; 