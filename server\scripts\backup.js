#!/usr/bin/env node

/**
 * Database Backup and <PERSON><PERSON> for Torvie
 * Provides automated backup and restore functionality for PostgreSQL
 */

import fs from 'fs/promises';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { logger } from '../utils/logger.js';
import { query, testConnection } from '../utils/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BACKUP_DIR = path.join(__dirname, '../backups');

/**
 * Ensure backup directory exists
 */
async function ensureBackupDir() {
  try {
    await fs.mkdir(BACKUP_DIR, { recursive: true });
  } catch (error) {
    logger.error('Failed to create backup directory:', error);
    throw error;
  }
}

/**
 * Parse DATABASE_URL to get connection parameters
 */
function parseDbUrl(url) {
  const dbUrl = new URL(url || process.env.DATABASE_URL);
  return {
    host: dbUrl.hostname,
    port: dbUrl.port || 5432,
    database: dbUrl.pathname.slice(1),
    username: dbUrl.username,
    password: dbUrl.password
  };
}

/**
 * Execute shell command with promise
 */
function execCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Create database backup using pg_dump
 */
async function createBackup(options = {}) {
  const {
    filename,
    format = 'custom', // 'custom', 'plain', 'directory'
    compress = true,
    dataOnly = false,
    schemaOnly = false
  } = options;
  
  try {
    await ensureBackupDir();
    
    const dbConfig = parseDbUrl();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = filename || `torvie-backup-${timestamp}.dump`;
    const backupPath = path.join(BACKUP_DIR, backupFilename);
    
    logger.info(`Creating database backup: ${backupFilename}`);
    
    const args = [
      '--host', dbConfig.host,
      '--port', dbConfig.port,
      '--username', dbConfig.username,
      '--dbname', dbConfig.database,
      '--no-password',
      '--verbose'
    ];
    
    // Add format-specific options
    if (format === 'custom') {
      args.push('--format=custom');
      if (compress) {
        args.push('--compress=9');
      }
    } else if (format === 'directory') {
      args.push('--format=directory');
      args.push('--jobs=4'); // Parallel jobs for faster backup
    } else {
      args.push('--format=plain');
    }
    
    if (dataOnly) {
      args.push('--data-only');
    } else if (schemaOnly) {
      args.push('--schema-only');
    }
    
    args.push('--file', backupPath);
    
    // Set password via environment variable
    const env = {
      ...process.env,
      PGPASSWORD: dbConfig.password
    };
    
    const result = await execCommand('pg_dump', args, { env });
    
    // Get backup file stats
    const stats = await fs.stat(backupPath);
    const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
    
    logger.info(`✅ Backup created successfully: ${backupFilename} (${sizeInMB} MB)`);
    
    // Save backup metadata
    const metadata = {
      filename: backupFilename,
      path: backupPath,
      size: stats.size,
      sizeFormatted: `${sizeInMB} MB`,
      format,
      dataOnly,
      schemaOnly,
      compressed: compress,
      created: new Date().toISOString(),
      database: dbConfig.database,
      host: dbConfig.host
    };
    
    const metadataPath = backupPath + '.meta.json';
    await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    
    return metadata;
    
  } catch (error) {
    logger.error('Backup creation failed:', error);
    throw error;
  }
}

/**
 * Restore database from backup using pg_restore
 */
async function restoreBackup(backupPath, options = {}) {
  const {
    dropExisting = false,
    dataOnly = false,
    schemaOnly = false,
    createDatabase = false
  } = options;
  
  try {
    // Check if backup file exists
    await fs.access(backupPath);
    
    const dbConfig = parseDbUrl();
    
    logger.info(`Restoring database from: ${backupPath}`);
    
    // Test connection before restore
    const connectionTest = await testConnection();
    if (!connectionTest.success) {
      throw new Error('Database connection failed');
    }
    
    const args = [
      '--host', dbConfig.host,
      '--port', dbConfig.port,
      '--username', dbConfig.username,
      '--dbname', dbConfig.database,
      '--no-password',
      '--verbose'
    ];
    
    if (dropExisting) {
      args.push('--clean');
    }
    
    if (dataOnly) {
      args.push('--data-only');
    } else if (schemaOnly) {
      args.push('--schema-only');
    }
    
    if (createDatabase) {
      args.push('--create');
    }
    
    args.push(backupPath);
    
    // Set password via environment variable
    const env = {
      ...process.env,
      PGPASSWORD: dbConfig.password
    };
    
    const result = await execCommand('pg_restore', args, { env });
    
    logger.info('✅ Database restored successfully');
    
    return { success: true, output: result.stdout };
    
  } catch (error) {
    logger.error('Database restore failed:', error);
    throw error;
  }
}

/**
 * List available backups
 */
async function listBackups() {
  try {
    await ensureBackupDir();
    
    const files = await fs.readdir(BACKUP_DIR);
    const backups = [];
    
    for (const file of files) {
      if (file.endsWith('.dump') || file.endsWith('.sql')) {
        const filePath = path.join(BACKUP_DIR, file);
        const metadataPath = filePath + '.meta.json';
        
        let metadata = null;
        try {
          const metadataContent = await fs.readFile(metadataPath, 'utf8');
          metadata = JSON.parse(metadataContent);
        } catch {
          // No metadata file, get basic info
          const stats = await fs.stat(filePath);
          metadata = {
            filename: file,
            path: filePath,
            size: stats.size,
            sizeFormatted: `${(stats.size / 1024 / 1024).toFixed(2)} MB`,
            created: stats.mtime.toISOString()
          };
        }
        
        backups.push(metadata);
      }
    }
    
    // Sort by creation date (newest first)
    backups.sort((a, b) => new Date(b.created) - new Date(a.created));
    
    return backups;
    
  } catch (error) {
    logger.error('Failed to list backups:', error);
    throw error;
  }
}

/**
 * Delete old backups (keep only specified number)
 */
async function cleanupBackups(keepCount = 10) {
  try {
    const backups = await listBackups();
    
    if (backups.length <= keepCount) {
      logger.info(`No cleanup needed. Found ${backups.length} backups, keeping ${keepCount}`);
      return { deleted: 0, kept: backups.length };
    }
    
    const toDelete = backups.slice(keepCount);
    let deleted = 0;
    
    for (const backup of toDelete) {
      try {
        await fs.unlink(backup.path);
        
        // Also delete metadata file if it exists
        const metadataPath = backup.path + '.meta.json';
        try {
          await fs.unlink(metadataPath);
        } catch {
          // Metadata file doesn't exist, ignore
        }
        
        deleted++;
        logger.info(`Deleted old backup: ${backup.filename}`);
      } catch (error) {
        logger.error(`Failed to delete backup ${backup.filename}:`, error);
      }
    }
    
    logger.info(`Cleanup completed: ${deleted} backups deleted, ${keepCount} kept`);
    return { deleted, kept: keepCount };
    
  } catch (error) {
    logger.error('Backup cleanup failed:', error);
    throw error;
  }
}

/**
 * Main function for CLI usage
 */
async function main() {
  const command = process.argv[2];
  const arg = process.argv[3];
  
  try {
    switch (command) {
      case 'create':
        const backup = await createBackup({ filename: arg });
        console.log(`Backup created: ${backup.filename}`);
        break;
        
      case 'restore':
        if (!arg) {
          console.error('Backup file path required');
          process.exit(1);
        }
        await restoreBackup(arg);
        console.log('Database restored successfully');
        break;
        
      case 'list':
        const backups = await listBackups();
        console.log('\nAvailable backups:');
        console.log('==================');
        if (backups.length === 0) {
          console.log('No backups found');
        } else {
          backups.forEach(backup => {
            console.log(`${backup.filename} - ${backup.sizeFormatted} - ${backup.created}`);
          });
        }
        break;
        
      case 'cleanup':
        const keepCount = parseInt(arg) || 10;
        const result = await cleanupBackups(keepCount);
        console.log(`Cleanup completed: ${result.deleted} deleted, ${result.kept} kept`);
        break;
        
      default:
        console.log('Usage:');
        console.log('  npm run backup create [filename]  - Create database backup');
        console.log('  npm run backup restore <file>     - Restore from backup');
        console.log('  npm run backup list               - List available backups');
        console.log('  npm run backup cleanup [count]    - Delete old backups (keep last N)');
        process.exit(1);
    }
  } catch (error) {
    console.error('Command failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { createBackup, restoreBackup, listBackups, cleanupBackups };
