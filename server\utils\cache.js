import Redis from 'ioredis';
import { logger } from './logger.js';

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || 'torvie_redis_password',
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  db: 0
};

// Create Redis client
const redis = new Redis(redisConfig);

// Handle Redis connection events
redis.on('connect', () => {
  logger.info('Redis connected');
});

redis.on('error', (error) => {
  logger.error({ 
    event: 'redis_error',
    error: error.message 
  });
});

redis.on('close', () => {
  logger.warn('Redis connection closed');
});

redis.on('reconnecting', () => {
  logger.info('Redis reconnecting...');
});

// Cache configuration
const DEFAULT_TTL = 300; // 5 minutes
const CACHE_PREFIXES = {
  TMDB: 'tmdb:',
  SEARCH: 'search:',
  USER: 'user:',
  PROFILE: 'profile:',
  WATCHLIST: 'watchlist:',
  PROGRESS: 'progress:',
  TRENDING: 'trending:',
  RECOMMENDATIONS: 'recs:'
};

// Generic cache functions
export const setCache = async (key, value, ttl = DEFAULT_TTL) => {
  try {
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
    await redis.setex(key, ttl, serializedValue);
    
    logger.debug({
      event: 'cache_set',
      key,
      ttl,
      size: serializedValue.length
    });
    
    return true;
  } catch (error) {
    logger.error({
      event: 'cache_set_failed',
      key,
      error: error.message
    });
    return false;
  }
};

export const getCache = async (key) => {
  try {
    const value = await redis.get(key);
    
    if (value === null) {
      logger.debug({
        event: 'cache_miss',
        key
      });
      return null;
    }
    
    logger.debug({
      event: 'cache_hit',
      key
    });
    
    // Try to parse as JSON, fallback to string
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  } catch (error) {
    logger.error({
      event: 'cache_get_failed',
      key,
      error: error.message
    });
    return null;
  }
};

export const deleteCache = async (key) => {
  try {
    const result = await redis.del(key);
    
    logger.debug({
      event: 'cache_delete',
      key,
      deleted: result > 0
    });
    
    return result > 0;
  } catch (error) {
    logger.error({
      event: 'cache_delete_failed',
      key,
      error: error.message
    });
    return false;
  }
};

export const clearCache = async (pattern = '*') => {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    logger.info({
      event: 'cache_clear',
      pattern,
      keysDeleted: keys.length
    });
    
    return keys.length;
  } catch (error) {
    logger.error({
      event: 'cache_clear_failed',
      pattern,
      error: error.message
    });
    return 0;
  }
};

// TMDB API caching
export const cacheTMDBData = async (endpoint, data, ttl = 1800) => { // 30 minutes
  const key = `${CACHE_PREFIXES.TMDB}${endpoint}`;
  return await setCache(key, data, ttl);
};

export const getTMDBCache = async (endpoint) => {
  const key = `${CACHE_PREFIXES.TMDB}${endpoint}`;
  return await getCache(key);
};

// Search result caching
export const cacheSearchResults = async (query, filters, results, ttl = 900) => { // 15 minutes
  const key = `${CACHE_PREFIXES.SEARCH}${query}:${JSON.stringify(filters)}`;
  return await setCache(key, results, ttl);
};

export const getSearchCache = async (query, filters) => {
  const key = `${CACHE_PREFIXES.SEARCH}${query}:${JSON.stringify(filters)}`;
  return await getCache(key);
};

// User data caching
export const cacheUserData = async (userId, data, ttl = 3600) => { // 1 hour
  const key = `${CACHE_PREFIXES.USER}${userId}`;
  return await setCache(key, data, ttl);
};

export const getUserCache = async (userId) => {
  const key = `${CACHE_PREFIXES.USER}${userId}`;
  return await getCache(key);
};

export const invalidateUserCache = async (userId) => {
  const key = `${CACHE_PREFIXES.USER}${userId}`;
  return await deleteCache(key);
};

// Profile data caching
export const cacheProfileData = async (profileId, data, ttl = 3600) => { // 1 hour
  const key = `${CACHE_PREFIXES.PROFILE}${profileId}`;
  return await setCache(key, data, ttl);
};

export const getProfileCache = async (profileId) => {
  const key = `${CACHE_PREFIXES.PROFILE}${profileId}`;
  return await getCache(key);
};

export const invalidateProfileCache = async (profileId) => {
  const key = `${CACHE_PREFIXES.PROFILE}${profileId}`;
  return await deleteCache(key);
};

// Watchlist caching
export const cacheWatchlist = async (profileId, watchlist, ttl = 1800) => { // 30 minutes
  const key = `${CACHE_PREFIXES.WATCHLIST}${profileId}`;
  return await setCache(key, watchlist, ttl);
};

export const getWatchlistCache = async (profileId) => {
  const key = `${CACHE_PREFIXES.WATCHLIST}${profileId}`;
  return await getCache(key);
};

export const invalidateWatchlistCache = async (profileId) => {
  const key = `${CACHE_PREFIXES.WATCHLIST}${profileId}`;
  return await deleteCache(key);
};

// Watch progress caching
export const cacheWatchProgress = async (profileId, mediaId, progress, ttl = 3600) => { // 1 hour
  const key = `${CACHE_PREFIXES.PROGRESS}${profileId}:${mediaId}`;
  return await setCache(key, progress, ttl);
};

export const getWatchProgressCache = async (profileId, mediaId) => {
  const key = `${CACHE_PREFIXES.PROGRESS}${profileId}:${mediaId}`;
  return await getCache(key);
};

export const invalidateWatchProgressCache = async (profileId, mediaId) => {
  const key = `${CACHE_PREFIXES.PROGRESS}${profileId}:${mediaId}`;
  return await deleteCache(key);
};

// Trending content caching
export const cacheTrendingContent = async (type, data, ttl = 3600) => { // 1 hour
  const key = `${CACHE_PREFIXES.TRENDING}${type}`;
  return await setCache(key, data, ttl);
};

export const getTrendingCache = async (type) => {
  const key = `${CACHE_PREFIXES.TRENDING}${type}`;
  return await getCache(key);
};

// Recommendations caching
export const cacheRecommendations = async (profileId, recommendations, ttl = 7200) => { // 2 hours
  const key = `${CACHE_PREFIXES.RECOMMENDATIONS}${profileId}`;
  return await setCache(key, recommendations, ttl);
};

export const getRecommendationsCache = async (profileId) => {
  const key = `${CACHE_PREFIXES.RECOMMENDATIONS}${profileId}`;
  return await getCache(key);
};

export const invalidateRecommendationsCache = async (profileId) => {
  const key = `${CACHE_PREFIXES.RECOMMENDATIONS}${profileId}`;
  return await deleteCache(key);
};

// Rate limiting with Redis
export const checkRateLimit = async (key, limit, window) => {
  try {
    const current = await redis.incr(key);
    
    if (current === 1) {
      await redis.expire(key, window);
    }
    
    return {
      allowed: current <= limit,
      remaining: Math.max(0, limit - current),
      reset: await redis.ttl(key)
    };
  } catch (error) {
    logger.error({
      event: 'rate_limit_check_failed',
      key,
      error: error.message
    });
    return { allowed: true, remaining: limit, reset: window };
  }
};

// Session storage with Redis
export const storeSession = async (sessionId, data, ttl = 86400) => { // 24 hours
  const key = `session:${sessionId}`;
  return await setCache(key, data, ttl);
};

export const getSession = async (sessionId) => {
  const key = `session:${sessionId}`;
  return await getCache(key);
};

export const deleteSession = async (sessionId) => {
  const key = `session:${sessionId}`;
  return await deleteCache(key);
};

// Cache statistics
export const getCacheStats = async () => {
  try {
    const info = await redis.info();
    const memory = await redis.memory('USAGE');
    
    return {
      connected: redis.status === 'ready',
      memory: parseInt(memory) || 0,
      info: info
    };
  } catch (error) {
    logger.error({
      event: 'cache_stats_failed',
      error: error.message
    });
    return { connected: false, memory: 0, info: null };
  }
};

// Graceful shutdown
export const closeRedis = async () => {
  try {
    await redis.quit();
    logger.info('Redis connection closed');
  } catch (error) {
    logger.error({
      event: 'redis_close_failed',
      error: error.message
    });
  }
};

export default {
  setCache,
  getCache,
  deleteCache,
  clearCache,
  cacheTMDBData,
  getTMDBCache,
  cacheSearchResults,
  getSearchCache,
  cacheUserData,
  getUserCache,
  invalidateUserCache,
  cacheProfileData,
  getProfileCache,
  invalidateProfileCache,
  cacheWatchlist,
  getWatchlistCache,
  invalidateWatchlistCache,
  cacheWatchProgress,
  getWatchProgressCache,
  invalidateWatchProgressCache,
  cacheTrendingContent,
  getTrendingCache,
  cacheRecommendations,
  getRecommendationsCache,
  invalidateRecommendationsCache,
  checkRateLimit,
  storeSession,
  getSession,
  deleteSession,
  getCacheStats,
  closeRedis
}; 