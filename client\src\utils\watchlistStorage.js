// Watchlist storage utility for standalone app behavior
// Uses backend API to store watchlist data in local files instead of browser localStorage

import { localAppStorage } from './localStorage';

export class WatchlistStorage {
  constructor() {
    console.log('📺 WatchlistStorage: Initialized for standalone app storage');
  }

  // Get watchlist for specific user and profile
  async getWatchlist(userId, profileId) {
    try {
      console.log(`📺 WatchlistStorage: Loading watchlist for user ${userId}, profile ${profileId}`);
      return await localAppStorage.getWatchlist(userId, profileId);
    } catch (error) {
      console.error('❌ WatchlistStorage: Error loading watchlist:', error);
      return [];
    }
  }

  // Save watchlist for specific user and profile
  async saveWatchlist(userId, profileId, watchlist) {
    try {
      console.log(`📺 WatchlistStorage: Saving ${watchlist.length} items for user ${userId}, profile ${profileId}`);
      return await localAppStorage.saveWatchlist(userId, profileId, watchlist);
    } catch (error) {
      console.error('❌ WatchlistStorage: Error saving watchlist:', error);
      return false;
    }
  }

  // Add item to watchlist
  async addToWatchlist(userId, profileId, item) {
    try {
      const watchlist = await this.getWatchlist(userId, profileId);
      
      // Check if item already exists
      const exists = watchlist.some(w => w.id === item.id && w.media_type === item.media_type);
      if (exists) {
        console.log('📺 WatchlistStorage: Item already in watchlist:', item.title || item.name);
        return false;
      }
      
      // Add item with timestamp
      const newItem = {
        ...item,
        added_at: new Date().toISOString()
      };
      
      watchlist.push(newItem);
      const success = await this.saveWatchlist(userId, profileId, watchlist);
      
      if (success) {
        console.log('📺 WatchlistStorage: Successfully added to watchlist:', item.title || item.name);
      }
      
      return success;
    } catch (error) {
      console.error('❌ WatchlistStorage: Error adding to watchlist:', error);
      return false;
    }
  }

  // Remove item from watchlist
  async removeFromWatchlist(userId, profileId, itemId, mediaType) {
    try {
      const watchlist = await this.getWatchlist(userId, profileId);
      const filteredWatchlist = watchlist.filter(item => 
        !(item.id === itemId && item.media_type === mediaType)
      );
      
      const success = await this.saveWatchlist(userId, profileId, filteredWatchlist);
      
      if (success) {
        console.log('📺 WatchlistStorage: Successfully removed from watchlist');
      }
      
      return success;
    } catch (error) {
      console.error('❌ WatchlistStorage: Error removing from watchlist:', error);
      return false;
    }
  }

  // Check if item is in watchlist
  async isInWatchlist(userId, profileId, itemId, mediaType) {
    try {
      const watchlist = await this.getWatchlist(userId, profileId);
      return watchlist.some(item => item.id === itemId && item.media_type === mediaType);
    } catch (error) {
      console.error('❌ WatchlistStorage: Error checking watchlist:', error);
      return false;
    }
  }

  // Get watchlist count
  async getWatchlistCount(userId, profileId) {
    try {
      const watchlist = await this.getWatchlist(userId, profileId);
      return watchlist.length;
    } catch (error) {
      console.error('❌ WatchlistStorage: Error getting watchlist count:', error);
      return 0;
    }
  }
}

// Create singleton instance
export const watchlistStorage = new WatchlistStorage();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.watchlistStorage = watchlistStorage;
  console.log('📺 Watchlist Storage initialized for standalone app!');
  console.log('🔧 Debug commands:');
  console.log('  window.watchlistStorage.getWatchlist(1, 1) - Get watchlist for user 1, profile 1');
} 