import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import MediaCard from '../components/MediaCard';
import TrailerPlayer from '../components/TrailerPlayer';
import ErrorBoundary from '../components/ErrorBoundary';
import ActorModal from '../components/ActorModal';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import { watchlistStorage } from '../utils/watchlistStorage';
import { apiFetch } from '../utils/api';

const genreIdMap = { 
  28: "Action", 12: "Adventure", 16: "Animation", 35: "Comedy", 80: "Crime", 
  99: "Documentary", 18: "Drama", 10751: "Family", 14: "Fantasy", 36: "History", 
  27: "Horror", 10402: "Music", 9648: "Mystery", 10749: "Romance", 878: "Sci-Fi", 
  10770: "TV Movie", 53: "Thriller", 10752: "War", 37: "Western" 
};

const GenrePill = ({ genreId }) => (
    <span className="text-sm bg-gray-700/50 text-gray-300 px-3 py-1 rounded-full">
        {genreIdMap[genreId] || 'Genre'}
    </span>
);

const MovieDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { profile: currentProfile } = useProfile();
  const { user } = useAuth();
  const [movie, setMovie] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [credits, setCredits] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [similar, setSimilar] = useState([]);
  const [collection, setCollection] = useState(null);
  const [videos, setVideos] = useState([]);
  const [isInWatchlist, setIsInWatchlist] = useState(false);
  const [actorModal, setActorModal] = useState({ isOpen: false, actorId: null });

  useEffect(() => {
    fetchMovieDetails();
    fetchExtraData();
  }, [id]);

  // Scroll to top when component mounts or ID changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [id]);

  // Watchlist effect: check if movie is in watchlist
  useEffect(() => {
    const checkWatchlist = async () => {
      if (movie && movie.id && user && currentProfile && currentProfile.id) {
        console.log(`🏠 MovieDetails: Checking watchlist for movie "${movie.title}"`);
        const inList = await watchlistStorage.isInWatchlist(user.id, currentProfile.id, movie.id, 'movie');
        console.log(`📋 MovieDetails: Movie "${movie.title}" ${inList ? 'IS' : 'IS NOT'} in watchlist`);
        setIsInWatchlist(inList);
      } else {
        console.log(`📋 MovieDetails: No user/profile or movie, setting watchlist to false`);
        setIsInWatchlist(false);
      }
    };
    
    checkWatchlist();
  }, [movie, user, currentProfile]);

  const toggleWatchlist = async () => {
    if (!movie || !movie.id || !user || !currentProfile || !currentProfile.id) {
      console.log('❌ MovieDetails: Cannot toggle watchlist - missing data');
      return;
    }
    
    console.log(`🏠 MovieDetails: Toggling watchlist for movie "${movie.title}"`);
    
    let success;
    if (isInWatchlist) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, movie.id, 'movie');
      if (success) {
        setIsInWatchlist(false);
        console.log(`➖ MovieDetails: Removed "${movie.title}" from watchlist`);
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, { ...movie, media_type: 'movie' });
      if (success) {
        setIsInWatchlist(true);
        console.log(`➕ MovieDetails: Added "${movie.title}" to watchlist`);
      }
    }
    
    if (!success) {
      console.error(`❌ MovieDetails: Failed to toggle watchlist for "${movie.title}"`);
    }
  };

  // Fetch collection only after movie is loaded
  useEffect(() => {
    if (movie && movie.belongs_to_collection && movie.belongs_to_collection.id) {
      fetchCollectionIfNeeded(movie.belongs_to_collection.id);
    } else {
      setCollection(null);
    }
  }, [movie]);

  const fetchMovieDetails = async () => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors
      
      const response = await apiFetch(`/movies/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Movie not found");
        } else {
          throw new Error(`Server error: ${response.status}`);
        }
      }
      
      const data = await response.json();
      
      // Validate that we have the minimum required data
      if (!data || !data.title) {
        throw new Error("Invalid movie data received");
      }
      
      setMovie(data);
    } catch (err) {
      console.error('Error fetching movie details:', err);
      setError(err.message || "Failed to load movie details");
    } finally {
      setLoading(false);
    }
  };

  const fetchExtraData = async () => {
    try {
      // Credits
      const creditsRes = await apiFetch(`/movies/${id}/credits`);
      const creditsData = await creditsRes.json();
      setCredits(creditsData);
      // Reviews
      const reviewsRes = await apiFetch(`/movies/${id}/reviews`);
      const reviewsData = await reviewsRes.json();
      setReviews(reviewsData.results || []);
      // Similar
      const similarRes = await apiFetch(`/movies/${id}/similar`);
      const similarData = await similarRes.json();
      setSimilar(similarData.results || []);
      // Videos
      const videosRes = await apiFetch(`/movies/${id}/videos`);
      const videosData = await videosRes.json();
      setVideos(videosData.results || []);
    } catch (err) {
      // Fail silently for extra data
    }
  };

  const fetchCollectionIfNeeded = async (collectionId) => {
    try {
      const res = await apiFetch(`/collection/${collectionId}`);
      const data = await res.json();
      setCollection(data);
    } catch (err) {
      setCollection(null);
    }
  };

  const handleActorClick = (actorId) => {
    setActorModal({ isOpen: true, actorId });
  };

  const closeActorModal = () => {
    setActorModal({ isOpen: false, actorId: null });
  };

  if (loading) {
    return (
      <div className="p-4 sm:p-6">
        <div className="animate-pulse space-y-6">
          <div className="w-full h-[60vh] bg-gray-900 rounded-lg"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-900 rounded w-1/3"></div>
            <div className="h-4 bg-gray-900 rounded w-2/3"></div>
            <div className="h-4 bg-gray-900 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !movie) {
    return (
      <div className="p-4 sm:p-6">
        <div className="text-center p-10 bg-gray-900/50 rounded-lg border border-red-500/30">
          <h2 className="text-2xl font-bold mb-2 text-red-400">Movie Not Found</h2>
          <p className="text-red-300 mb-4">The movie you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          <button 
            onClick={() => navigate(-1)}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const backdropUrl = movie.backdrop_path ? `https://image.tmdb.org/t/p/original${movie.backdrop_path}` : 'https://placehold.co/1920x1080?text=No+Backdrop';
  const posterUrl = movie.poster_path ? `https://image.tmdb.org/t/p/w500${movie.poster_path}` : 'https://placehold.co/300x450?text=No+Image';

  // Defensive check for required movie data
  if (!movie.title) {
    return (
      <div className="p-4 sm:p-6">
        <div className="text-center p-10 bg-gray-900/50 rounded-lg border border-red-500/30">
          <h2 className="text-2xl font-bold mb-2 text-red-400">Invalid Movie Data</h2>
          <p className="text-red-300 mb-4">This movie has incomplete or invalid data.</p>
          <button 
            onClick={() => navigate(-1)}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6">
      {/* Hero Section */}
      <div className="relative h-[60vh] rounded-lg overflow-hidden mb-8">
        <div 
          className="absolute inset-0 bg-cover bg-center" 
          style={{backgroundImage: `url(${backdropUrl})`}}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
        
        {/* Back Button */}
        <button 
          onClick={() => {
            // Use browser history to go back to previous page
            // This will work correctly whether user came from watchlist, dashboard, search, etc.
            navigate(-1);
          }}
          className="absolute top-6 left-6 z-10 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>

        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="flex flex-col md:flex-row gap-8 items-center min-h-[340px]">
            {/* Poster */}
            <div className="flex-shrink-0 overflow-visible" style={{ padding: '40px 32px', margin: '0 24px' }}>
              <MediaCard
                media={movie}
                showActions={false}
                className="w-64 h-96"
              />
            </div>
            
            {/* Movie Info */}
            <div className="flex-1">
              <h1 className="text-5xl font-black tracking-tighter mb-4">{movie.title}</h1>
              <div className="flex flex-wrap gap-2 mb-4">
                {movie.genre_ids && movie.genre_ids.length > 0 ? (
                  movie.genre_ids.map(genreId => (
                    <GenrePill key={genreId} genreId={genreId} />
                  ))
                ) : movie.genres && movie.genres.length > 0 ? (
                  movie.genres.map(genre => (
                    <span key={genre.id} className="text-sm bg-gray-700/50 text-gray-300 px-3 py-1 rounded-full">
                      {genre.name}
                    </span>
                  ))
                ) : (
                  <span className="text-sm bg-gray-700/50 text-gray-300 px-3 py-1 rounded-full">
                    Unknown Genre
                  </span>
                )}
              </div>
              <p className="text-lg text-gray-300 max-w-3xl mb-6">{movie.overview || "No overview available."}</p>
              
              {/* Action Buttons */}
              <div className="flex flex-wrap gap-4">
                <button className="bg-white text-black font-bold py-3 px-8 rounded-lg flex items-center gap-2 transform hover:scale-105 transition-transform">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"></path>
                  </svg>
                  Watch Now
                </button>
                <button
                  className={`bg-gray-800 text-white font-bold py-3 px-8 rounded-lg flex items-center gap-2 hover:bg-gray-700 transition-colors ${isInWatchlist ? 'border border-yellow-400' : ''}`}
                  onClick={toggleWatchlist}
                >
                  {isInWatchlist ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="yellow">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  )}
                  {isInWatchlist ? 'In Watchlist' : 'Add to Watchlist'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Sections */}
      {collection && collection.parts && collection.parts.length > 1 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Sequels & Series */}
          <div className="">
            <h2 className="text-2xl font-bold mb-4 text-white">Sequels & Series</h2>
            <div className="flex flex-wrap gap-4">
              {collection.parts
                .filter(part => part.id !== movie.id)
                .sort((a, b) => (a.release_date || '').localeCompare(b.release_date || ''))
                .map(part => (
                  <div key={part.id} className="cursor-pointer w-32" onClick={() => navigate(`/movie/${part.id}`)}>
                    <img
                      src={part.poster_path ? `https://image.tmdb.org/t/p/w185${part.poster_path}` : 'https://placehold.co/80x120?text=No+Image'}
                      alt={part.title}
                      className="w-full h-44 object-cover rounded-lg mb-1 bg-gray-800"
                    />
                    <span className="text-xs text-white font-bold text-center block truncate">{part.title}</span>
                  </div>
                ))}
            </div>
          </div>
          {/* Trailer/Preview */}
          <div className="flex flex-col gap-4 justify-center items-center">
            <h2 className="text-2xl font-bold mb-4 text-white">Trailer</h2>
            <div className="bg-gray-900/50 rounded-lg p-4 flex items-center justify-center w-full">
              {(() => {
                const trailer = videos.find(v => v.type === 'Trailer' && v.site === 'YouTube');
                if (trailer) {
                  return (
                    <div className="w-full aspect-video" style={{ maxWidth: '1200px', height: '600px' }}>
                      <ErrorBoundary>
                        <TrailerPlayer videoId={trailer.key} title={trailer.name} posterUrl={movie && movie.poster_path ? `https://image.tmdb.org/t/p/w500${movie.poster_path}` : undefined} />
                      </ErrorBoundary>
                    </div>
                  );
                } else {
                  return <p className="text-gray-400 text-center">No trailer available.</p>;
                }
              })()}
            </div>
          </div>
        </div>
      ) : (
        <div className="mb-8">
          {/* Trailer/Preview Full Width */}
          <h2 className="text-2xl font-bold mb-4 text-white">Trailer</h2>
          <div className="bg-gray-900/50 rounded-lg p-4 flex items-center justify-center w-full">
            {(() => {
              const trailer = videos.find(v => v.type === 'Trailer' && v.site === 'YouTube');
              if (trailer) {
                return (
                  <div className="w-full aspect-video" style={{ maxWidth: '1200px', height: '600px' }}>
                    <ErrorBoundary>
                      <TrailerPlayer videoId={trailer.key} title={trailer.name} posterUrl={movie && movie.poster_path ? `https://image.tmdb.org/t/p/w500${movie.poster_path}` : undefined} />
                    </ErrorBoundary>
                  </div>
                );
              } else {
                return <p className="text-gray-400 text-center">No trailer available.</p>;
              }
            })()}
          </div>
        </div>
      )}
      {/* Cast & Crew */}
      <div className="lg:col-span-2">
        <h2 className="text-2xl font-bold mb-4 text-white">Cast & Crew</h2>
        <div className="bg-gray-900/50 rounded-lg p-6">
          {/* Cast */}
          {credits && credits.cast && credits.cast.length > 0 ? (
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Top Cast</h3>
              <div className="flex flex-wrap gap-4 mb-4">
                {credits.cast.slice(0, 6).map(actor => (
                  <div 
                    key={actor.cast_id || actor.credit_id} 
                    className="flex flex-col items-center w-24 cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => handleActorClick(actor.id)}
                  >
                    <img
                      src={actor.profile_path ? `https://image.tmdb.org/t/p/w185${actor.profile_path}` : 'https://placehold.co/80x120?text=No+Image'}
                      alt={actor.name}
                      className="w-20 h-28 object-cover rounded-lg mb-1 bg-gray-800"
                    />
                    <span className="text-xs text-white font-bold text-center truncate w-full">{actor.name}</span>
                    <span className="text-xs text-gray-400 text-center truncate w-full">{actor.character}</span>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p className="text-gray-400 mb-2">No cast information available.</p>
          )}
          {/* Crew */}
          {credits && credits.crew && credits.crew.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Crew</h3>
              <div className="flex flex-wrap gap-4">
                {credits.crew.filter(c => c.job === 'Director').map(director => (
                  <div key={director.credit_id} className="flex flex-col items-center w-24">
                    <span className="text-xs text-white font-bold text-center">Director</span>
                    <span className="text-xs text-gray-400 text-center">{director.name}</span>
                  </div>
                ))}
                {credits.crew.filter(c => c.job === 'Writer' || c.job === 'Screenplay').map(writer => (
                  <div key={writer.credit_id} className="flex flex-col items-center w-24">
                    <span className="text-xs text-white font-bold text-center">{writer.job}</span>
                    <span className="text-xs text-gray-400 text-center">{writer.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Reviews Section */}
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4 text-white">Reviews</h2>
        <div className="bg-gray-900/50 rounded-lg p-6">
          {reviews && reviews.length > 0 ? (
            <div className="space-y-6">
              {reviews.slice(0, 3).map(review => (
                <div key={review.id} className="border-b border-gray-800 pb-4">
                  <div className="flex items-center mb-2">
                    <span className="text-sm font-bold text-white mr-2">{review.author}</span>
                    {review.author_details && review.author_details.rating && (
                      <span className="text-yellow-400 text-xs ml-2">★ {review.author_details.rating}</span>
                    )}
                  </div>
                  <p className="text-gray-300 text-sm whitespace-pre-line">{review.content}</p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400">User reviews and ratings will be displayed here.</p>
          )}
        </div>
      </div>

      {/* Actor Modal */}
      <ActorModal
        isOpen={actorModal.isOpen}
        onClose={closeActorModal}
        actorId={actorModal.actorId}
      />
    </div>
  );
};

export default MovieDetails; 