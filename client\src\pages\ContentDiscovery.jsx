import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import OptimizedMediaCard from '../components/OptimizedMediaCard';
import { apiFetch } from '../utils/api';
import recommendationEngine from '../utils/recommendationEngine';
import TrailerModal from '../components/TrailerModal';
import { watchlistStorage } from '../utils/watchlistStorage';

const ContentDiscoveryPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const [activeSection, setActiveSection] = useState('discovery');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [trendingContent, setTrendingContent] = useState([]);
  const [newReleases, setNewReleases] = useState([]);
  const [hiddenGems, setHiddenGems] = useState([]);
  const [personalizedRecs, setPersonalizedRecs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, trailer: null, content: null });
  const [watchlist, setWatchlist] = useState([]);

  // Navigation sections
  const sections = [
    { id: 'discovery', name: '🎯 Smart Discovery', icon: '🎯' },
    { id: 'trending', name: '🔥 Trending Now', icon: '🔥' },
    { id: 'new', name: '🆕 New Releases', icon: '🆕' },
    { id: 'gems', name: '💎 Hidden Gems', icon: '💎' },
    { id: 'search', name: '🔍 Advanced Search', icon: '🔍' }
  ];

  // Load initial data
  const loadInitialData = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Load different content types
      const [moviesResponse, tvResponse] = await Promise.all([
        apiFetch('/api/trending/movies?page=1'),
        apiFetch('/api/trending/tv?page=1')
      ]);

      const movies = moviesResponse.ok ? (await moviesResponse.json()).results : [];
      const tvShows = tvResponse.ok ? (await tvResponse.json()).results : [];
      
      // Combine and add media type
      const allContent = [
        ...movies.map(item => ({ ...item, media_type: 'movie' })),
        ...tvShows.map(item => ({ ...item, media_type: 'tv' }))
      ];

      // Load user data
      const watchHistory = JSON.parse(localStorage.getItem('watchProgress') || '{}');
      const userRatings = JSON.parse(localStorage.getItem('userRatings') || '[]');

      // Generate different recommendation types
      const history = Object.entries(watchHistory).map(([id, data]) => ({
        id: parseInt(id),
        ...data,
        media_type: data.media_type || 'movie'
      }));

      const ratings = userRatings.map(rating => ({
        itemId: rating.itemId,
        rating: rating.rating,
        ratedAt: rating.ratedAt
      }));

      // Set different content categories
      setTrendingContent(recommendationEngine.getTrendingContent(allContent, 20));
      setNewReleases(recommendationEngine.getNewReleases(allContent, 20));
      setHiddenGems(recommendationEngine.getHiddenGems(allContent, 20));

      // Generate personalized recommendations
      const personalized = await recommendationEngine.getPersonalizedRecommendations(
        user?.id || 'anonymous',
        profile?.id || 'default',
        allContent,
        {
          limit: 20,
          userWatchHistory: history,
          userRatings: ratings,
          userWatchlist: watchlist
        }
      );

      setPersonalizedRecs(personalized.map(rec => rec.item));

    } catch (error) {
      console.error('Error loading discovery data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user, profile, watchlist]);

  // Load watchlist
  useEffect(() => {
    const loadWatchlist = async () => {
      if (!user || !profile || !profile.id) return;
      
      try {
        const stored = await watchlistStorage.getWatchlist(user.id, profile.id);
        setWatchlist(stored);
      } catch (error) {
        console.error('Error loading watchlist:', error);
      }
    };
    
    loadWatchlist();
  }, [user, profile]);

  // Load initial data
  useEffect(() => {
    if (!user || !profile || !profile.id || watchlist === undefined) return;
    loadInitialData();
  }, [loadInitialData, user, profile, watchlist]);

  // Handle search
  const handleSearch = useCallback(async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    try {
      const [moviesResponse, tvResponse] = await Promise.all([
        apiFetch(`/api/search/movies?query=${encodeURIComponent(query)}&page=1`),
        apiFetch(`/api/search/tv?query=${encodeURIComponent(query)}&page=1`)
      ]);

      const movies = moviesResponse.ok ? (await moviesResponse.json()).results : [];
      const tvShows = tvResponse.ok ? (await tvResponse.json()).results : [];

      const results = [
        ...movies.map(item => ({ ...item, media_type: 'movie' })),
        ...tvShows.map(item => ({ ...item, media_type: 'tv' }))
      ];

      setSearchResults(results);
    } catch (error) {
      console.error('Error searching:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Handle item click
  const handleItemClick = useCallback((item) => {
    const route = item.media_type === 'movie' ? 'movie' : 'show';
    navigate(`/${route}/${item.id}`);
  }, [navigate]);

  // Handle play button
  const handlePlay = useCallback((item) => {
    const route = item.media_type === 'movie' ? 'movie' : 'show';
    navigate(`/${route}/${item.id}`);
  }, [navigate]);

  // Handle trailer button
  const handleTrailer = async (media) => {
    if (!media) return;
    try {
      const endpoint = media.media_type === 'movie'
        ? `/api/movies/${media.id}/videos`
        : `/api/tv/${media.id}/videos`;
      const response = await apiFetch(endpoint);
      const data = await response.json();
      const trailers = data.results?.filter(video =>
        video.site === 'YouTube' &&
        (video.type === 'Trailer' || video.type === 'Teaser')
      ) || [];
      if (trailers.length > 0) {
        setTrailerModal({ isOpen: true, trailer: trailers[0], content: media });
      } else {
        alert('No trailer available for this title.');
      }
    } catch (error) {
      alert('Failed to load trailer.');
    }
  };

  const closeTrailerModal = () => setTrailerModal({ isOpen: false, trailer: null, content: null });

  // Watchlist functions
  const isInWatchlist = (id) => watchlist.some(item => String(item.id) === String(id));

  const toggleWatchlist = async (item) => {
    if (!user || !profile || !profile.id) return;
    
    const exists = watchlist.some(watchlistItem => String(watchlistItem.id) === String(item.id));
    let success;
    
    if (exists) {
      success = await watchlistStorage.removeFromWatchlist(user.id, profile.id, item.id, item.media_type);
      if (success) {
        setWatchlist(prev => prev.filter(watchlistItem => String(watchlistItem.id) !== String(item.id)));
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, profile.id, { ...item, media_type: item.media_type });
      if (success) {
        setWatchlist(prev => [...prev, item]);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen px-6 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading discovery features...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen px-6 py-8">
      <div className="max-w-7xl mx-auto">
        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-3 justify-center">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === section.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <span className="mr-2">{section.icon}</span>
                {section.name}
              </button>
            ))}
          </div>
        </div>

        {/* Smart Discovery Section */}
        {activeSection === 'discovery' && (
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6">🎯 Smart Discovery</h2>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
              {personalizedRecs.map((item) => (
                <OptimizedMediaCard
                  key={item.id}
                  item={item}
                  isInWatchlist={isInWatchlist(item.id)}
                  toggleWatchlist={() => toggleWatchlist(item)}
                  onClick={() => handleItemClick(item)}
                  onPlay={() => handlePlay(item)}
                  onTrailer={() => handleTrailer(item)}
                  className="media-card"
                />
              ))}
            </div>
          </div>
        )}

        {/* Trending Now Section */}
        {activeSection === 'trending' && (
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6">🔥 Trending Now</h2>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
              {trendingContent.map((item) => (
                <OptimizedMediaCard
                  key={item.id}
                  item={item}
                  isInWatchlist={isInWatchlist(item.id)}
                  toggleWatchlist={() => toggleWatchlist(item)}
                  onClick={() => handleItemClick(item)}
                  onPlay={() => handlePlay(item)}
                  onTrailer={() => handleTrailer(item)}
                  className="media-card"
                />
              ))}
            </div>
          </div>
        )}

        {/* New Releases Section */}
        {activeSection === 'new' && (
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6">🆕 New Releases</h2>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
              {newReleases.map((item) => (
                <OptimizedMediaCard
                  key={item.id}
                  item={item}
                  isInWatchlist={isInWatchlist(item.id)}
                  toggleWatchlist={() => toggleWatchlist(item)}
                  onClick={() => handleItemClick(item)}
                  onPlay={() => handlePlay(item)}
                  onTrailer={() => handleTrailer(item)}
                  className="media-card"
                />
              ))}
            </div>
          </div>
        )}

        {/* Hidden Gems Section */}
        {activeSection === 'gems' && (
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6">💎 Hidden Gems</h2>
            <p className="text-gray-400 mb-6">Highly rated content that deserves more attention</p>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
              {hiddenGems.map((item) => (
                <OptimizedMediaCard
                  key={item.id}
                  item={item}
                  isInWatchlist={isInWatchlist(item.id)}
                  toggleWatchlist={() => toggleWatchlist(item)}
                  onClick={() => handleItemClick(item)}
                  onPlay={() => handlePlay(item)}
                  onTrailer={() => handleTrailer(item)}
                  className="media-card"
                />
              ))}
            </div>
          </div>
        )}

        {/* Advanced Search Section */}
        {activeSection === 'search' && (
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6">🔍 Advanced Search</h2>
            
            {/* Search Input */}
            <div className="mb-8">
              <div className="relative max-w-2xl mx-auto">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                  placeholder="Search for movies, TV shows, actors, genres..."
                  className="w-full bg-gray-800 text-white px-4 py-3 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none pr-12"
                />
                <button
                  onClick={() => handleSearch(searchQuery)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Search Results */}
            {!isSearching && searchQuery && searchResults.length > 0 && (
              <div className="mb-10">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">Search Results for &quot;{searchQuery}&quot;</h2>
                  <span className="text-gray-400">{searchResults.length} results</span>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
                  {searchResults.map((item) => (
                    <OptimizedMediaCard
                      key={item.id}
                      item={item}
                      isInWatchlist={isInWatchlist(item.id)}
                      toggleWatchlist={() => toggleWatchlist(item)}
                      onClick={() => handleItemClick(item)}
                      onPlay={() => handlePlay(item)}
                      onTrailer={() => handleTrailer(item)}
                      className="media-card"
                    />
                  ))}
                </div>
              </div>
            )}

            {!isSearching && searchQuery && searchResults.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-400 text-lg mb-2">No results found for &quot;{searchQuery}&quot;</p>
                <p className="text-gray-500">Try different keywords or check spelling</p>
              </div>
            )}

            {/* Personalized Recommendations when no search */}
            {!searchQuery && (
              <div className="mb-10 mt-12">
                <h3 className="text-xl font-semibold mb-4">💡 Recommended for You</h3>
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
                  {personalizedRecs.map((item) => (
                    <OptimizedMediaCard
                      key={item.id}
                      item={item}
                      isInWatchlist={isInWatchlist(item.id)}
                      toggleWatchlist={() => toggleWatchlist(item)}
                      onClick={() => handleItemClick(item)}
                      onPlay={() => handlePlay(item)}
                      onTrailer={() => handleTrailer(item)}
                      className="media-card"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      <TrailerModal 
        isOpen={trailerModal.isOpen} 
        onClose={closeTrailerModal} 
        trailer={trailerModal.trailer} 
        content={trailerModal.content} 
      />
    </div>
  );
};

export default ContentDiscoveryPage; 