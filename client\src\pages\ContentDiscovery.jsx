import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import ContentDiscovery from '../components/ContentDiscovery';
import ContentInsights from '../components/ContentInsights';
import SmartSearchSuggestions from '../components/SmartSearchSuggestions';
import OptimizedMediaCard from '../components/OptimizedMediaCard';
import VirtualizedMediaList from '../components/VirtualizedMediaList';
import { apiFetch } from '../utils/api';
import recommendationEngine from '../utils/recommendationEngine';
import TrailerModal from '../components/TrailerModal';

const ContentDiscoveryPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const [activeSection, setActiveSection] = useState('discovery');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [trendingContent, setTrendingContent] = useState([]);
  const [newReleases, setNewReleases] = useState([]);
  const [hiddenGems, setHiddenGems] = useState([]);
  const [personalizedRecs, setPersonalizedRecs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, trailer: null, content: null });

  // Navigation sections
  const sections = [
    { id: 'discovery', name: '🎯 Smart Discovery', icon: '🎯' },
    { id: 'trending', name: '🔥 Trending Now', icon: '🔥' },
    { id: 'new', name: '🆕 New Releases', icon: '🆕' },
    { id: 'gems', name: '💎 Hidden Gems', icon: '💎' },
    { id: 'insights', name: '📊 Your Insights', icon: '📊' },
    { id: 'search', name: '🔍 Advanced Search', icon: '🔍' }
  ];

  // Load initial data
  const loadInitialData = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Load different content types
      const [moviesResponse, tvResponse] = await Promise.all([
        apiFetch('/api/trending/movies?page=1'),
        apiFetch('/api/trending/tv?page=1')
      ]);

      const movies = moviesResponse.ok ? (await moviesResponse.json()).results : [];
      const tvShows = tvResponse.ok ? (await tvResponse.json()).results : [];
      
      // Combine and add media type
      const allContent = [
        ...movies.map(item => ({ ...item, media_type: 'movie' })),
        ...tvShows.map(item => ({ ...item, media_type: 'tv' }))
      ];

      // Load user data
      const watchHistory = JSON.parse(localStorage.getItem('watchProgress') || '{}');
      const watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
      const userRatings = JSON.parse(localStorage.getItem('userRatings') || '[]');

      // Generate different recommendation types
      const history = Object.entries(watchHistory).map(([id, data]) => ({
        id: parseInt(id),
        ...data,
        media_type: data.media_type || 'movie'
      }));

      const ratings = userRatings.map(rating => ({
        itemId: rating.itemId,
        rating: rating.rating,
        ratedAt: rating.ratedAt
      }));

      // Set different content categories
      setTrendingContent(recommendationEngine.getTrendingContent(allContent, 20));
      setNewReleases(recommendationEngine.getNewReleases(allContent, 20));
      setHiddenGems(recommendationEngine.getHiddenGems(allContent, 20));

      // Generate personalized recommendations
      const personalized = await recommendationEngine.getPersonalizedRecommendations(
        user?.id || 'anonymous',
        profile?.id || 'default',
        allContent,
        {
          limit: 20,
          userWatchHistory: history,
          userRatings: ratings
        }
      );

      setPersonalizedRecs(personalized.map(rec => rec.item));

    } catch (error) {
      console.error('Error loading discovery data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user, profile]);

  // Load data on mount
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  // Handle search
  const handleSearch = useCallback(async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    try {
      const [moviesResponse, tvResponse] = await Promise.all([
        apiFetch(`/api/search/movies?query=${encodeURIComponent(query)}&page=1`),
        apiFetch(`/api/search/tv?query=${encodeURIComponent(query)}&page=1`)
      ]);

      const movies = moviesResponse.ok ? (await moviesResponse.json()).results : [];
      const tvShows = tvResponse.ok ? (await tvResponse.json()).results : [];

      const results = [
        ...movies.map(item => ({ ...item, media_type: 'movie' })),
        ...tvShows.map(item => ({ ...item, media_type: 'tv' }))
      ];

      setSearchResults(results);
    } catch (error) {
      console.error('Error searching:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Handle item click
  const handleItemClick = useCallback((item) => {
    const route = item.media_type === 'movie' ? 'movie' : 'show';
    navigate(`/${route}/${item.id}`);
  }, [navigate]);

  // Handle play button
  const handlePlay = useCallback((item) => {
    // For now, navigate to details page where user can play
    const route = item.media_type === 'movie' ? 'movie' : 'show';
    navigate(`/${route}/${item.id}`);
  }, [navigate]);

  // Handle trailer button
  const handleTrailer = async (media) => {
    if (!media) return;
    try {
      const endpoint = media.media_type === 'movie'
        ? `/api/movies/${media.id}/videos`
        : `/api/tv/${media.id}/videos`;
      const response = await apiFetch(endpoint);
      const data = await response.json();
      const trailers = data.results?.filter(video =>
        video.site === 'YouTube' &&
        (video.type === 'Trailer' || video.type === 'Teaser')
      ) || [];
      if (trailers.length > 0) {
        setTrailerModal({ isOpen: true, trailer: trailers[0], content: media });
      } else {
        alert('No trailer available for this title.');
      }
    } catch (error) {
      alert('Failed to load trailer.');
    }
  };
  const closeTrailerModal = () => setTrailerModal({ isOpen: false, trailer: null, content: null });

  // Handle search suggestion select
  const handleSuggestionSelect = useCallback((suggestion) => {
    if (suggestion.id) {
      handleItemClick(suggestion);
    } else {
      setSearchQuery(suggestion.title || suggestion);
      handleSearch(suggestion.title || suggestion);
    }
  }, [handleItemClick, handleSearch]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading discovery features...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 py-8">
        <div className="max-w-7xl mx-auto px-4">
          <h1 className="text-4xl font-bold text-center mb-2">Content Discovery</h1>
          <p className="text-center text-blue-200">Discover amazing content tailored just for you</p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-900 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-wrap gap-2 py-4">
            {sections.map(section => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === section.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <span className="mr-2">{section.icon}</span>
                {section.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Smart Discovery Section */}
        {activeSection === 'discovery' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">🎯 Smart Discovery</h2>
              <ContentDiscovery
                showTitle={false}
                maxItems={24}
                onItemClick={handleItemClick}
                onPlay={handlePlay}
                onTrailer={handleTrailer}
              />
            </div>
          </div>
        )}

        {/* Trending Now Section */}
        {activeSection === 'trending' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">🔥 Trending Now</h2>
              <VirtualizedMediaList
                items={trendingContent}
                onItemClick={handleItemClick}
                onPlay={handlePlay}
                onTrailer={handleTrailer}
                showPlayButton={true}
                showTrailerButton={true}
                showWatchlistButton={true}
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
              />
            </div>
          </div>
        )}

        {/* New Releases Section */}
        {activeSection === 'new' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">🆕 New Releases</h2>
              <VirtualizedMediaList
                items={newReleases}
                onItemClick={handleItemClick}
                onPlay={handlePlay}
                onTrailer={handleTrailer}
                showPlayButton={true}
                showTrailerButton={true}
                showWatchlistButton={true}
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
              />
            </div>
          </div>
        )}

        {/* Hidden Gems Section */}
        {activeSection === 'gems' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">💎 Hidden Gems</h2>
              <p className="text-gray-400 mb-6">
                Highly rated content that deserves more attention
              </p>
              <VirtualizedMediaList
                items={hiddenGems}
                onItemClick={handleItemClick}
                onPlay={handlePlay}
                onTrailer={handleTrailer}
                showPlayButton={true}
                showTrailerButton={true}
                showWatchlistButton={true}
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
              />
            </div>
          </div>
        )}

        {/* Insights Section */}
        {activeSection === 'insights' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">📊 Your Insights</h2>
              <ContentInsights
                showTitle={false}
                showCharts={true}
              />
            </div>
          </div>
        )}

        {/* Advanced Search Section */}
        {activeSection === 'search' && (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">🔍 Advanced Search</h2>
              
              {/* Search Input */}
              <div className="mb-8">
                <div className="relative max-w-2xl mx-auto">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search for movies, TV shows, actors, genres..."
                    className="w-full bg-gray-800 text-white px-4 py-3 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none pr-12"
                  />
                  <button
                    onClick={() => handleSearch(searchQuery)}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
                
                {/* Smart Search Suggestions */}
                <div className="mt-4">
                  <SmartSearchSuggestions
                    query={searchQuery}
                    onSuggestionSelect={handleSuggestionSelect}
                    maxSuggestions={8}
                    showTrending={true}
                    showRecent={true}
                    showPersonalized={true}
                  />
                </div>
              </div>

              {/* Search Results */}
              {!isSearching && searchQuery && searchResults.length > 0 && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-2xl font-bold">
                      Search Results for &quot;{searchQuery}&quot;
                    </h2>
                    <span className="text-gray-400">
                      {searchResults.length} results
                    </span>
                  </div>
                  <VirtualizedMediaList
                    items={searchResults}
                    onItemClick={handleItemClick}
                    onPlay={handlePlay}
                    onTrailer={handleTrailer}
                    showPlayButton={true}
                    showTrailerButton={true}
                    showWatchlistButton={true}
                    className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
                  />
                </div>
              )}

              {!isSearching && searchQuery && searchResults.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-400 text-lg mb-2">No results found for &quot;{searchQuery}&quot;</p>
                  <p className="text-gray-500">Try different keywords or check spelling</p>
                </div>
              )}

              {/* Personalized Recommendations */}
              {!searchQuery && (
                <div className="mt-12">
                  <h3 className="text-xl font-semibold mb-4">💡 Recommended for You</h3>
                  <VirtualizedMediaList
                    items={personalizedRecs}
                    onItemClick={handleItemClick}
                    showPlayButton={true}
                    showTrailerButton={true}
                    showWatchlistButton={true}
                    className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      <TrailerModal isOpen={trailerModal.isOpen} onClose={closeTrailerModal} trailer={trailerModal.trailer} content={trailerModal.content} />
    </div>
  );
};

export default ContentDiscoveryPage; 