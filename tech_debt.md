# Torvie Technical Debt & Improvement Areas

## Overview
This document tracks known technical debt, architectural limitations, and areas for improvement. Items are categorized by severity and affected area. Addressing these will improve maintainability, scalability, and robustness.

---

## 1. Backend

### 1.1. JSON File Storage (High Priority)
- **Issue:** User profiles, watchlists, and progress are stored in flat JSON files.
- **Impact:** Not scalable, risk of data corruption, no concurrency control.
- **Recommendation:** Migrate to PostgreSQL with proper schema and ORM (e.g., Prisma, Sequelize).

### 1.2. Limited Error Handling (Medium)
- **Issue:** Some API routes lack comprehensive error handling and logging.
- **Impact:** Harder to debug, risk of silent failures.
- **Recommendation:** Standardize error handling middleware and add structured logging.

### 1.3. No Rate Limiting/Security Hardening (High)
- **Issue:** APIs lack rate limiting and advanced security headers.
- **Impact:** Vulnerable to abuse and attacks.
- **Recommendation:** Add express-rate-limit, helmet, and input validation.

---

## 2. Frontend

### 2.1. Incomplete Test Coverage (High)
- **Issue:** Minimal unit/integration tests for components and pages.
- **Impact:** Risk of regressions, harder refactoring.
- **Recommendation:** Add Jest/React Testing Library tests for critical flows.

### 2.2. UI/UX Consistency (Medium)
- **Issue:** Some pages/components lack consistent styling or responsive design.
- **Impact:** Inconsistent user experience.
- **Recommendation:** Audit UI, standardize with Tailwind, improve accessibility.

### 2.3. State Management Complexity (Low)
- **Issue:** Contexts and prop drilling in some areas.
- **Impact:** Harder to scale as app grows.
- **Recommendation:** Consider Redux or Zustand if state grows more complex.

---

## 3. DevOps & Tooling

### 3.1. No Dockerization (High)
- **Issue:** App is not containerized; onboarding and deployment are manual.
- **Impact:** Harder to ensure environment parity.
- **Recommendation:** Add Dockerfile and docker-compose.yml for full stack.

### 3.2. No CI/CD Pipeline (High)
- **Issue:** No automated testing/build pipeline.
- **Impact:** Manual deploys, risk of broken code in main.
- **Recommendation:** Add GitHub Actions workflow for lint, test, build.

---

## 4. Observability

### 4.1. No Centralized Logging/Monitoring (Medium)
- **Issue:** No structured logs or metrics.
- **Impact:** Harder to debug/monitor in production.
- **Recommendation:** Integrate logging (winston/pino), add Prometheus metrics, OpenTelemetry tracing.

---

## 5. Documentation

### 5.1. Incomplete Docs (Medium)
- **Issue:** Some features and flows are not documented.
- **Impact:** Harder for new devs to onboard.
- **Recommendation:** Keep `architecture.md`, `tech_debt.md`, and `README.md` up to date.

---

## 6. Future Considerations
- **Database migration** (PostgreSQL)
- **Containerization** (Docker)
- **CI/CD** (GitHub Actions)
- **Testing** (unit, integration, E2E)
- **Observability** (logging, metrics, tracing)
- **Security** (rate limiting, input validation)
- **UI/UX** (consistency, accessibility) 