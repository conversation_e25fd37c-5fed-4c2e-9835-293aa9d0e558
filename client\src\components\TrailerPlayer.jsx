import React, { useEffect, useRef, useState } from 'react';

const TrailerPlayer = ({ videoId, title, _posterUrl, startMuted = true }) => {
  const playerRef = useRef(null);
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(startMuted);
  // eslint-disable-next-line no-unused-vars
  const [newPlayer, setNewPlayer] = useState(null);

  useEffect(() => {
    // Load YouTube IFrame Player API
    if (!window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    }

    // Initialize player when API is ready
    const initializePlayer = () => {
      if (window.YT && window.YT.Player) {
        const newPlayer = new window.YT.Player(playerRef.current, {
          height: '100%',
          width: '100%',
          videoId: videoId,
          playerVars: {
            autoplay: 1,
            mute: startMuted ? 1 : 0,
            modestbranding: 1,
            rel: 0,
            showinfo: 0,
            controls: 0,
            disablekb: 0,
            fs: 0,
            iv_load_policy: 3,
            cc_load_policy: 0,
            vq: 'hd1080',
            origin: window.location.origin,
            enablejsapi: 1,
            playsinline: 1,
          },
          events: {
            onReady: (event) => {
              setIsLoading(false);
              setNewPlayer(event.target);
              if (startMuted) {
                event.target.mute();
              } else {
                event.target.unMute();
              }
              event.target.playVideo();
              setIsPlaying(true);
              setIsMuted(startMuted);
              const player = event.target;
              const qualities = player.getAvailableQualityLevels();
              if (qualities.includes('hd1080')) {
                player.setPlaybackQuality('hd1080');
              } else if (qualities.includes('hd720')) {
                player.setPlaybackQuality('hd720');
              }
            },
            onStateChange: (_event) => {
              if (_event.data === window.YT.PlayerState.PLAYING) {
                setIsLoading(false);
                setIsPlaying(true);
              } else if (_event.data === window.YT.PlayerState.PAUSED) {
                setIsPlaying(false);
              } else if (_event.data === window.YT.PlayerState.ENDED) {
                setIsPlaying(false);
              }
            },
            onError: (event) => {
              setError('Failed to load video');
              setIsLoading(false);
            }
          }
        });
      }
    };

    // Wait for API to load
    if (window.YT && window.YT.Player) {
      initializePlayer();
    } else {
      window.onYouTubeIframeAPIReady = initializePlayer;
    }

    return () => {
      // Cleanup
      if (window.onYouTubeIframeAPIReady) {
        delete window.onYouTubeIframeAPIReady;
      }
    };
  }, [videoId, startMuted]);

  const handleUnmute = () => {
    if (newPlayer) {
      newPlayer.unMute();
      setIsMuted(false);
    }
  };

  return (
    <div className="w-full h-full">
      <div 
        ref={containerRef}
        className="relative w-full h-full rounded-lg overflow-hidden bg-black"
      >
        {/* Equal height black bars to hide YouTube branding and center video */}
        <div className="absolute top-0 left-0 w-full" style={{height: '12%', background: 'black', zIndex: 20, pointerEvents: 'none'}} />
        <div className="absolute bottom-0 left-0 w-full" style={{height: '12%', background: 'black', zIndex: 20, pointerEvents: 'none'}} />

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <p>Loading trailer...</p>
            </div>
          </div>
        )}
        
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10">
            <div className="text-white text-center">
              <p className="text-red-400 mb-2">{error}</p>
              <p className="text-sm">Please try again later</p>
            </div>
          </div>
        )}

        <div 
          ref={playerRef}
          className="w-full h-full"
          title={title}
          style={{ pointerEvents: 'none' }}
        />

        {/* Invisible overlay to block all YouTube interactions */}
        <div 
          className="absolute inset-0 z-25"
          style={{ pointerEvents: 'none' }}
        />

        {/* Unmute button overlay */}
        {isMuted && !isLoading && !error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center z-30">
            <button
              className="bg-white bg-opacity-90 hover:bg-opacity-100 text-black rounded-full p-6 shadow-2xl transition-all duration-200 flex items-center justify-center transform hover:scale-110 active:scale-95"
              title="Unmute Trailer"
              onClick={handleUnmute}
            >
              <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 7v6h4l5 5V2l-5 5H9z" />
              </svg>
              <span className="ml-2 font-bold">Unmute</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrailerPlayer; 