# Torvie Database Architecture

## Overview

Torvie now uses a robust PostgreSQL database architecture designed for production deployment. The system includes comprehensive migration management, automated backups, health monitoring, and seamless data migration from the previous JSON file storage.

## 🏗️ Architecture Features

### ✅ Production-Ready Components
- **PostgreSQL Database** with optimized schema and indexes
- **Migration System** with versioning and rollback capabilities
- **Connection Pooling** with retry logic and health monitoring
- **Automated Backups** with compression and metadata tracking
- **Health Monitoring** with detailed diagnostics and metrics
- **Data Migration** from existing JSON files to PostgreSQL
- **Environment-Specific Configuration** for development, testing, and production

### 🔧 Key Improvements
- **Scalability**: Handles thousands of concurrent users
- **Data Integrity**: ACID transactions and foreign key constraints
- **Performance**: Optimized queries with proper indexing
- **Reliability**: Connection pooling with automatic retry logic
- **Monitoring**: Comprehensive health checks and performance metrics
- **Backup & Recovery**: Automated backup system with restore capabilities

## 📊 Database Schema

### Core Tables
- **users** - User accounts with authentication
- **profiles** - User profiles (Netflix-style multi-profile support)
- **watchlist** - User watchlists per profile
- **watch_progress** - Video playback progress tracking
- **viewing_history** - Complete viewing history with ratings
- **search_history** - Search query tracking
- **user_preferences** - Customizable user settings
- **content_ratings** - User ratings and reviews

### Analytics Tables
- **user_sessions** - Session tracking and analytics
- **content_interactions** - Detailed user interaction tracking
- **performance_metrics** - Application performance monitoring
- **error_logs** - Centralized error logging
- **content_popularity** - Content popularity and trending metrics
- **system_health** - System health monitoring

### Migration Management
- **schema_migrations** - Migration version tracking with checksums

## 🚀 Quick Setup

### Prerequisites
```bash
# Ensure PostgreSQL is installed and running
# For Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib

# For macOS:
brew install postgresql

# For Windows:
# Download from https://www.postgresql.org/download/windows/
```

### Environment Configuration
```bash
# Copy environment template
cp env.example .env

# Edit .env file with your database configuration
DATABASE_URL=postgresql://username:password@localhost:5432/torvie
JWT_SECRET=your-super-secret-jwt-key
TMDB_API_KEY=your-tmdb-api-key
```

### Database Setup
```bash
# Complete database setup (recommended)
cd server
npm run db:setup

# Or step-by-step:
npm run migrate              # Run migrations
npm run migrate-data         # Migrate JSON data
npm run backup:create        # Create initial backup
```

## 📋 Available Commands

### Migration Commands
```bash
npm run migrate              # Run pending migrations
npm run migrate:status       # Show migration status
npm run migrate:create <name> # Create new migration
npm run migrate-data         # Migrate data from JSON files
```

### Backup Commands
```bash
npm run backup:create        # Create database backup
npm run backup:list          # List available backups
npm run backup:restore <file> # Restore from backup
npm run backup:cleanup       # Clean up old backups
```

### Database Management
```bash
npm run db:setup             # Complete database setup
npm run db:health            # Check database health
npm run db:reset -- --confirm # Reset database (DANGEROUS!)
```

### Health Monitoring
```bash
# API endpoints for monitoring
curl http://localhost:3000/api/health
curl http://localhost:3000/api/health/database
curl http://localhost:3000/api/health/database/performance
curl http://localhost:3000/api/health/system
```

## 🔧 Configuration Options

### Environment Variables
```bash
# Database Configuration
DATABASE_URL=postgresql://user:pass@host:port/database
DB_POOL_MAX=30              # Maximum connections in pool
DB_POOL_MIN=5               # Minimum connections in pool
DB_IDLE_TIMEOUT=30000       # Idle connection timeout (ms)
DB_CONNECTION_TIMEOUT=5000  # Connection timeout (ms)
DB_QUERY_TIMEOUT=30000      # Query timeout (ms)
DB_MAX_RETRIES=5            # Maximum connection retries

# Migration Configuration
AUTO_MIGRATE=true           # Auto-run migrations on startup
DEBUG_ROUTES=false          # Enable debug endpoints

# Backup Configuration
BACKUP_RETENTION=10         # Number of backups to keep
```

### Connection Pool Configuration
The database uses an intelligent connection pool that:
- Automatically adjusts pool size based on environment
- Implements exponential backoff for failed connections
- Monitors connection health and performance
- Provides detailed logging and metrics

## 📈 Performance Features

### Optimized Queries
- **Indexed Columns**: All frequently queried columns have proper indexes
- **Composite Indexes**: Multi-column indexes for complex queries
- **Partial Indexes**: Conditional indexes for filtered queries
- **Generated Columns**: Computed columns for derived values

### Connection Management
- **Pool Monitoring**: Real-time connection pool statistics
- **Query Monitoring**: Slow query detection and logging
- **Retry Logic**: Automatic retry for transient failures
- **Health Checks**: Continuous database health monitoring

### Caching Strategy
- **Query Result Caching**: In-memory caching for frequently accessed data
- **Connection Pooling**: Reuse database connections efficiently
- **Prepared Statements**: Optimized query execution

## 🛡️ Security Features

### Data Protection
- **Encrypted Passwords**: bcrypt hashing for user passwords
- **SQL Injection Prevention**: Parameterized queries throughout
- **Input Validation**: Comprehensive input sanitization
- **Access Control**: Role-based access to database operations

### Monitoring & Auditing
- **Error Logging**: Centralized error tracking with context
- **Performance Monitoring**: Query performance and resource usage
- **Security Logging**: Authentication and authorization events
- **Health Monitoring**: Continuous system health checks

## 🔄 Migration System

### Features
- **Version Control**: Sequential migration versioning
- **Integrity Checks**: Checksum verification for applied migrations
- **Rollback Support**: Safe rollback capabilities (manual)
- **Environment Support**: Different configurations per environment

### Creating Migrations
```bash
# Create a new migration
npm run migrate:create "add_user_preferences_table"

# This creates: server/database/migrations/YYYYMMDD_add_user_preferences_table.sql
```

### Migration Best Practices
1. **Always backup** before running migrations in production
2. **Test migrations** in development environment first
3. **Use transactions** for complex migrations
4. **Add proper indexes** for new columns
5. **Consider performance** impact of large table changes

## 💾 Backup & Recovery

### Automated Backups
- **Scheduled Backups**: Can be configured with cron jobs
- **Compression**: Automatic compression for space efficiency
- **Metadata Tracking**: Detailed backup information and checksums
- **Retention Policy**: Automatic cleanup of old backups

### Backup Types
```bash
# Full backup (default)
npm run backup:create

# Schema only
npm run backup:create -- --schema-only

# Data only
npm run backup:create -- --data-only

# Custom filename
npm run backup:create my-backup-name.dump
```

### Recovery Process
```bash
# List available backups
npm run backup:list

# Restore from backup
npm run backup:restore path/to/backup.dump

# Restore with options
npm run backup:restore backup.dump -- --clean --data-only
```

## 📊 Monitoring & Health Checks

### Health Check Endpoints
- `GET /api/health` - Basic application health
- `GET /api/health/database` - Detailed database health
- `GET /api/health/database/performance` - Performance metrics
- `GET /api/health/database/migrations` - Migration status
- `GET /api/health/system` - System resource usage
- `GET /api/health/ready` - Kubernetes readiness probe
- `GET /api/health/live` - Kubernetes liveness probe

### Metrics Collected
- **Connection Pool**: Active, idle, and waiting connections
- **Query Performance**: Execution times and slow queries
- **Database Size**: Table sizes and growth trends
- **Index Usage**: Index efficiency and recommendations
- **Error Rates**: Database errors and connection failures

## 🚨 Troubleshooting

### Common Issues

#### Connection Failures
```bash
# Check database status
npm run db:health

# Test connection manually
psql $DATABASE_URL

# Check environment variables
echo $DATABASE_URL
```

#### Migration Issues
```bash
# Check migration status
npm run migrate:status

# Verify database schema
psql $DATABASE_URL -c "\dt"

# Check migration table
psql $DATABASE_URL -c "SELECT * FROM schema_migrations;"
```

#### Performance Issues
```bash
# Check database performance
curl http://localhost:3000/api/health/database/performance

# Monitor slow queries
tail -f logs/database.log | grep "slow_query"

# Check connection pool
curl http://localhost:3000/api/health/database
```

### Recovery Procedures

#### Database Corruption
1. Stop the application
2. Create emergency backup if possible
3. Restore from latest known good backup
4. Replay any missing transactions if available

#### Migration Failures
1. Check migration logs for specific errors
2. Manually fix any data inconsistencies
3. Re-run migrations with `npm run migrate`
4. Verify database integrity with health checks

## 🎯 Production Deployment

### Pre-Deployment Checklist
- [ ] Environment variables configured
- [ ] Database created and accessible
- [ ] Migrations tested in staging
- [ ] Backup system configured
- [ ] Monitoring endpoints accessible
- [ ] Performance benchmarks established

### Deployment Steps
1. **Backup Production Database**
   ```bash
   npm run backup:create production-pre-deploy-$(date +%Y%m%d)
   ```

2. **Run Database Setup**
   ```bash
   NODE_ENV=production npm run db:setup -- --backup-first
   ```

3. **Verify Health**
   ```bash
   curl https://your-domain.com/api/health
   ```

4. **Monitor Performance**
   ```bash
   curl https://your-domain.com/api/health/database/performance
   ```

### Production Configuration
```bash
# Production environment variables
NODE_ENV=production
DATABASE_URL=*************************************/torvie
DB_POOL_MAX=50
DB_POOL_MIN=10
AUTO_MIGRATE=false  # Disable auto-migration in production
LOG_LEVEL=info
```

## 📚 Additional Resources

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Node.js pg Library](https://node-postgres.com/)
- [Database Design Best Practices](https://www.postgresql.org/docs/current/ddl-best-practices.html)
- [Performance Tuning Guide](https://wiki.postgresql.org/wiki/Performance_Optimization)

---

**Ready for Distribution**: This database architecture is production-ready and designed to scale with your application's growth. The comprehensive migration system, automated backups, and health monitoring ensure reliable operation in any environment.
