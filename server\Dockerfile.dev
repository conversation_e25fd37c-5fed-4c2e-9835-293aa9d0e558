# Development Dockerfile for Node.js backend
FROM node:18-alpine

# Install dependencies for node-gyp
RUN apk add --no-cache python3 make g++

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p /app/storage /app/downloads

# Expose port
EXPOSE 3000

# Start development server with nodemon
CMD ["npm", "run", "dev"] 