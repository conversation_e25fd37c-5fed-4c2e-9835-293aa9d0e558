import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import Hls from 'hls.js';

const LiveStreamPlayer = ({
  streamUrl,
  streamTitle = 'Live Stream',
  streamerName = 'Streamer',
  viewerCount = 0,
  isLive = true,
  chatEnabled = true,
  onStreamEnd,
  className = '',
  logo,
  onNextChannel // Optional: callback to go to next channel on error
}) => {
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const videoRef = useRef(null);
  const chatRef = useRef(null);
  const hlsRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [showChat, setShowChat] = useState(false); // default to hidden
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [streamQuality, setStreamQuality] = useState('auto');
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  const [buffering, setBuffering] = useState(false);
  const [error, setError] = useState(null);
  const [showQualityMenu, setShowQualityMenu] = useState(false);

  // Initialize HLS stream
  const initializeHLS = useCallback((url) => {
    const video = videoRef.current;
    if (!video) return;

    // Clean up existing HLS instance
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    // Check if HLS is supported
    if (Hls.isSupported()) {
      console.log('🔴 Live TV: Initializing HLS stream:', url);
      const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90
      });

      hlsRef.current = hls;
      
      hls.loadSource(url);
      hls.attachMedia(video);

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('🔴 Live TV: HLS manifest parsed successfully');
        setConnectionStatus('connected');
        setError(null);
        video.play().catch(console.error);
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('🔴 Live TV: HLS error:', data);
        if (data.fatal) {
          setError('HLS stream error: ' + data.details);
          setConnectionStatus('error');
        }
      });

      hls.on(Hls.Events.BUFFER_STALLED, () => {
        setBuffering(true);
      });

      hls.on(Hls.Events.BUFFER_APPENDING, () => {
        setBuffering(false);
      });

    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      console.log('🔴 Live TV: Using native HLS support');
      video.src = url;
      video.addEventListener('loadedmetadata', () => {
        setConnectionStatus('connected');
        setError(null);
        video.play().catch(console.error);
      });
    } else {
      setError('HLS not supported in this browser');
      setConnectionStatus('error');
    }
  }, []);

  // Auto-start stream
  useEffect(() => {
    if (!streamUrl || !videoRef.current) return;

    const isHLS = streamUrl.includes('.m3u8') || streamUrl.includes('application/vnd.apple.mpegurl');
    
    if (isHLS) {
      initializeHLS(streamUrl);
    } else {
      // Regular video stream
      console.log('🔴 Live TV: Loading regular video stream:', streamUrl);
      videoRef.current.src = streamUrl;
      videoRef.current.load();
    }

    // Cleanup function
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [streamUrl, initializeHLS]);

  // Auto-hide controls
  useEffect(() => {
    let timeout;
    if (isPlaying && showControls) {
      timeout = setTimeout(() => setShowControls(false), 3000);
    }
    return () => clearTimeout(timeout);
  }, [isPlaying, showControls]);

  // Handle video events
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => {
      setIsPlaying(true);
      setConnectionStatus('connected');
      setError(null);
    };

    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      if (onStreamEnd) onStreamEnd();
    };
    const handleError = (e) => {
      setError('Stream connection failed');
      setConnectionStatus('error');
      console.error('Video error:', e);
    };
    const handleWaiting = () => setBuffering(true);
    const handleCanPlay = () => setBuffering(false);
    const handleVolumeChange = () => setVolume(video.volume);
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('error', handleError);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('volumechange', handleVolumeChange);
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('error', handleError);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('volumechange', handleVolumeChange);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [onStreamEnd]);

  // Cleanup HLS on unmount
  useEffect(() => {
    return () => {
      if (hlsRef.current) {
        console.log('🔴 Live TV: Cleaning up HLS instance');
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, []);

  // Simulated chat messages (replace with real WebSocket)
  useEffect(() => {
    if (!chatEnabled) return;

    const interval = setInterval(() => {
      const mockMessages = [
        { id: Date.now(), user: 'Viewer1', message: 'Great stream!', timestamp: new Date() },
        { id: Date.now() + 1, user: 'Viewer2', message: 'Love this content!', timestamp: new Date() },
        { id: Date.now() + 2, user: 'Viewer3', message: '🔥🔥🔥', timestamp: new Date() }
      ];
      
      setChatMessages(prev => [...prev.slice(-50), ...mockMessages.slice(0, Math.floor(Math.random() * 3) + 1)]);
    }, 3000);

    return () => clearInterval(interval);
  }, [chatEnabled]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.target.tagName === 'INPUT') return;

      switch (e.key) {
        case ' ':
          e.preventDefault();
          togglePlay();
          break;
        case 'm':
          toggleMute();
          break;
        case 'f':
          toggleFullscreen();
          break;
        case 'c':
          setShowChat(prev => !prev);
          break;
        case 'ArrowUp':
          e.preventDefault();
          adjustVolume(0.1);
          break;
        case 'ArrowDown':
          e.preventDefault();
          adjustVolume(-0.1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);

  // Control functions
  const togglePlay = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    if (video.paused) {
      video.play().catch(console.error);
    } else {
      video.pause();
    }
  }, []);

  const toggleMute = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
  }, []);

  const adjustVolume = useCallback((delta) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = Math.max(0, Math.min(1, video.volume + delta));
    video.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      videoRef.current?.requestFullscreen().catch(console.error);
    } else {
      document.exitFullscreen();
    }
  }, []);

  const sendChatMessage = useCallback((e) => {
    e.preventDefault();
    if (!newMessage.trim() || !user) return;

    const message = {
      id: Date.now(),
      user: profile?.name || user.username,
      message: newMessage.trim(),
      timestamp: new Date(),
      isOwn: true
    };

    setChatMessages(prev => [...prev, message]);
    setNewMessage('');
  }, [newMessage, user, profile]);

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'LIVE';
      case 'connecting': return 'CONNECTING';
      case 'error': return 'ERROR';
      default: return 'OFFLINE';
    }
  };

  return (
    <div className={`relative bg-black ${className}`}>
      {/* Main video container */}
      <div className="relative group">
        {/* Channel logo overlay */}
        {logo && (
          <div className="absolute top-4 left-4 z-10 w-12 h-12 bg-black/60 rounded-full flex items-center justify-center border-2 border-gray-700 overflow-hidden">
            <img src={logo} alt="Channel Logo" className="w-10 h-10 object-contain" />
          </div>
        )}
        {/* Video element */}
        <video
          ref={videoRef}
          className="w-full h-full object-contain"
          playsInline
          autoPlay
          muted={isMuted}
        />

        {/* Live indicator */}
        {isLive && (
          <div className="absolute top-4 left-4 flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${getConnectionStatusColor()} animate-pulse`}></div>
            <span className="text-white text-sm font-semibold bg-black bg-opacity-50 px-2 py-1 rounded">
              {getConnectionStatusText()}
            </span>
            <span className="text-white text-sm bg-black bg-opacity-50 px-2 py-1 rounded">
              {viewerCount.toLocaleString()} watching
            </span>
          </div>
        )}

        {/* Stream info */}
        <div className="absolute top-4 right-4 text-right">
          <h2 className="text-white text-lg font-bold bg-black bg-opacity-50 px-3 py-1 rounded">
            {streamTitle}
          </h2>
          <p className="text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded mt-1">
            {streamerName}
          </p>
        </div>

        {/* Buffering indicator */}
        {buffering && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p>Buffering...</p>
            </div>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-75 z-30">
            <div className="text-white text-center">
              <p className="text-xl mb-4">
                {error.includes('manifestLoadError') || error.includes('HLS stream error')
                  ? 'This channel is currently unavailable or geo-blocked.'
                  : error}
              </p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
                >
                  Retry
                </button>
                {onNextChannel && (
                  <button
                    onClick={onNextChannel}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                  >
                    Next Channel
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Video controls */}
        <div
          className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 transition-opacity duration-300 ${
            showControls ? 'opacity-100' : 'opacity-0'
          }`}
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => isPlaying && setShowControls(false)}
        >
          {/* Progress bar */}
          <div className="w-full bg-gray-600 rounded-full h-1 mb-4">
            <div className="bg-red-600 h-1 rounded-full w-full"></div>
          </div>

          {/* Control buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={togglePlay}
                className="text-white hover:text-gray-300 transition-colors"
              >
                {isPlaying ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                )}
              </button>

              <button
                onClick={toggleMute}
                className="text-white hover:text-gray-300 transition-colors"
              >
                {isMuted ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L5.5 14H3a1 1 0 01-1-1V7a1 1 0 011-1h2.5l3.883-3.793a1 1 0 011.414.086zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L5.5 14H3a1 1 0 01-1-1V7a1 1 0 011-1h2.5l3.883-3.793a1 1 0 011.414.086zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                )}
              </button>

              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => adjustVolume(parseFloat(e.target.value) - volume)}
                  className="w-20"
                />
                <span className="text-white text-sm">{Math.round(volume * 100)}%</span>
              </div>
              {/* Live Chat button in controls */}
              {chatEnabled && (
                <button
                  onClick={() => setShowChat(true)}
                  className="text-white hover:text-blue-400 transition-colors flex items-center px-3 py-1 rounded bg-blue-600 hover:bg-blue-700 ml-2"
                  title="Open Live Chat"
                >
                  <svg className="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                  </svg>
                  <span className="font-semibold">Live Chat</span>
                </button>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {/* Quality selector */}
              <div className="relative">
                <button
                  onClick={() => setShowQualityMenu(!showQualityMenu)}
                  className="text-white hover:text-gray-300 transition-colors"
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
                
                {showQualityMenu && (
                  <div className="absolute bottom-full right-0 mb-2 bg-gray-800 rounded shadow-lg">
                    {['auto', '1080p', '720p', '480p', '360p'].map((quality) => (
                      <button
                        key={quality}
                        onClick={() => {
                          setStreamQuality(quality);
                          setShowQualityMenu(false);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-700 ${
                          streamQuality === quality ? 'text-blue-400' : 'text-white'
                        }`}
                      >
                        {quality}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Fullscreen toggle */}
              <button
                onClick={toggleFullscreen}
                className="text-white hover:text-gray-300 transition-colors"
              >
                {isFullscreen ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Chat modal */}
      {chatEnabled && showChat && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm">
          <div className="relative w-full max-w-md mx-auto bg-gray-900 rounded-2xl shadow-2xl flex flex-col h-[80vh]">
            {/* Close chat modal button */}
            <button
              className="absolute top-4 right-4 text-white bg-black/70 hover:bg-black/90 rounded-full p-2 focus:outline-none z-20"
              onClick={() => setShowChat(false)}
              aria-label="Close Chat"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            {/* Chat header */}
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-white font-semibold">Live Chat</h3>
              <p className="text-gray-400 text-sm">{viewerCount.toLocaleString()} viewers</p>
            </div>
            {/* Chat messages */}
            <div
              ref={chatRef}
              className="flex-1 overflow-y-auto p-4 space-y-2"
            >
              {chatMessages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex flex-col ${msg.isOwn ? 'items-end' : 'items-start'}`}
                >
                  <div className={`max-w-xs px-3 py-2 rounded-lg ${
                    msg.isOwn
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-white'
                  }`}>
                    <p className="text-xs font-semibold mb-1">{msg.user}</p>
                    <p className="text-sm">{msg.message}</p>
                  </div>
                  <span className="text-xs text-gray-500 mt-1">
                    {msg.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
            {/* Chat input */}
            <div className="p-4 border-t border-gray-700">
              <form onSubmit={sendChatMessage} className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 bg-gray-800 text-white px-3 py-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  maxLength={200}
                />
                <button
                  type="submit"
                  disabled={!newMessage.trim()}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
                >
                  Send
                </button>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveStreamPlayer; 