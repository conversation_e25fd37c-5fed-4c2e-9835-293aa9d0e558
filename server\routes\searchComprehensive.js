import express from 'express';
import fetch from 'node-fetch';
import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';

const router = express.Router();
const TMDB_BASE_URL = 'https://api.themoviedb.org/3';

router.get('/', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  const { query } = req.query;

  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
  if (!query || query.trim() === '') return res.status(400).json({ error: 'Search query is required' });

  console.log(`🔍 Comprehensive TMDB search for: "${query}"`);

  const searchPromises = [];
  const maxPages = 5;

  const addPromises = (type) => {
    for (let page = 1; page <= maxPages; page++) {
      searchPromises.push(
        fetch(`${TMDB_BASE_URL}/search/${type}?api_key=${TMDB_API_KEY}&query=${encodeURIComponent(query)}&include_adult=false&page=${page}`)
          .then(r => r.json())
          .then(data => ({ type, page, data }))
          .catch(err => ({ type, page, error: err.message }))
      );
    }
  };

  addPromises('movie');
  addPromises('tv');
  for (let page = 1; page <= 3; page++) {
    searchPromises.push(
      fetch(`${TMDB_BASE_URL}/search/multi?api_key=${TMDB_API_KEY}&query=${encodeURIComponent(query)}&include_adult=false&page=${page}`)
        .then(r => r.json())
        .then(data => ({ type: 'multi', page, data }))
        .catch(err => ({ type: 'multi', page, error: err.message }))
    );
  }

  const results = await Promise.allSettled(searchPromises);
  let movies = [], shows = [];
  let totalMovieResults = 0, totalShowResults = 0;

  results.forEach(r => {
    if (r.status !== 'fulfilled' || !r.value.data) return;
    const { type, data } = r.value;
    if (!data.results) return;
    if (type === 'movie') {
      movies = movies.concat(data.results.map(i => ({ ...i, media_type: 'movie', source: 'tmdb_movie' })));
      totalMovieResults = Math.max(totalMovieResults, data.total_results || 0);
    } else if (type === 'tv') {
      shows = shows.concat(data.results.map(i => ({ ...i, media_type: 'tv', source: 'tmdb_tv' })));
      totalShowResults = Math.max(totalShowResults, data.total_results || 0);
    } else {
      data.results.forEach(i => {
        if (i.media_type === 'movie') movies.push({ ...i, source: 'tmdb_multi' });
        else if (i.media_type === 'tv') shows.push({ ...i, source: 'tmdb_multi' });
      });
    }
  });

  const dedupe = arr => Array.from(arr.reduce((acc, cur) => {
    const existing = acc.get(cur.id);
    if (!existing || (cur.popularity || 0) > (existing.popularity || 0)) acc.set(cur.id, cur);
    return acc;
  }, new Map()).values());

  const finalMovies = dedupe(movies).sort((a,b)=>(b.popularity||0)-(a.popularity||0)).slice(0,100);
  const finalShows = dedupe(shows).sort((a,b)=>(b.popularity||0)-(a.popularity||0)).slice(0,100);
  const allResults = [...finalMovies, ...finalShows].sort((a,b)=>(b.popularity||0)-(a.popularity||0));

  res.json({
    success:true,
    query: query.trim(),
    results: allResults,
    movies: finalMovies,
    shows: finalShows,
    stats:{ total: allResults.length, movies:finalMovies.length, shows:finalShows.length, totalMovieResults, totalShowResults }
  });
}));

export default router; 