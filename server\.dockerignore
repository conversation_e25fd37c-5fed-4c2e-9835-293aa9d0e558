node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.*
.nyc_output
coverage
.cache
.parcel-cache
dist
build
.DS_Store
*.log
*.pid
*.seed
*.pid.lock

# Exclude downloads folder (this is HUGE)
downloads/
storage/*.backup
storage/*.json.backup

# Test files
*.test.js
*.spec.js
tests/
__tests__/

# Development files
.eslintrc*
.prettierrc*
jest.config.js
nodemon.json

# Documentation
*.md
docs/

# Temporary files
tmp/
temp/
*.tmp
*.temp 