import React, { useState, useEffect } from 'react';
import { apiFetch } from '../utils/api.js';
import { useNavigate } from 'react-router-dom';

const ActorModal = ({ isOpen, onClose, actorId }) => {
  const [actor, setActor] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen && actorId) {
      fetchActorDetails();
    }
  }, [isOpen, actorId]);

  const fetchActorDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiFetch(`/api/person/${actorId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch actor details: ${response.status}`);
      }
      const data = await response.json();
      setActor(data);
    } catch (err) {
      console.error('Error fetching actor details:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleMovieClick = (movieId) => {
    onClose();
    navigate(`/movie/${movieId}`);
  };

  const handleTVClick = (tvId) => {
    onClose();
    navigate(`/show/${tvId}`);
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-40 cursor-pointer"
      onClick={onClose}
    >
      {/* Glass overlay that covers ENTIRE viewport */}
      <div 
        className="fixed backdrop-blur-xl bg-black/70" 
        style={{ 
          top: 0,
          left: 0, 
          right: 0, 
          bottom: 0,
          width: '100vw',
          height: '100vh'
        }}
      />
      
      {/* Header spacer to keep header visible and clickable */}
      <div 
        className="fixed bg-transparent z-[60]" 
        style={{ 
          top: 0,
          left: 0, 
          right: 0, 
          height: '80px',
          width: '100vw'
        }}
      />
      
      {/* Modal container positioned below header */}
      <div 
        className="fixed flex items-center justify-center z-[51]" 
        style={{ 
          top: '80px',
          left: 0,
          right: 0,
          bottom: 0,
          width: '100vw',
          height: 'calc(100vh - 80px)'
        }}
      >
        <div 
          className="w-[90vw] h-[85vh] max-w-4xl bg-gray-900 rounded-xl overflow-hidden shadow-2xl relative"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 text-white hover:bg-black/50 rounded-full transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Content */}
          <div className="h-full overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full text-center">
                <div>
                  <div className="text-red-400 text-xl mb-2">Error loading actor details</div>
                  <div className="text-gray-400 text-sm">{error}</div>
                </div>
              </div>
            ) : actor ? (
              <>
                {/* Actor Header */}
                <div className="flex flex-col md:flex-row gap-6 mb-8">
                  <div className="flex-shrink-0">
                    <img
                      src={actor.profile_path ? `https://image.tmdb.org/t/p/w500${actor.profile_path}` : 'https://placehold.co/300x450?text=No+Image'}
                      alt={actor.name}
                      className="w-64 h-96 object-cover rounded-lg bg-gray-800"
                    />
                  </div>
                  
                  <div className="flex-1">
                    <h1 className="text-4xl font-bold text-white mb-4">{actor.name}</h1>
                    
                    {/* Basic Info */}
                    <div className="space-y-3 mb-6">
                      {actor.birthday && (
                        <div className="flex items-center">
                          <span className="text-gray-400 font-semibold w-32">Born:</span>
                          <span className="text-white">
                            {new Date(actor.birthday).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                            {actor.place_of_birth && ` in ${actor.place_of_birth}`}
                          </span>
                        </div>
                      )}
                      
                      {actor.deathday && (
                        <div className="flex items-center">
                          <span className="text-gray-400 font-semibold w-32">Died:</span>
                          <span className="text-white">
                            {new Date(actor.deathday).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                      )}
                      
                      {actor.known_for_department && (
                        <div className="flex items-center">
                          <span className="text-gray-400 font-semibold w-32">Known for:</span>
                          <span className="text-white">{actor.known_for_department}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Biography */}
                    {actor.biography && (
                      <div>
                        <h3 className="text-xl font-semibold text-white mb-3">Biography</h3>
                        <p className="text-gray-300 leading-relaxed">
                          {actor.biography.length > 400 ? 
                            `${actor.biography.substring(0, 400)}...` : 
                            actor.biography
                          }
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Known For Movies */}
                {actor.movie_credits && actor.movie_credits.cast && actor.movie_credits.cast.length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-2xl font-semibold text-white mb-4">Known For (Movies)</h3>
                    <div className="flex gap-4 overflow-x-auto pb-4">
                      {actor.movie_credits.cast
                        .filter(movie => movie.poster_path)
                        .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
                        .slice(0, 10)
                        .map(movie => (
                          <div 
                            key={movie.id} 
                            className="flex-shrink-0 w-32 cursor-pointer hover:scale-105 transition-transform"
                            onClick={() => handleMovieClick(movie.id)}
                          >
                            <img
                              src={`https://image.tmdb.org/t/p/w185${movie.poster_path}`}
                              alt={movie.title}
                              className="w-full h-48 object-cover rounded-lg mb-2"
                            />
                            <div className="text-xs text-white font-semibold truncate">{movie.title}</div>
                            <div className="text-xs text-gray-400">{movie.release_date ? new Date(movie.release_date).getFullYear() : 'N/A'}</div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* Known For TV Shows */}
                {actor.tv_credits && actor.tv_credits.cast && actor.tv_credits.cast.length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-2xl font-semibold text-white mb-4">Known For (TV Shows)</h3>
                    <div className="flex gap-4 overflow-x-auto pb-4">
                      {actor.tv_credits.cast
                        .filter(show => show.poster_path)
                        .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
                        .slice(0, 10)
                        .map(show => (
                          <div 
                            key={show.id} 
                            className="flex-shrink-0 w-32 cursor-pointer hover:scale-105 transition-transform"
                            onClick={() => handleTVClick(show.id)}
                          >
                            <img
                              src={`https://image.tmdb.org/t/p/w185${show.poster_path}`}
                              alt={show.name}
                              className="w-full h-48 object-cover rounded-lg mb-2"
                            />
                            <div className="text-xs text-white font-semibold truncate">{show.name}</div>
                            <div className="text-xs text-gray-400">{show.first_air_date ? new Date(show.first_air_date).getFullYear() : 'N/A'}</div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActorModal; 