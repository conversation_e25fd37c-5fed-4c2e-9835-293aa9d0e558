import React, { useEffect, useState } from 'react';
import MediaCard from '../components/MediaCard';
import EnhancedVideoPlayer from '../components/EnhancedVideoPlayer';
import TrailerPlayer from '../components/TrailerPlayer';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import { watchlistStorage } from '../utils/watchlistStorage';
import { TypewriterText, funnyLoadingPhrases, getRandomPhrase } from '../utils/loadingPhrases.jsx';
import { apiFetch } from '../utils/api';

const Watchlist = () => {
  const { profile: currentProfile, isProfileLoaded } = useProfile();
  const { user } = useAuth();
  const [watchlist, setWatchlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [videoPlayer, setVideoPlayer] = useState({ isOpen: false, magnetLink: null, title: null, fallbackTorrents: [], contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, media: null, trailer: null });
  const navigate = useNavigate();

  // Update watchlist when profile changes
  useEffect(() => {
    // Don't load watchlist until profile is loaded to avoid clearing on initial render
    if (!isProfileLoaded) {
      console.log('🏠 Watchlist: Waiting for profile to load...');
      return;
    }
    
    const loadWatchlist = async () => {
      console.log('🏠 Watchlist: Loading watchlist data from standalone app storage');
      console.log('👤 Current profile:', currentProfile);
      console.log('👤 Current user:', user);
      
      if (user && currentProfile && currentProfile.id) {
        try {
          const stored = await watchlistStorage.getWatchlist(user.id, currentProfile.id);
          console.log(`✅ Watchlist: Loaded ${stored.length} items from local app storage`);
          setWatchlist(stored);
        } catch (error) {
          console.error('❌ Watchlist: Error loading watchlist data:', error);
          setWatchlist([]);
          setError('Failed to load watchlist data');
        }
      } else {
        console.log(`👤 Watchlist: No user or profile, clearing watchlist`);
        setWatchlist([]);
      }
      setLoading(false);
    };
    
    loadWatchlist();
  }, [user, currentProfile, isProfileLoaded]);

  // Scroll to top on initial load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const toggleWatchlist = async (movie) => {
    if (!user || !currentProfile || !currentProfile.id) {
      console.log('❌ Watchlist: No user or current profile for toggleWatchlist');
      return;
    }
    
    console.log(`🏠 Watchlist: Toggling watchlist for movie "${movie.title || movie.name}"`);
    
    const exists = watchlist.some(item => String(item.id) === String(movie.id));
    let success;
    
    if (exists) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, movie.id, movie.media_type || 'movie');
      if (success) {
        setWatchlist(prev => prev.filter(item => String(item.id) !== String(movie.id)));
        console.log(`➖ Watchlist: Removed "${movie.title || movie.name}" from watchlist`);
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, { ...movie, media_type: movie.media_type || 'movie' });
      if (success) {
        setWatchlist(prev => [...prev, movie]);
        console.log(`➕ Watchlist: Added "${movie.title || movie.name}" to watchlist`);
      }
    }
    
    if (!success) {
      console.error(`❌ Watchlist: Failed to toggle watchlist for "${movie.title || movie.name}"`);
    }
  };

  const isInWatchlist = (id) => {
    return watchlist.some(item => String(item.id) === String(id));
  };

  const handlePlayMovie = async (movie) => {
    if (!movie) {
      console.error('🎬 No movie provided to handlePlayMovie');
      return;
    }

    console.log('🎬 handlePlayMovie called with movie:', movie);
    console.log('🎬 Movie ID:', movie.id);
    console.log('🎬 Movie title:', movie.title || movie.name);

    try {
      const searchTitle = movie.title || movie.name;
      const contentType = movie.media_type || (movie.title ? 'movie' : 'tv');
      console.log(`🎬 Searching torrents for: "${searchTitle}" (ID: ${movie.id})`);
      
      const response = await apiFetch(`/search?query=${encodeURIComponent(searchTitle)}&type=${contentType}`);
      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        console.log(`🎬 Found ${data.results.length} torrents for "${searchTitle}"`);
        
        // Filter and sort torrents - prioritize those with good seeders
        const validTorrents = data.results
          .filter(torrent => torrent.magnetLink && torrent.seeders > 0)
          .sort((a, b) => (b.seeders || 0) - (a.seeders || 0))
          .slice(0, 5); // Keep top 5 for fallback
        
        if (validTorrents.length === 0) {
          console.error(`🎬 No valid torrents with seeders found for "${searchTitle}"`);
          return;
        }
        
        console.log(`🎬 Selected top ${validTorrents.length} torrents for fallback support`);
        
        // Start with the best torrent, VideoPlayer will handle fallbacks
        const bestTorrent = validTorrents[0];
        console.log(`🎬 Starting with best torrent: "${bestTorrent.title}" (${bestTorrent.seeders} seeders) for movie "${searchTitle}"`);
        
        setVideoPlayer({ 
          isOpen: true, 
          magnetLink: bestTorrent.magnetLink, 
          title: searchTitle,
          fallbackTorrents: validTorrents.slice(1), // Pass remaining torrents for fallback
          contentId: `${contentType}_${movie.id}`, // Use consistent format: movie_123 or tv_123
          contentType: contentType,
          posterUrl: movie.poster_path ? `https://image.tmdb.org/t/p/w500${movie.poster_path}` : null,
          episodeInfo: null
        });
      } else {
        console.log(`🎬 No torrents found for: "${searchTitle}"`);
      }
    } catch (error) {
      console.error('🎬 Error searching for torrents:', error);
    }
  };

  const closeVideoPlayer = () => {
    setVideoPlayer({ isOpen: false, magnetLink: null, title: null, fallbackTorrents: [], contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  };

  const closeTrailerModal = () => {
    setTrailerModal({ isOpen: false, media: null, trailer: null });
  };

  const handleTrailer = async (media) => {
    if (!media) {
      console.warn('🎬 No media provided to handleTrailer');
      return;
    }

    try {
      console.log('🎬 Fetching trailer for:', media.title || media.name);
      
      // Determine if it's a movie or TV show and use appropriate endpoint
      const isMovie = media.media_type === 'movie' || media.title;
      const endpoint = isMovie ? `movies/${media.id}/videos` : `tv/${media.id}/videos`;
      const response = await apiFetch(`/${endpoint}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch videos: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('🎬 Video data received:', data);
      
      // Find the best trailer - prioritize official trailers
      const trailers = data.results?.filter(video => 
        video.site === 'YouTube' && 
        (video.type === 'Trailer' || video.type === 'Teaser') &&
        // Filter out portrait/vertical videos (like YouTube Shorts)
        (!video.name?.toLowerCase().includes('short') && 
         !video.name?.toLowerCase().includes('vertical') &&
         !video.name?.toLowerCase().includes('portrait'))
      ) || [];
      
      console.log(`🎬 Found ${trailers.length} potential trailers:`, trailers.map(t => t.name));
      
      // Sort by priority: Official Trailer first, then Teaser, then others
      const sortedTrailers = trailers.sort((a, b) => {
        const aPriority = a.type === 'Trailer' ? 1 : 2;
        const bPriority = b.type === 'Trailer' ? 1 : 2;
        
        // If same type, prefer "Official" in the name
        if (aPriority === bPriority) {
          const aIsOfficial = a.name?.toLowerCase().includes('official');
          const bIsOfficial = b.name?.toLowerCase().includes('official');
          if (aIsOfficial && !bIsOfficial) return -1;
          if (!aIsOfficial && bIsOfficial) return 1;
        }
        
        return aPriority - bPriority;
      });
      
      if (sortedTrailers.length > 0) {
        const trailer = sortedTrailers[0];
        console.log('🎬 Opening trailer modal with:', trailer.name);
        setTrailerModal({ isOpen: true, media, trailer });
      } else {
        console.log('🎬 No trailers found for:', media.title || media.name);
        // Could show a notification here
        alert(`No trailer available for ${media.title || media.name}`);
      }
    } catch (error) {
      console.error('🎬 Error fetching trailer:', error);
      alert(`Failed to load trailer: ${error.message}`);
    }
  };

  const handleMoreInfo = (movie) => {
    // Keep this for when user actually wants to go to details page (like clicking the card)
    navigate(`/movie/${movie.id}`, { state: { movie } });
  };

  const handleCardClick = (movie) => {
    // Save current scroll position for return navigation
    sessionStorage.setItem('watchlist_scroll_position', window.scrollY.toString());
    
    // Preserve current location state so back button works correctly
    navigate(`/movie/${movie.id}`, { 
      state: { 
        movie,
        from: '/watchlist', // Track where we came from
        returnPath: window.location.pathname // Preserve exact return path
      } 
    });
  };

  // Restore scroll position when returning to watchlist
  useEffect(() => {
    const savedScrollPosition = sessionStorage.getItem('watchlist_scroll_position');
    if (savedScrollPosition) {
      window.scrollTo(0, parseInt(savedScrollPosition));
      // Clean up the saved position
      sessionStorage.removeItem('watchlist_scroll_position');
    }
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-16 max-w-md mx-auto">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-xl mb-4">Loading your watchlist...</p>
          
          {/* Funny Typewriter Message */}
          <div className="text-cyan-300 text-lg mb-4 min-h-[2rem]">
            <TypewriterText
              text={getRandomPhrase(funnyLoadingPhrases)}
              speed={40}
              className="loading-glow"
              showCursor={true}
            />
          </div>
          
          <p className="text-gray-500 text-sm opacity-60">Gathering your saved favorites...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <p className="text-red-400 mb-4">{error}</p>
          <button 
            onClick={() => {
              setError(null);
              setLoading(true);
              // Retry loading
              if (user && currentProfile && currentProfile.id) {
                const key = `torvie_watchlist_${user.id}_${currentProfile.id}`;
                const stored = localStorage.getItem(key);
                if (stored) {
                  try {
                    const parsed = JSON.parse(stored);
                    setWatchlist(parsed);
                  } catch (error) {
                    console.error('Error parsing watchlist data:', error);
                    setWatchlist([]);
                  }
                } else {
                  setWatchlist([]);
                }
              }
              setLoading(false);
            }}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!watchlist.length) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <h1 className="text-3xl font-bold mb-6 text-center">My Watchlist</h1>
          <p className="text-2xl text-gray-400 mb-4">Your watchlist is empty.</p>
          <p className="text-gray-500 mb-6">Start adding movies and shows to your watchlist!</p>
          <button 
            onClick={() => navigate('/')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse Movies
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen px-6 py-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">My Watchlist</h1>
          <p className="text-gray-400">{watchlist.length} item{watchlist.length !== 1 ? 's' : ''}</p>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 justify-items-center">
          {watchlist.map(movie => (
            <div key={movie.id} className="w-full max-w-[200px]">
              <MediaCard 
                media={movie} 
                onClick={() => handleCardClick(movie)}
                onPlay={(media) => handlePlayMovie(media)}
                onMoreInfo={(media) => handleTrailer(media)}
                isInWatchlist={isInWatchlist(movie.id)}
                toggleWatchlist={() => toggleWatchlist(movie)}
                noWatchlistGlow={true}
              />
            </div>
          ))}
        </div>
      </div>
      
      {/* Video Player */}
      {videoPlayer.isOpen && (
        <EnhancedVideoPlayer
          magnetLink={videoPlayer.magnetLink}
          title={videoPlayer.title}
          onClose={closeVideoPlayer}
          fallbackTorrents={videoPlayer.fallbackTorrents}
          contentId={videoPlayer.contentId}
          contentType={videoPlayer.contentType || "movie"}
          posterUrl={videoPlayer.posterUrl}
          episodeInfo={videoPlayer.episodeInfo}
        />
      )}

      {/* Trailer Modal */}
      {trailerModal.isOpen && trailerModal.media && trailerModal.trailer && (
        <div 
          className="fixed inset-0 z-40 cursor-pointer"
          onClick={closeTrailerModal}
        >
          {/* Glass overlay that covers ENTIRE viewport */}
          <div 
            className="fixed backdrop-blur-xl bg-black/70" 
            style={{ 
              top: 0,
              left: 0, 
              right: 0, 
              bottom: 0,
              width: '100vw',
              height: '100vh'
            }}
          />
          {/* Header spacer to keep header visible and clickable */}
          <div 
            className="fixed bg-transparent z-[60]" 
            style={{ 
              top: 0,
              left: 0, 
              right: 0, 
              height: '80px',
              width: '100vw'
            }}
          />
          {/* Trailer container positioned below header */}
          <div 
            className="fixed flex items-center justify-center z-[51]" 
            style={{ 
              top: '80px',
              left: 0,
              right: 0,
              bottom: 0,
              width: '100vw',
              height: 'calc(100vh - 80px)'
            }}
          >
            <div className="w-[80vw] h-[70vh] max-w-5xl flex flex-col rounded-xl overflow-hidden shadow-2xl bg-black relative" onClick={(e) => e.stopPropagation()}>
              {/* Close button in top right corner */}
              <button
                onClick={closeTrailerModal}
                className="absolute top-4 right-4 z-10 p-2 text-white hover:bg-black/50 rounded-full transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              {/* Trailer Video - Full container */}
              <div className="w-full h-full bg-black">
                <TrailerPlayer 
                  videoId={trailerModal.trailer.key} 
                  title={trailerModal.media.title || trailerModal.media.name} 
                  posterUrl={trailerModal.media.poster_path ? `https://image.tmdb.org/t/p/w500${trailerModal.media.poster_path}` : undefined} 
                  startMuted={false} 
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Watchlist; 