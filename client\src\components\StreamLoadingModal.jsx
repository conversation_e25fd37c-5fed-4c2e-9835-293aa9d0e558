import React, { useEffect, useState, useRef } from 'react';
import { TypewriterText, streamingLoadingPhrases, getRandomPhrase } from '../utils/loadingPhrases.jsx';

const DEFAULT_MESSAGES = [
  'Connecting to torrent peers...',
  'Downloading metadata...',
  'Buffering video stream...',
  'Preparing video player...',
  'Establishing connection...',
  'Loading video data...',
  'Initializing stream...',
  'Processing request...'
];

// Error message mapping for different error types
const getErrorMessage = (error, errorType) => {
  switch (errorType) {
    case 'NO_PEERS':
      return {
        title: 'No Seeders Available',
        message: 'This torrent has no active seeders. Try a different torrent or check back later.',
        icon: '🌱',
        color: 'text-yellow-400'
      };
    case 'NO_VIDEO_FILE':
      return {
        title: 'No Video File Found',
        message: 'This torrent doesn\'t contain any playable video files.',
        icon: '🎬',
        color: 'text-orange-400'
      };
    case 'INVALID_MAGNET':
      return {
        title: 'Invalid Torrent Link',
        message: 'The provided torrent link is invalid or corrupted.',
        icon: '🔗',
        color: 'text-red-400'
      };
    case 'STREAM_NOT_FOUND':
      return {
        title: 'Stream Not Available',
        message: 'The stream is no longer available or is still buffering.',
        icon: '📺',
        color: 'text-blue-400'
      };
    case 'INVALID_INPUT':
      return {
        title: 'Invalid Request',
        message: 'Please provide a valid torrent link.',
        icon: '⚠️',
        color: 'text-red-400'
      };
    case 'NETWORK_ERROR':
      return {
        title: 'Connection Error',
        message: 'Unable to connect to the streaming server. Please check your internet connection.',
        icon: '🌐',
        color: 'text-red-400'
      };
    default:
      return {
        title: 'Stream Error',
        message: error || 'An unexpected error occurred while starting the stream.',
        icon: '❌',
        color: 'text-red-400'
      };
  }
};

const StreamLoadingModal = ({ visible, loading = true, error = null, errorType = null, onCancel, onRetry, messages = DEFAULT_MESSAGES, interval = 2500, progress = null }) => {
  const [currentMsgIdx, setCurrentMsgIdx] = useState(0);
  const [funnyPhrase, setFunnyPhrase] = useState("");
  const [lastFunnyPhraseIndex, setLastFunnyPhraseIndex] = useState(-1);
  const timerRef = useRef();
  const funnyPhraseRef = useRef();

  useEffect(() => {
    if (!visible || error) return;
    timerRef.current = setInterval(() => {
      setCurrentMsgIdx(idx => (idx + 1) % messages.length);
    }, interval);
    return () => clearInterval(timerRef.current);
  }, [visible, messages, interval, error]);

  // Helper function to get next random phrase without repeating
  const getNextUniquePhrase = () => {
    if (streamingLoadingPhrases.length <= 1) return streamingLoadingPhrases[0] || "";
    
    let nextIndex;
    do {
      nextIndex = Math.floor(Math.random() * streamingLoadingPhrases.length);
    } while (nextIndex === lastFunnyPhraseIndex);
    
    setLastFunnyPhraseIndex(nextIndex);
    return streamingLoadingPhrases[nextIndex];
  };

  // Funny phrase cycling effect
  useEffect(() => {
    if (!visible || error) return;
    
    // Set initial funny phrase
    const initialPhrase = getRandomPhrase(streamingLoadingPhrases);
    const initialIndex = streamingLoadingPhrases.indexOf(initialPhrase);
    setFunnyPhrase(initialPhrase);
    setLastFunnyPhraseIndex(initialIndex);
    
    // Cycle through funny phrases every 4 seconds
    funnyPhraseRef.current = setInterval(() => {
      setFunnyPhrase(getNextUniquePhrase());
    }, 4000);
    
    return () => clearInterval(funnyPhraseRef.current);
  }, [visible, error]);

  useEffect(() => {
    if (!visible) {
      setCurrentMsgIdx(0);
      setFunnyPhrase("");
      setLastFunnyPhraseIndex(-1);
    }
  }, [visible]);

  if (!visible) return null;

  const errorInfo = error ? getErrorMessage(error, errorType) : null;

  return (
    <div className="fixed inset-0 z-[99999] flex items-center justify-center bg-black transition-all" style={{ top: 0, left: 0, right: 0, bottom: 0, position: 'fixed' }}>
      <div className="flex flex-col items-center p-8 rounded-xl shadow-2xl bg-gray-900/90 border border-purple-700/40 min-w-[340px]">
        {/* Loading State */}
        {loading && !error && (
          <>
            <div className="mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-500"></div>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Preparing Your Stream...</h2>
            {progress && (
              <p className="text-sm text-blue-400 mb-2 text-center">
                {progress}
              </p>
            )}
            
            {/* Funny Typewriter Message */}
            <div className="text-lg text-cyan-300 mb-4 min-h-[2.5rem] transition-all text-center" style={{minWidth: 320}}>
              {funnyPhrase && (
                <TypewriterText
                  text={funnyPhrase}
                  speed={40}
                  className="loading-glow"
                  showCursor={true}
                />
              )}
            </div>
            
            {/* Technical Message */}
            <p className="text-sm text-indigo-400 mb-6 min-h-[1.5rem] opacity-60 text-center" style={{minWidth: 320}}>
              {messages[currentMsgIdx]}
            </p>
            
            <button
              onClick={onCancel}
              className="mt-2 px-6 py-2 bg-gray-700 hover:bg-gray-800 text-white rounded-lg text-base font-medium transition-colors"
            >
              Cancel
            </button>
          </>
        )}
        {/* Error State */}
        {error && errorInfo && (
          <>
            <div className="mb-6">
              <div className="text-5xl mb-2">{errorInfo.icon}</div>
            </div>
            <h2 className={`text-2xl font-bold ${errorInfo.color} mb-2`}>{errorInfo.title}</h2>
            <p className="text-lg text-gray-300 mb-6 min-h-[2.5rem] transition-all text-center" style={{minWidth: 320}}>
              {errorInfo.message}
            </p>
            <div className="flex space-x-4">
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-base font-medium transition-colors"
                >
                  Try Again
                </button>
              )}
              <button
                onClick={onCancel}
                className="px-6 py-2 bg-gray-700 hover:bg-gray-800 text-white rounded-lg text-base font-medium transition-colors"
              >
                Close
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default StreamLoadingModal; 