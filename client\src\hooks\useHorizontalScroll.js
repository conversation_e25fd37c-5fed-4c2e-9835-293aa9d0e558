import { useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook for enabling drag-to-scroll on horizontal rows
 * Perfect for Netflix-style horizontal movie/TV rows
 * Users can drag to scroll horizontally, mouse wheel scrolls vertically
 * Includes momentum scrolling and click prevention during drag
 */
export const useHorizontalScroll = () => {
  const scrollRef = useRef(null);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element) return;

    let isDown = false;
    let startX;
    let scrollLeft;
    let lastX;
    let velocity = 0;
    let momentum = 0;
    let animationId = null;
    let hasMoved = false;
    let dragThreshold = 8; // Increased threshold to be more forgiving
    let clickPrevented = false;

    const handleMouseDown = (e) => {
      // Don't interfere with button clicks or interactive elements
      if (e.target.tagName === 'BUTTON' || e.target.closest('button') || e.target.closest('[role="button"]')) {
        return;
      }
      
      isDown = true;
      hasMoved = false;
      clickPrevented = false;
      element.classList.add('cursor-grabbing');
      startX = e.pageX - element.offsetLeft;
      lastX = startX;
      scrollLeft = element.scrollLeft;
      velocity = 0;
      momentum = 0;
      
      // Cancel any ongoing momentum animation
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
    };

    const handleMouseLeave = () => {
      if (isDown) {
        // Apply momentum if user was dragging when mouse left
        applyMomentum();
      }
      isDown = false;
      element.classList.remove('cursor-grabbing');
    };

    const handleMouseUp = () => {
      if (isDown) {
        applyMomentum();
      }
      isDown = false;
      element.classList.remove('cursor-grabbing');
      
      // Reset click prevention after a brief delay
      setTimeout(() => {
        clickPrevented = false;
      }, 50);
    };

    const handleMouseMove = (e) => {
      if (!isDown) return;
      e.preventDefault();
      
      const x = e.pageX - element.offsetLeft;
      const deltaX = x - startX;
      
      // Check if this constitutes a drag (moved beyond threshold)
      if (Math.abs(deltaX) > dragThreshold) {
        hasMoved = true;
        clickPrevented = true;
      }
      
      // Calculate velocity for momentum
      velocity = x - lastX;
      lastX = x;
      
      // Smooth scrolling during drag
      const walk = deltaX * 1.2; // Slightly enhanced sensitivity
      element.scrollLeft = scrollLeft - walk;
    };

    const applyMomentum = () => {
      if (Math.abs(velocity) < 1) return; // Too slow to apply momentum
      
      momentum = velocity * 2; // Even weaker momentum (was 4)
      
      // Much smaller cap for maximum momentum
      const maxMomentum = 400; // Roughly 2-3 movie cards worth (was 800)
      momentum = Math.sign(momentum) * Math.min(Math.abs(momentum), maxMomentum);
      
      const momentumAnimation = () => {
        if (Math.abs(momentum) < 0.5) {
          momentum = 0;
          return;
        }
        
        element.scrollLeft -= momentum;
        momentum *= 0.80; // Even higher friction (was 0.85)
        
        animationId = requestAnimationFrame(momentumAnimation);
      };
      
      animationId = requestAnimationFrame(momentumAnimation);
    };

    const handleClick = (e) => {
      // Only prevent clicks on media cards if we actually dragged
      if (clickPrevented && hasMoved && e.target.closest('.media-card')) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
      }
    };

    // Add event listeners
    element.addEventListener('mousedown', handleMouseDown);
    element.addEventListener('mouseleave', handleMouseLeave);
    element.addEventListener('mouseup', handleMouseUp);
    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('click', handleClick, true); // Capture phase to prevent clicks

    return () => {
      if (element) {
        element.removeEventListener('mousedown', handleMouseDown);
        element.removeEventListener('mouseleave', handleMouseLeave);
        element.removeEventListener('mouseup', handleMouseUp);
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('click', handleClick, true);
      }
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return scrollRef;
};

/**
 * Hook for managing multiple horizontal scroll references
 * Used when multiple rows need drag-to-scroll functionality
 */
export const useMultipleHorizontalScroll = (rowCount = 0) => {
  const scrollRefs = useRef({});

  const setScrollRef = useCallback((key) => (element) => {
    if (element) {
      scrollRefs.current[key] = element;
      
      let isDown = false;
      let startX;
      let scrollLeft;
      let lastX;
      let velocity = 0;
      let momentum = 0;
      let animationId = null;
      let hasMoved = false;
      let dragThreshold = 8;
      let clickPrevented = false;

      const handleMouseDown = (e) => {
        if (e.target.tagName === 'BUTTON' || e.target.closest('button') || e.target.closest('[role="button"]')) {
          return;
        }
        
        isDown = true;
        hasMoved = false;
        clickPrevented = false;
        element.classList.add('cursor-grabbing');
        startX = e.pageX - element.offsetLeft;
        lastX = startX;
        scrollLeft = element.scrollLeft;
        velocity = 0;
        momentum = 0;
        
        if (animationId) {
          cancelAnimationFrame(animationId);
          animationId = null;
        }
      };

      const handleMouseLeave = () => {
        if (isDown) {
          applyMomentum();
        }
        isDown = false;
        element.classList.remove('cursor-grabbing');
      };

      const handleMouseUp = () => {
        if (isDown) {
          applyMomentum();
        }
        isDown = false;
        element.classList.remove('cursor-grabbing');
        
        setTimeout(() => {
          clickPrevented = false;
        }, 50);
      };

      const handleMouseMove = (e) => {
        if (!isDown) return;
        e.preventDefault();
        
        const x = e.pageX - element.offsetLeft;
        const deltaX = x - startX;
        
        if (Math.abs(deltaX) > dragThreshold) {
          hasMoved = true;
          clickPrevented = true;
        }
        
        velocity = x - lastX;
        lastX = x;
        
        const walk = deltaX * 1.2;
        element.scrollLeft = scrollLeft - walk;
      };

      const applyMomentum = () => {
        if (Math.abs(velocity) < 1) return;
        
        momentum = velocity * 2;
        
        // Much smaller cap for maximum momentum
        const maxMomentum = 400; // Roughly 2-3 movie cards worth (was 800)
        momentum = Math.sign(momentum) * Math.min(Math.abs(momentum), maxMomentum);
        
        const momentumAnimation = () => {
          if (Math.abs(momentum) < 0.5) {
            momentum = 0;
            return;
          }
          
          element.scrollLeft -= momentum;
          momentum *= 0.80; // Even higher friction (was 0.85)
          
          animationId = requestAnimationFrame(momentumAnimation);
        };
        
        animationId = requestAnimationFrame(momentumAnimation);
      };

      const handleClick = (e) => {
        if (clickPrevented && hasMoved && e.target.closest('.media-card')) {
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
        }
      };

      // Add event listeners
      element.addEventListener('mousedown', handleMouseDown);
      element.addEventListener('mouseleave', handleMouseLeave);
      element.addEventListener('mouseup', handleMouseUp);
      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('click', handleClick, true);

      // Store cleanup function
      const cleanup = () => {
        element.removeEventListener('mousedown', handleMouseDown);
        element.removeEventListener('mouseleave', handleMouseLeave);
        element.removeEventListener('mouseup', handleMouseUp);
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('click', handleClick, true);
        if (animationId) {
          cancelAnimationFrame(animationId);
        }
      };
      element._scrollCleanup = cleanup;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      Object.values(scrollRefs.current).forEach(element => {
        if (element && element._scrollCleanup) {
          element._scrollCleanup();
        }
      });
    };
  }, []);

  return { setScrollRef, scrollRefs: scrollRefs.current };
}; 