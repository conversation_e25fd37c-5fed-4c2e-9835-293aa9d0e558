import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import OptimizedMediaCard from '../components/OptimizedMediaCard';
import ContentDiscovery from '../components/ContentDiscovery';
import ContentInsights from '../components/ContentInsights';
import EnhancedVideoPlayer from '../components/EnhancedVideoPlayer';
import TrailerPlayer from '../components/TrailerPlayer';
import TrailerModal from '../components/TrailerModal';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import { watchlistStorage } from '../utils/watchlistStorage';
import StorageDebug from '../components/StorageDebug';
import { useMultipleHorizontalScroll } from '../hooks/useHorizontalScroll';
import { apiFetch } from '../utils/api';
import HorizontalRow from '../components/HorizontalRow';
import LoadingSpinner from '../components/LoadingSpinner';

// IMPORTANT: This page uses TMDB data for all display elements (posters, titles, info)
// Torrent data is ONLY used for the play button functionality
// - TMDB: Posters, titles, cast, info, trailers
// - Torrents: Only for the left play button to stream content

// eslint-disable-next-line no-unused-vars
const SkeletonCard = () => (
    <div className="bg-black rounded-lg animate-pulse aspect-[2/3]"></div>
);

const SkeletonRow = () => (
  <div className="mb-10">
    <div className="h-6 w-32 bg-gray-700 rounded mb-4 animate-pulse"></div>
    <div className="flex gap-4 overflow-x-auto scrollbar-hide">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="w-55 h-80 bg-black rounded-lg animate-pulse"></div>
      ))}
    </div>
  </div>
);

const LoadingScreen = () => (
  <div className="p-4 sm:p-6 space-y-12">
    {Array.from({ length: 5 }).map((_, i) => <SkeletonRow key={i} />)}
  </div>
);

const ErrorDisplay = ({ error }) => (
    <div className="text-center p-10 bg-gray-900/50 rounded-lg m-6 border border-red-500/30">
        <h2 className="text-2xl font-bold mb-2 text-red-400">Something Went Wrong</h2>
        <p className="text-red-300">Could not load application data. Please try again later.</p>
        <p className="text-xs text-gray-500 mt-4">Error: {error?.message || 'Unknown error'}</p>
    </div>
);

const FeaturedHero = ({ movie, onWatchNow, isPlayingTrailer, trailer, _onStopTrailer }) => {
    if (!movie) return <div className="w-full h-[60vh] bg-black animate-pulse rounded-lg"></div>;
    const backdropUrl = `https://image.tmdb.org/t/p/original${movie.backdrop_path}`;
    
    return (
        <div className="w-full flex justify-center py-8">
            <div className="relative max-w-4xl aspect-video w-full rounded-lg overflow-hidden shadow-2xl bg-black">
                {isPlayingTrailer ? (
                    <div className="absolute inset-0 w-full h-full">
                        <iframe
                            src={`https://www.youtube.com/embed/${trailer.key}?autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&iv_load_policy=3&fs=0&vq=hd1080`}
                            title={`${movie.title} Trailer`}
                            className="absolute inset-0 w-full h-full"
                            frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                        ></iframe>
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent pointer-events-none"></div>
                    </div>
                ) : (
                    <div className="absolute inset-0 w-full h-full">
                        <div
                            className="w-full h-full bg-cover bg-center"
                            style={{ backgroundImage: `url(${backdropUrl})` }}
                        ></div>
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
                    </div>
                )}
                <div className="relative z-10 p-8 max-w-2xl">
                    <h1 className="text-5xl font-black tracking-tighter">{movie.title}</h1>
                    <p className="mt-2 text-gray-300">{movie.overview}</p>
                    <div className="flex gap-4 mt-4">
                        <button 
                            className="w-16 h-16 bg-white/10 backdrop-blur-sm text-white rounded-full flex items-center justify-center transform hover:scale-110 hover:bg-white/20 transition-all shadow-lg"
                            onClick={() => onWatchNow && onWatchNow(movie)}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="ml-1">
                                <path d="M8 5v14l11-7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

// eslint-disable-next-line no-unused-vars
const NETFLIX_GENRES = [
  { id: 28, name: 'Action' },
  { id: 35, name: 'Comedy' },
  { id: 27, name: 'Horror' },
  { id: 878, name: 'Sci-Fi' },
  { id: 16, name: 'Animation' },
  { id: 18, name: 'Drama' },
  { id: 10749, name: 'Romance' },
  { id: 12, name: 'Adventure' },
];

const Dashboard = () => {
  const { user } = useAuth();
  const { profile: currentProfile, isProfileLoaded } = useProfile();
  const [trendingMovies, setTrendingMovies] = useState([]);
  const [appState, setAppState] = useState('loading');
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [totalPages, setTotalPages] = useState(0);
  const [videoPlayer, setVideoPlayer] = useState({ isOpen: false, magnetLink: null, title: null });
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, trailer: null, content: null });
  const [heroTrailer, setHeroTrailer] = useState({ isPlaying: false, movie: null, trailer: null });
  const [hasPlayedHeroTrailer, setHasPlayedHeroTrailer] = useState(false);
  const [genres, setGenres] = useState([]);
  const [genreMovies, setGenreMovies] = useState({}); // { genreId: [movies] }
  const [loadingGenres, setLoadingGenres] = useState(true);
  const [loadingRows, setLoadingRows] = useState({}); // { genreId: true/false }
  const [genrePages, setGenrePages] = useState({}); // { genreId: page }
  const [genreHasMore, setGenreHasMore] = useState({}); // { genreId: hasMore }
  const [genreLoading, setGenreLoading] = useState({}); // { genreId: loading }
  const [watchlist, setWatchlist] = useState([]);
  const [topRatedMovies, setTopRatedMovies] = useState([]);
  const [topRatedLoading, setTopRatedLoading] = useState(true);
  const [topRatedPage, setTopRatedPage] = useState(1);
  const [topRatedHasMore, setTopRatedHasMore] = useState(true);
  const [topRatedLoadingMore, setTopRatedLoadingMore] = useState(false);
  const [continueWatching, setContinueWatching] = useState([]);
  const [continueWatchingLoading, setContinueWatchingLoading] = useState(true);
  const [showInsights, setShowInsights] = useState(false);
  const [showDiscovery, setShowDiscovery] = useState(true);
  const navigate = useNavigate();

  // Listen for toggle events from header
  useEffect(() => {
    const handleToggleDiscovery = (event) => {
      setShowDiscovery(event.detail);
    };

    const handleToggleInsights = (event) => {
      setShowInsights(event.detail);
    };

    window.addEventListener('toggleDiscovery', handleToggleDiscovery);
    window.addEventListener('toggleInsights', handleToggleInsights);

    // Initialize from localStorage
    const savedDiscovery = localStorage.getItem('showDiscovery');
    const savedInsights = localStorage.getItem('showInsights');
    
    if (savedDiscovery !== null) {
      setShowDiscovery(savedDiscovery !== 'false');
    }
    if (savedInsights !== null) {
      setShowInsights(savedInsights === 'true');
    }

    return () => {
      window.removeEventListener('toggleDiscovery', handleToggleDiscovery);
      window.removeEventListener('toggleInsights', handleToggleInsights);
    };
  }, []);
  // eslint-disable-next-line no-unused-vars
  const location = useLocation();
  const observer = useRef();
  // eslint-disable-next-line no-unused-vars
  const loadingRef = useRef();
  const genreRowRefs = useRef({});
  const lastCardRefs = useRef({});
  const topRatedRowRef = useRef();
  const lastTopRatedCardRef = useRef();
  
  // Enable horizontal scrolling with mouse wheel for all rows
  const { setScrollRef } = useMultipleHorizontalScroll([genres.length, topRatedMovies.length]);

  // Scroll position restoration
  useEffect(() => {
    // Scroll to top on initial load
    window.scrollTo(0, 0);
    
    // Restore scroll position when returning to the page
    const savedScrollPosition = sessionStorage.getItem('dashboard_scroll_position');
    if (savedScrollPosition && appState === 'ready') {
      setTimeout(() => {
        window.scrollTo(0, parseInt(savedScrollPosition));
      }, 100);
    }

    // Save scroll position when navigating away
    const handleBeforeUnload = () => {
      sessionStorage.setItem('dashboard_scroll_position', window.scrollY.toString());
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        sessionStorage.setItem('dashboard_scroll_position', window.scrollY.toString());
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [loadingGenres]);

  // Intersection Observer for infinite scrolling
  const lastMovieElementRef = useCallback(node => {
    if (isLoadingMore || loadingGenres) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting) {
        loadMoreMovies();
      }
    });
    if (node) observer.current.observe(node);
  }, [isLoadingMore, loadingGenres]);

  const fetchTrending = async (page = 1, append = false) => {
    try {
      // Check for cached data first (only for page 1, non-append requests)
      if (page === 1 && !append) {
        const cachedTrendingMovies = localStorage.getItem('torvie_cached_trending_movies');
        if (cachedTrendingMovies) {
          try {
            const cachedData = JSON.parse(cachedTrendingMovies);
            if (cachedData && cachedData.length > 0) {
              console.log(`✅ Dashboard: Using cached trending movies (${cachedData.length} items)`);
              setTrendingMovies(cachedData);
              setCurrentPage(1);
              setTotalPages(20); // Assume reasonable total pages
              setHasMore(true);
              setAppState('ready');
              return; // Skip API call if we have cached data
            }
          } catch (error) {
            console.warn('Failed to parse cached trending movies, fetching fresh data:', error);
          }
        }
      }

      const response = await apiFetch(`/api/trending/movies?page=${page}`);
      if (!response.ok) throw new Error("Network response was not ok");
      const data = await response.json();
      const moviesArray = data.results && Array.isArray(data.results) ? data.results : data;
      if (Array.isArray(moviesArray)) {
        if (append) {
          setTrendingMovies(prev => {
            const combined = [...prev, ...moviesArray];
            localStorage.setItem('torvie_trending_movies', JSON.stringify(combined));
            return combined;
          });
        } else {
          setTrendingMovies(moviesArray);
          localStorage.setItem('torvie_trending_movies', JSON.stringify(moviesArray));
          // Also update the main cache
          localStorage.setItem('torvie_cached_trending_movies', JSON.stringify(moviesArray));
        }
        setCurrentPage(data.page || page);
        setTotalPages(data.total_pages || 1);
        setHasMore((data.page || page) < (data.total_pages || 1));
        setAppState('ready');
      } else {
        throw new Error("Invalid data format from server");
      }
    } catch (e) {
      setError(e);
      setAppState('error');
    }
  };

  const loadMoreMovies = async () => {
    if (isLoadingMore || !hasMore) return;
    setIsLoadingMore(true);
    const nextPage = currentPage + 1;
    try {
      await fetchTrending(nextPage, true);
    } catch (error) {
      console.error('Error loading more movies:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    const initialLoad = async () => {
      // Check if we have cached data to skip loading screen
      const cachedTrendingMovies = localStorage.getItem('torvie_cached_trending_movies');
      if (cachedTrendingMovies) {
        try {
          const cachedData = JSON.parse(cachedTrendingMovies);
          if (cachedData && cachedData.length > 0) {
            console.log('✅ Dashboard: Skipping loading screen, using cached data immediately');
            await fetchTrending(1, false);
            return;
          }
        } catch (error) {
          console.warn('Failed to parse cached trending movies:', error);
        }
      }
      
      // Only show loading screen if no cached data
      setAppState('loading');
      await new Promise(resolve => setTimeout(resolve, 800)); // Shorter delay if no cache
      await fetchTrending(1, false);
    };
    initialLoad();
  }, []);

  // Auto-play trailer for featured movie
  useEffect(() => {
    if (trendingMovies.length > 0 && !heroTrailer.isPlaying && appState === 'ready' && !hasPlayedHeroTrailer) {
      // Check if user is returning to the page (has saved scroll position)
      const savedScrollPosition = sessionStorage.getItem('dashboard_scroll_position');
      const isReturningUser = savedScrollPosition && parseInt(savedScrollPosition) > 0;
      
      // Only auto-play if this is a fresh page load (not returning from details)
      if (!isReturningUser) {
        const featuredMovie = trendingMovies[0];
        setTimeout(() => {
          handleHeroTrailer(featuredMovie);
          setHasPlayedHeroTrailer(true);
        }, 1000);
      } else {
        // Mark as played so it doesn't auto-play when returning
        setHasPlayedHeroTrailer(true);
      }
    }
  }, [trendingMovies, appState, hasPlayedHeroTrailer]);

  // Handle scroll to stop trailer
  useEffect(() => {
    const handleScroll = () => {
      if (heroTrailer.isPlaying && window.scrollY > 100) {
        stopHeroTrailer();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [heroTrailer.isPlaying]);

  const handleMovieClick = (movie) => {
    // Save current scroll position before navigating
    sessionStorage.setItem('dashboard_scroll_position', window.scrollY.toString());
    navigate(`/movie/${movie.id}`);
  };

  const handleWatchNow = (magnetLink, title) => {
    setVideoPlayer({ isOpen: true, magnetLink, title });
  };

  const closeVideoPlayer = () => {
    setVideoPlayer({ isOpen: false, magnetLink: null, title: null });
  };

  const handleHeroTrailer = async (movie) => {
    try {
      console.log('Fetching trailer for:', movie.title);
      // Fetch trailer data for the movie
              const response = await apiFetch(`/api/movies/${movie.id}/videos`);
      const data = await response.json();
      
      // Find the best trailer - prioritize official cinematic trailers
      const trailers = data.results?.filter(video => 
        video.site === 'YouTube' && 
        (video.type === 'Trailer' || video.type === 'Teaser') &&
        // Filter out portrait/vertical videos (like YouTube Shorts)
        (!video.name?.toLowerCase().includes('short') && 
         !video.name?.toLowerCase().includes('vertical') &&
         !video.name?.toLowerCase().includes('portrait'))
      ) || [];
      
      console.log('Found trailers:', trailers.length);
      
      // Sort by priority: Official Trailer first, then Teaser, then others
      const sortedTrailers = trailers.sort((a, b) => {
        const aPriority = a.type === 'Trailer' ? 1 : 2;
        const bPriority = b.type === 'Trailer' ? 1 : 2;
        
        // If same type, prefer "Official" in the name
        if (aPriority === bPriority) {
          const aIsOfficial = a.name?.toLowerCase().includes('official');
          const bIsOfficial = b.name?.toLowerCase().includes('official');
          if (aIsOfficial && !bIsOfficial) return -1;
          if (!aIsOfficial && bIsOfficial) return 1;
        }
        
        return aPriority - bPriority;
      });
      
      const trailer = sortedTrailers[0];
      
      if (trailer) {
        console.log('Playing trailer:', trailer.name);
        setHeroTrailer({ isPlaying: true, movie, trailer });
      } else {
        console.log('No trailer available for:', movie.title);
      }
    } catch (error) {
      console.error('Error fetching trailer:', error);
    }
  };

  const stopHeroTrailer = () => {
    setHeroTrailer({ isPlaying: false, movie: null, trailer: null });
  };

  // Add this function to search for the best torrent and play it with fallback support
  const handlePlayMovie = async (movie) => {
    if (!movie) {
      return;
    }

    try {
      console.log(`🎬 Searching torrents for: ${movie.title}`);
      const response = await apiFetch(`/api/search?query=${encodeURIComponent(movie.title)}&type=movie`);
      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        console.log(`🎬 Found ${data.results.length} torrents for ${movie.title}`);
        
        // Filter and sort torrents - prioritize those with good seeders
        const validTorrents = data.results
          .filter(torrent => torrent.magnetLink && torrent.seeders > 0)
          .sort((a, b) => (b.seeders || 0) - (a.seeders || 0))
          .slice(0, 5); // Keep top 5 for fallback
        
        if (validTorrents.length === 0) {
          console.error(`🎬 No valid torrents with seeders found for ${movie.title}`);
          return;
        }
        
        console.log(`🎬 Selected top ${validTorrents.length} torrents for fallback support`);
        
        // Start with the best torrent, VideoPlayer will handle fallbacks
        const bestTorrent = validTorrents[0];
        console.log(`🎬 Starting with best torrent: ${bestTorrent.title} (${bestTorrent.seeders} seeders)`);
        
        setVideoPlayer({ 
          isOpen: true, 
          magnetLink: bestTorrent.magnetLink, 
          title: movie.title,
          fallbackTorrents: validTorrents.slice(1), // Pass remaining torrents for fallback
          contentId: `movie_${movie.id}`, // Make more unique and descriptive
          contentType: 'movie',
          posterUrl: movie.poster_path ? `https://image.tmdb.org/t/p/w500${movie.poster_path}` : null
        });
      } else {
        console.log(`🎬 No torrents found for: ${movie.title}`);
      }
    } catch (error) {
      console.error('🎬 Error searching for torrents:', error);
    }
  };

  const handleTrailer = async (media) => {
    if (!media) return;
    try {
      const endpoint = media.media_type === 'movie'
        ? `/api/movies/${media.id}/videos`
        : `/api/tv/${media.id}/videos`;
      const response = await apiFetch(endpoint);
      const data = await response.json();
      const trailers = data.results?.filter(video =>
        video.site === 'YouTube' &&
        (video.type === 'Trailer' || video.type === 'Teaser')
      ) || [];
      if (trailers.length > 0) {
        setTrailerModal({ isOpen: true, trailer: trailers[0], content: media });
      } else {
        alert('No trailer available for this title.');
      }
    } catch (error) {
      alert('Failed to load trailer.');
    }
  };
  const closeTrailerModal = () => setTrailerModal({ isOpen: false, trailer: null, content: null });

  // Callback to attach observer to the last card in each row
  const setLastCardRef = (genreId) => (node) => {
    if (lastCardRefs.current[genreId]) {
      lastCardRefs.current[genreId].disconnect();
    }
    if (node) {
      lastCardRefs.current[genreId] = new window.IntersectionObserver(entries => {
        if (entries[0].isIntersecting && !genreLoading[genreId] && genreHasMore[genreId] !== false) {
          loadMoreGenreMovies(genreId);
        }
      }, { root: genreRowRefs.current[genreId], threshold: 0.01, rootMargin: '500px' });
      lastCardRefs.current[genreId].observe(node);
    }
  };

  // Callback to attach observer to the last top rated card
  const setLastTopRatedCardRef = (node) => {
    if (lastTopRatedCardRef.current) {
      lastTopRatedCardRef.current.disconnect();
    }
    if (node) {
      lastTopRatedCardRef.current = new window.IntersectionObserver(entries => {
        if (entries[0].isIntersecting && !topRatedLoadingMore && topRatedHasMore) {
          loadMoreTopRatedMovies();
        }
      }, { root: topRatedRowRef.current, threshold: 0.01, rootMargin: '500px' });
      lastTopRatedCardRef.current.observe(node);
    }
  };

  // Fetch movies for a genre
  const loadMoreGenreMovies = async (genreId) => {
    if (genreLoading[genreId] || genreHasMore[genreId] === false) {
      return;
    }
    setGenreLoading(prev => ({ ...prev, [genreId]: true }));
    const nextPage = (genrePages[genreId] || 1) + 1;
    try {
      const res = await apiFetch(`/api/media/genre-movies/${genreId}?page=${nextPage}`);
      const data = await res.json();
      const apiMovies = data.movies || [];
      setGenreMovies(prev => {
        const existingIds = new Set((prev[genreId] || []).map(m => m.id));
        const newUnique = apiMovies.filter(m => !existingIds.has(m.id));
        return {
          ...prev,
          [genreId]: [...(prev[genreId] || []), ...newUnique]
        };
      });
      setGenrePages(prev => ({ ...prev, [genreId]: nextPage }));
      setGenreHasMore(prev => ({ ...prev, [genreId]: apiMovies.length > 0 }));
    } catch (error) {
      setGenreHasMore(prev => ({ ...prev, [genreId]: false }));
    } finally {
      setGenreLoading(prev => ({ ...prev, [genreId]: false }));
    }
  };

  // Fetch top rated movies
  const loadTopRatedMovies = async (page = 1, append = false) => {
    if (topRatedLoadingMore && append) return;
    
    // Check for cached data first (only for page 1, non-append requests)
    if (page === 1 && !append) {
      const cachedTopRatedMovies = localStorage.getItem('torvie_cached_top_rated_movies');
      if (cachedTopRatedMovies) {
        try {
          const cachedData = JSON.parse(cachedTopRatedMovies);
          if (cachedData && cachedData.length > 0) {
            console.log(`✅ Dashboard: Using cached top rated movies (${cachedData.length} items)`);
            setTopRatedMovies(cachedData);
            setTopRatedPage(1);
            setTopRatedHasMore(true);
            setTopRatedLoading(false);
            return; // Skip API call if we have cached data
          }
        } catch (error) {
          console.warn('Failed to parse cached top rated movies, fetching fresh data:', error);
        }
      }
    }

    if (append) {
      setTopRatedLoadingMore(true);
    } else {
      setTopRatedLoading(true);
    }
    
    try {
      const response = await apiFetch(`/api/top-rated/movies?page=${page}`);
      const data = await response.json();
      const movies = data.results || [];
      
      if (append) {
        setTopRatedMovies(prev => {
          const existingIds = new Set(prev.map(m => m.id));
          const newUnique = movies.filter(m => !existingIds.has(m.id));
          return [...prev, ...newUnique];
        });
      } else {
        setTopRatedMovies(movies);
        // Also update the main cache for fresh data
        localStorage.setItem('torvie_cached_top_rated_movies', JSON.stringify(movies));
      }
      
      setTopRatedPage(page);
      setTopRatedHasMore(movies.length > 0 && page < (data.total_pages || 1));
    } catch (error) {
      console.error('Error loading top rated movies:', error);
      setTopRatedHasMore(false);
    } finally {
      if (append) {
        setTopRatedLoadingMore(false);
      } else {
        setTopRatedLoading(false);
      }
    }
  };

  const loadMoreTopRatedMovies = async () => {
    if (topRatedLoadingMore || !topRatedHasMore) return;
    await loadTopRatedMovies(topRatedPage + 1, true);
  };

  // Fetch genres and initial movies (with caching optimization)
  useEffect(() => {
    const fetchGenresAndMovies = async () => {
      try {
        // First, try to load cached genres from loading screen
        const cachedGenres = localStorage.getItem('torvie_cached_movie_genres');
        let genresToUse = null;
        
        if (cachedGenres) {
          try {
            const parsedGenres = JSON.parse(cachedGenres);
            if (Array.isArray(parsedGenres) && parsedGenres.length > 0) {
              console.log('📋 Dashboard: Using cached movie genres from loading screen');
              genresToUse = parsedGenres;
              setGenres(genresToUse);
              setLoadingGenres(false);
            }
          } catch (error) {
            console.warn('📋 Dashboard: Invalid cached movie genres, fetching fresh:', error);
          }
        }
        
        // If no valid cached genres, fetch from API
        if (!genresToUse) {
          console.log('📋 Dashboard: Fetching genres from API');
          const response = await apiFetch('/api/genres');
          const data = await response.json();
          if (Array.isArray(data.genres)) {
            genresToUse = data.genres;
            setGenres(genresToUse);
            // Cache for future use
            localStorage.setItem('torvie_cached_movie_genres', JSON.stringify(genresToUse));
          } else {
            setGenres([]);
            setLoadingGenres(false);
            return;
          }
        }
        
        // Load initial movies for every genre
        if (genresToUse && Array.isArray(genresToUse)) {
          genresToUse.forEach(async (genre) => {
            setLoadingRows(prev => ({ ...prev, [genre.id]: true }));
            try {
              const res = await apiFetch(`/api/media/genre-movies/${genre.id}?page=1`);
              const genreData = await res.json();
              const apiMovies = genreData.movies || [];
              setGenreMovies(prev => {
                const existingIds = new Set((prev[genre.id] || []).map(m => m.id));
                const newUnique = apiMovies.filter(m => !existingIds.has(m.id));
                return {
                  ...prev,
                  [genre.id]: [...(prev[genre.id] || []), ...newUnique]
                };
              });
              setGenreHasMore(prev => ({ ...prev, [genre.id]: apiMovies.length > 0 }));
            } catch {
              setGenreMovies(prev => ({ ...prev, [genre.id]: [] }));
              setGenreHasMore(prev => ({ ...prev, [genre.id]: false }));
            } finally {
              setLoadingRows(prev => ({ ...prev, [genre.id]: false }));
            }
          });
        }
        
        setLoadingGenres(false);
      } catch (error) {
        console.error('📋 Dashboard: Error in fetchGenresAndMovies:', error);
        setGenres([]);
        setLoadingGenres(false);
      }
    };
    fetchGenresAndMovies();
  }, []);

  // Fetch top rated movies on component mount
  useEffect(() => {
    loadTopRatedMovies(1, false);
  }, []);

  const loadContinueWatching = async () => {
    if (!isProfileLoaded) return;
    setContinueWatchingLoading(true);
    if (user && currentProfile && currentProfile.id) {
      const watchProgressStorage = (await import('../utils/watchProgressStorage')).watchProgressStorage;
      const continueItems = await watchProgressStorage.getContinueWatching(user.id, currentProfile.id, 100);
      setContinueWatching(continueItems);
    } else if (!user) {
      setContinueWatching([]);
    }
    setContinueWatchingLoading(false);
  };

  useEffect(() => {
    loadContinueWatching();
  }, [user, currentProfile, isProfileLoaded]);

  // NEW: Auto-refresh continue watching when returning to dashboard
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && user && currentProfile && currentProfile.id && isProfileLoaded) {
        console.log(`📺 Dashboard visible again, refreshing continue watching for profile "${currentProfile.name}"...`);
        // Small delay to ensure any progress from video player has been saved
        setTimeout(async () => {
          const watchProgressStorage = (await import('../utils/watchProgressStorage')).watchProgressStorage;
          const continueItems = await watchProgressStorage.getContinueWatching(user.id, currentProfile.id, 100);
          console.log(`📺 Dashboard: Refreshed ${continueItems.length} continue watching items for profile "${currentProfile.name}"`);
          setContinueWatching(continueItems);
        }, 1000);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user, currentProfile, isProfileLoaded]);

  // NEW: Handle continue watching click
  const handleContinueWatching = async (item) => {
    console.log(`📺 Continue watching: ${item.title}`);
    
    try {
      // Search for torrents for this content
              const response = await apiFetch(`/api/search?query=${encodeURIComponent(item.title)}&type=${item.contentType}`);
      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        const validTorrents = data.results
          .filter(torrent => torrent.magnetLink && torrent.seeders > 0)
          .sort((a, b) => (b.seeders || 0) - (a.seeders || 0))
          .slice(0, 5);
        
        if (validTorrents.length > 0) {
          const bestTorrent = validTorrents[0];
          
          setVideoPlayer({ 
            isOpen: true, 
            magnetLink: bestTorrent.magnetLink, 
            title: item.title,
            fallbackTorrents: validTorrents.slice(1),
            contentId: item.contentId,
            contentType: item.contentType,
            posterUrl: item.poster,
            episodeInfo: item.episodeInfo
          });
        }
      }
    } catch (error) {
      console.error('📺 Error loading continue watching item:', error);
    }
  };

  // NEW: Handle removing item from Continue Watching
  const handleRemoveFromContinueWatching = async (item, event) => {
    // Prevent triggering the play action when clicking X
    event.stopPropagation();
    event.preventDefault();
    
    if (!user || !currentProfile) return;
    
    console.log(`🗑️ Removing "${item.title}" from Continue Watching`);
    
    try {
      const watchProgressStorage = (await import('../utils/watchProgressStorage')).watchProgressStorage;
      const success = await watchProgressStorage.removeProgress(user.id, currentProfile.id, item.contentId);
      
      if (success) {
        // Remove from local state immediately for instant UI feedback
        setContinueWatching(prev => prev.filter(continueItem => continueItem.contentId !== item.contentId));
        console.log(`✅ Removed "${item.title}" from Continue Watching`);
      } else {
        console.error(`❌ Failed to remove "${item.title}" from Continue Watching`);
      }
    } catch (error) {
      console.error(`❌ Error removing "${item.title}" from Continue Watching:`, error);
    }
  };

  const isInWatchlist = (id) => watchlist.some(item => String(item.id) === String(id));

  const toggleWatchlist = async (movie) => {
    if (!user || !currentProfile || !currentProfile.id) {
      console.log('❌ Dashboard: No user or current profile');
      return;
    }
    const exists = watchlist.some(item => String(item.id) === String(movie.id));
    let success;
    if (exists) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, movie.id, movie.media_type || 'movie');
      if (success) {
        setWatchlist(prev => prev.filter(item => String(item.id) !== String(movie.id)));
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, { ...movie, media_type: movie.media_type || 'movie' });
      if (success) {
        setWatchlist(prev => [...prev, movie]);
      }
    }
  };

  const renderContent = () => {
    if (appState === 'loading') {
      return <LoadingScreen />;
    }
    if (appState === 'error') {
      return <ErrorDisplay error={error}/>;
    }
    // Debug print
    console.log('Genres:', genres, 'GenreMovies:', genreMovies);
    return (
      <div className="min-h-screen px-6 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Always show the hero section at the top */}
          <div className="mb-8">
            <FeaturedHero movie={trendingMovies[0]} onWatchNow={handlePlayMovie} isPlayingTrailer={heroTrailer.isPlaying} trailer={heroTrailer.trailer} _onStopTrailer={stopHeroTrailer} />
            
            {/* Debug component to help diagnose profile issues */}
            <StorageDebug />
          </div>
        </div>
        
        {/* Netflix-style Genre Rows - Full Width */}
        <div className="space-y-8">
          {/* Continue Watching Section */}
          {!continueWatchingLoading && continueWatching.length > 0 && (
            <HorizontalRow
              items={continueWatching}
              renderItem={(item, idx) => {
                const mediaItem = {
                  id: item.contentId,
                  title: item.title,
                  poster_path: item.poster ? item.poster.replace('https://image.tmdb.org/t/p/w500', '') : null,
                  media_type: item.contentType,
                  _progressOverlay: {
                    percentage: Math.round(item.watchedPercentage),
                    timeLeft: (() => {
                      const remaining = Math.max(0, item.duration - item.currentTime);
                      const minutes = Math.floor(remaining / 60);
                      return minutes > 0 ? `${minutes}m left` : 'Almost done';
                    })()
                  }
                };
                return (
                  <OptimizedMediaCard
                    item={mediaItem}
                    isInWatchlist={false}
                    toggleWatchlist={() => {}}
                    onClick={() => handleContinueWatching(item)}
                    onPlay={(_media) => handleContinueWatching(item)}
                    onTrailer={() => {}}
                    className="media-card"
                    hideWatchlistButton={true}
                    hideMoreInfoButton={true}
                  />
                );
              }}
              loadMore={loadContinueWatching}
              loading={continueWatchingLoading}
              title="Continue Watching"
              rowId="continue-watching-row"
            />
          )}

          {/* Top Rated Movies Section */}
          <HorizontalRow
            items={topRatedMovies}
            renderItem={(movie, idx) => (
              <OptimizedMediaCard
                item={movie}
                isInWatchlist={isInWatchlist(movie.id)}
                toggleWatchlist={() => toggleWatchlist(movie)}
                onClick={() => handleMovieClick(movie)}
                onPlay={(_media) => handlePlayMovie(_media)}
                onTrailer={(_media) => handleTrailer(_media)}
                className="media-card"
              />
            )}
            loadMore={loadMoreTopRatedMovies}
            loading={topRatedLoadingMore}
            title="Top Rated"
            rowId="top-rated-row"
          />

          {/* Genre Rows */}
          {genres.map((genre) => (
            <HorizontalRow
              key={genre.id}
              items={genreMovies[genre.id] || []}
              renderItem={(movie, idx) => (
                <OptimizedMediaCard
                  item={movie}
                  isInWatchlist={isInWatchlist(movie.id)}
                  toggleWatchlist={() => toggleWatchlist(movie)}
                  onClick={() => handleMovieClick(movie)}
                  onPlay={(_media) => handlePlayMovie(_media)}
                  onTrailer={(_media) => handleTrailer(_media)}
                  className="media-card"
                />
              )}
              loadMore={() => loadMoreGenreMovies(genre.id)}
              loading={genreLoading[genre.id]}
              title={genre.name}
              rowId={`genre-row-${genre.id}`}
            />
          ))}

          {/* Content Discovery Section */}
          {showDiscovery && (
            <div className="max-w-7xl mx-auto">
              <ContentDiscovery
                showTitle={true}
                maxItems={12}
                onItemClick={(item) => {
                  if (item.media_type === 'movie') {
                    handleMovieClick(item);
                  } else {
                    navigate(`/show/${item.id}`);
                  }
                }}
                onPlay={(item) => {
                  if (item.media_type === 'movie') {
                    handlePlayMovie(item);
                  } else {
                    // Handle TV show play - navigate to show details for now
                    navigate(`/show/${item.id}`);
                  }
                }}
                onTrailer={(item) => {
                  if (item.media_type === 'movie') {
                    handleTrailer(item);
                  } else {
                    // Handle TV show trailer - navigate to show details for now
                    navigate(`/show/${item.id}`);
                  }
                }}
                className="mb-12"
              />
            </div>
          )}

          {/* Content Insights Section */}
          {showInsights && (
            <div className="max-w-7xl mx-auto">
              <ContentInsights
                showTitle={true}
                showCharts={true}
                className="mb-12"
              />
            </div>
          )}
        </div>
        
        {/* Video Player */}
        {videoPlayer.isOpen && (
                  <EnhancedVideoPlayer
          magnetLink={videoPlayer.magnetLink}
          title={videoPlayer.title}
          onClose={closeVideoPlayer}
          fallbackTorrents={videoPlayer.fallbackTorrents || []}
          contentId={videoPlayer.contentId}
          contentType={videoPlayer.contentType || "movie"}
          posterUrl={videoPlayer.posterUrl}
          episodeInfo={videoPlayer.episodeInfo}
        />
        )}
        {/* Trailer Modal */}
        <TrailerModal isOpen={trailerModal.isOpen} onClose={closeTrailerModal} trailer={trailerModal.trailer} content={trailerModal.content} />
      </div>
    );
  };

  return renderContent();
};

export default Dashboard;

 