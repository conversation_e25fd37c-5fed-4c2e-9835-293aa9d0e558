import axios from 'axios';
import NodeCache from 'node-cache';

// Cache results for 10 minutes
const cache = new NodeCache({ stdTTL: 600 });

const IPTV_M3U_URL = 'https://iptv-org.github.io/iptv/index.m3u';

// Helper: Parse M3U playlist text into channel objects
function parseM3U(m3uText) {
  const lines = m3uText.split('\n');
  const channels = [];
  let current = {};
  for (let line of lines) {
    line = line.trim();
    if (line.startsWith('#EXTINF')) {
      // Example: #EXTINF:-1 tvg-id="CNN.us" tvg-name="CNN" tvg-logo="https://logo.com/cnn.png" group-title="News",CNN
      const nameMatch = line.match(/tvg-name="([^"]+)"/);
      const logoMatch = line.match(/tvg-logo="([^"]+)"/);
      const countryMatch = line.match(/tvg-country="([^"]+)"/);
      const langMatch = line.match(/tvg-language="([^"]+)"/);
      const groupMatch = line.match(/group-title="([^"]+)"/);
      // Fallback: if tvg-name missing, use text after last comma
      let name = nameMatch ? nameMatch[1] : '';
      if (!name) {
        const commaIdx = line.lastIndexOf(',');
        if (commaIdx !== -1 && commaIdx < line.length - 1) {
          name = line.slice(commaIdx + 1).trim();
        }
      }
      const logo = logoMatch ? logoMatch[1] : '';
      const country = countryMatch ? countryMatch[1] : '';
      const language = langMatch ? langMatch[1] : '';
      const group = groupMatch ? groupMatch[1] : '';
      current = { name, logo, country, language, group };
    } else if (line && !line.startsWith('#')) {
      // This is the stream URL
      if (line.endsWith('.m3u8') || line.endsWith('.mpd')) {
        channels.push({ ...current, url: line });
      }
    }
  }
  return channels;
}

// Main function: fetch and return live TV channels
async function getLiveTvChannels() {
  console.log('🔴 Live TV Scraper: Starting to fetch channels...');
  
  // Clear cache for debugging
  cache.del('channels');
  
  // Check cache first
  const cached = cache.get('channels');
  if (cached) {
    console.log('🔴 Live TV Scraper: Returning cached channels:', cached.length);
    return cached;
  }
  try {
    console.log('🔴 Live TV Scraper: Fetching from IPTV URL...');
    const { data } = await axios.get(IPTV_M3U_URL, { timeout: 15000 });
    console.log('🔴 Live TV Scraper: Raw M3U data length:', data.length);
    let channels = parseM3U(data);
    const uniqueGroups = [...new Set(channels.map(ch => ch.group).filter(Boolean))];
    console.log('🔴 Live TV Scraper: Unique group titles (sample):', uniqueGroups.slice(0, 20));
    console.log('🔴 Live TV Scraper: Parsed channels before filtering:', channels.length);
    
    // US local detection
    const US_NETWORKS = [
      'CBS', 'NBC', 'ABC', 'FOX', 'PBS', 'CW', 'ION', 'MyNetworkTV', 'Telemundo', 'Univision', 'MeTV', 'Bounce', 'Cozi', 'Antenna', 'TBN', 'QVC', 'HSN', 'C-SPAN', 'Court TV', 'NewsNation', 'Comet', 'Laff', 'GetTV', 'Circle', 'Dabl', 'This TV', 'Stadium', 'Scripps', 'Scripps News', 'CourtTV', 'Court TV Mystery', 'Grit', 'Heroes & Icons', 'Start TV', 'Movies!', 'Decades', 'Buzzr', 'Retro TV', 'America One', 'Azteca', 'Estrella', 'UniMás', 'Unimas', 'TeleXitos', 'UniMás', 'Univision', 'Telemundo', 'Estrella', 'Azteca', 'Telemundo', 'Univision', 'Unimas', 'TeleXitos', 'Estrella', 'Azteca', 'America One', 'Retro TV', 'Buzzr', 'Decades', 'Movies!', 'Start TV', 'Heroes & Icons', 'Grit', 'Court TV Mystery', 'Court TV', 'Scripps News', 'Scripps', 'Stadium', 'This TV', 'Dabl', 'Circle', 'GetTV', 'Laff', 'Comet', 'NewsNation', 'CourtTV', 'HSN', 'QVC', 'TBN', 'Antenna', 'Cozi', 'Bounce', 'MeTV', 'CW', 'PBS', 'FOX', 'ABC', 'NBC', 'CBS'
    ];
    const US_GROUPS = ['United States', 'USA', 'US', 'America', 'Local', 'English'];

    channels = channels.map(ch => {
      const nameUpper = (ch.name || '').toUpperCase();
      const groupUpper = (ch.group || '').toUpperCase();
      const countryUpper = (ch.country || '').toUpperCase();
      // Direct country match
      let isLocalUS = countryUpper === 'US' || countryUpper === 'USA' || countryUpper.includes('UNITED STATES');
      // Group match
      if (!isLocalUS) {
        isLocalUS = US_GROUPS.some(g => groupUpper.includes(g.toUpperCase()));
      }
      // Name/network match
      if (!isLocalUS) {
        isLocalUS = US_NETWORKS.some(n => nameUpper.includes(n.toUpperCase()));
      }
      // Language fallback
      if (!isLocalUS && (ch.language || '').toLowerCase() === 'english') {
        isLocalUS = true;
      }
      // US city/state/call sign patterns
      const US_CITIES = [
        'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose',
        'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis', 'Seattle', 'Denver', 'Washington',
        'Boston', 'El Paso', 'Nashville', 'Detroit', 'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 'Louisville', 'Baltimore',
        'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento', 'Mesa', 'Kansas City', 'Atlanta', 'Long Beach', 'Omaha', 'Raleigh',
        'Miami', 'Oakland', 'Minneapolis', 'Tulsa', 'Wichita', 'New Orleans', 'Arlington', 'Cleveland', 'Bakersfield', 'Tampa', 'Aurora',
        'Honolulu', 'Anaheim', 'Santa Ana', 'Corpus Christi', 'Riverside', 'Lexington', 'Stockton', 'Henderson', 'Saint Paul', 'St. Louis',
        'Cincinnati', 'Pittsburgh', 'Greensboro', 'Anchorage', 'Plano', 'Lincoln', 'Orlando', 'Irvine', 'Newark', 'Toledo', 'Durham',
        'Chula Vista', 'Fort Wayne', 'Jersey City', 'St. Petersburg', 'Laredo', 'Madison', 'Chandler', 'Buffalo', 'Lubbock', 'Scottsdale',
        'Reno', 'Glendale', 'Gilbert', 'Winston–Salem', 'North Las Vegas', 'Norfolk', 'Chesapeake', 'Garland', 'Irving', 'Hialeah',
        'Fremont', 'Boise', 'Richmond', 'Baton Rouge', 'Spokane', 'Des Moines', 'Tacoma', 'San Bernardino', 'Modesto', 'Fontana',
        'Santa Clarita', 'Birmingham', 'Oxnard', 'Fayetteville', 'Moreno Valley', 'Rochester', 'Glendale', 'Huntington Beach', 'Salt Lake City',
        'Grand Rapids', 'Amarillo', 'Yonkers', 'Aurora', 'Montgomery', 'Akron', 'Little Rock', 'Huntsville', 'Augusta', 'Port St. Lucie',
        'Grand Prairie', 'Columbus', 'Tallahassee', 'Overland Park', 'Tempe', 'McKinney', 'Mobile', 'Cape Coral', 'Shreveport', 'Frisco',
        'Knoxville', 'Worcester', 'Brownsville', 'Vancouver', 'Fort Lauderdale', 'Sioux Falls', 'Ontario', 'Chattanooga', 'Providence',
        'Newport News', 'Rancho Cucamonga', 'Santa Rosa', 'Oceanside', 'Salem', 'Elk Grove', 'Garden Grove', 'Pembroke Pines', 'Peoria',
        'Eugene', 'Corona', 'Cary', 'Springfield', 'Fort Collins', 'Jackson', 'Alexandria', 'Hayward', 'Lancaster', 'Lakewood', 'Clarksville',
        'Palmdale', 'Salinas', 'Springfield', 'Hollywood', 'Pasadena', 'Sunnyvale', 'Macon', 'Kansas City', 'Pomona', 'Escondido', 'Killeen',
        'Naperville', 'Joliet', 'Bellevue', 'Rockford', 'Savannah', 'Paterson', 'Torrance', 'Bridgeport', 'McAllen', 'Mesquite', 'Syracuse',
        'Midland', 'Pasadena', 'Murfreesboro', 'Miramar', 'Dayton', 'Fullerton', 'Olathe', 'Orange', 'Thornton', 'Roseville', 'Denton',
        'Waco', 'Surprise', 'Carrollton', 'West Valley City', 'Charleston', 'Warren', 'Hampton', 'Gainesville', 'Visalia', 'Coral Springs',
        'Columbia', 'Cedar Rapids', 'Sterling Heights', 'New Haven', 'Stamford', 'Concord', 'Kent', 'Santa Clara', 'Elizabeth', 'Round Rock',
        'Thousand Oaks', 'Lafayette', 'Topeka', 'Athens', 'Simi Valley', 'Fargo', 'Norman', 'Columbia', 'Abilene', 'Wilmington', 'Hartford',
        'Victorville', 'Pearland', 'Vallejo', 'Ann Arbor', 'Berkeley', 'Allentown', 'Richardson', 'Odessa', 'Arvada', 'Cambridge', 'Sugar Land',
        'Beaumont', 'Lansing', 'Evansville', 'Rochester', 'Independence', 'Fairfield', 'Provo', 'Clearwater', 'College Station', 'West Jordan',
        'Carlsbad', 'El Monte', 'Murrieta', 'Temecula', 'Springfield', 'Palm Bay', 'Costa Mesa', 'Westminster', 'North Charleston', 'Miami Gardens',
        'Manchester', 'High Point', 'Downey', 'Clovis', 'Pompano Beach', 'Pueblo', 'Elgin', 'Lowell', 'Antioch', 'West Palm Beach', 'Peoria',
        'Everett', 'Ventura', 'Centennial', 'Lakeland', 'Gresham', 'Richmond', 'Billings', 'Inglewood', 'Broken Arrow', 'Sandy Springs', 'Jurupa Valley',
        'Hillsboro', 'Waterbury', 'Santa Maria', 'Boulder', 'Greeley', 'Daly City', 'Meridian', 'Lewisville', 'Davie', 'West Covina', 'League City',
        'Tyler', 'Norwalk', 'San Mateo', 'Green Bay', 'Wichita Falls', 'Burbank', 'Rialto', 'Allen', 'El Cajon', 'Las Cruces', 'Renton', 'Davenport',
        'South Bend', 'Vista', 'Woodbridge', 'San Angelo', 'Kenosha', 'Vacaville', 'South Gate', 'Mission Viejo', 'New Bedford', 'Livonia', 'Roswell',
        'Yuma', 'Lawton', 'Layton', 'Missoula', 'Redding', 'Longview', 'San Marcos', 'Boca Raton', 'San Leandro', 'Chino', 'Chino Hills', 'Alhambra'
      ];
      const US_CALLSIGNS = [/^W[A-Z]{2,3}/, /^K[A-Z]{2,3}/]; // e.g., WABC, KABC, WGN, etc.
      // City match
      if (!isLocalUS) {
        isLocalUS = US_CITIES.some(city => nameUpper.includes(city.toUpperCase()) || groupUpper.includes(city.toUpperCase()));
      }
      // Call sign match
      if (!isLocalUS) {
        isLocalUS = US_CALLSIGNS.some(re => re.test(ch.name));
      }
      return { ...ch, isLocalUS };
    });

    // Filter for diverse, global, public channels (news, public, general)
    channels = channels.filter(ch => {
      if (!ch.url || !ch.logo || !ch.name || !ch.url.startsWith('http')) {
        return false;
      }
      
      // Handle semicolon-separated group titles
      const groupParts = ch.group ? ch.group.split(';').map(g => g.trim()) : [];
      const allowedCategories = ['News', 'Public', 'General', 'Entertainment', 'Government', 'Kids', 'Education', 'Culture', 'Documentary', 'Music', 'Movies', 'Series'];
      
      // Check if any part of the group matches allowed categories
      return groupParts.some(part => 
        allowedCategories.some(category => 
          part.toLowerCase().includes(category.toLowerCase())
        )
      )
      // OR: If it's a US local channel, always include
      || ch.isLocalUS;
    });
    
    console.log('🔴 Live TV Scraper: Channels after filtering:', channels.length);
    
    // Log sample channels after filtering
    if (channels.length > 0) {
      console.log('🔴 Live TV Scraper: Sample channels after filtering:', channels.slice(0, 3).map(ch => ({
        name: ch.name,
        group: ch.group,
        logo: ch.logo ? 'has logo' : 'no logo',
        url: ch.url ? 'has url' : 'no url',
        isLocalUS: ch.isLocalUS
      })));
    }
    
    // Remove duplicates by name+country
    const seen = new Set();
    channels = channels.filter(ch => {
      const key = ch.name + ch.country;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
    console.log('🔴 Live TV Scraper: Channels after deduplication:', channels.length);
    // No limit: return all channels
    console.log('🔴 Live TV Scraper: Final channels count:', channels.length);
    cache.set('channels', channels);
    return channels;
  } catch (err) {
    console.error('🔴 Live TV Scraper error:', err.message);
    return [];
  }
}

export { getLiveTvChannels }; 