// Ad Configuration for Torvie
// This file abstracts ad tag URLs and settings for easy replacement with real GAM tags

export const AD_CONFIG = {
  // Test ad tags - replace these with your real GAM tags when approved
  AD_TAGS: {
    // Google's public VAST test tags for development/testing
    PREROLL_LINEAR: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_ad_samples&sz=640x480&cust_params=sample_ct%3Dlinear&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&correlator=',
    PREROLL_SKIPPABLE: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_preroll_skippable&sz=640x480&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&correlator=',
    MIDROLL: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/vmap_ad_samples&sz=640x480&cust_params=sample_ar%3Dpremidpost&ciu_szs=300x250&gdfp_req=1&ad_rule=1&output=vmap&unviewed_position_start=1&env=vp&cmsid=496&vid=short_onecue&correlator=',
    
    // Your real GAM tags will go here when approved:
    // PRODUCTION_PREROLL: 'https://securepubads.g.doubleclick.net/gampad/ads?...',
    // PRODUCTION_MIDROLL: 'https://securepubads.g.doubleclick.net/gampad/ads?...',
  },

  // Ad display settings
  SETTINGS: {
    // Linear ad slot dimensions (adjust to match your video player)
    LINEAR_AD_SLOT_WIDTH: 640,
    LINEAR_AD_SLOT_HEIGHT: 360,
    
    // Non-linear ad slot dimensions  
    NONLINEAR_AD_SLOT_WIDTH: 640,
    NONLINEAR_AD_SLOT_HEIGHT: 150,
    
    // Enable/disable different ad types for testing
    ENABLE_PREROLL: false, // Disabled per user request
    ENABLE_MIDROLL: false, // Start with preroll only for initial testing
    ENABLE_POSTROLL: false,
    
    // Development vs production mode
    USE_TEST_ADS: true, // Set to false when using real GAM tags
  },

  // Get the appropriate ad tag based on current configuration
  getAdTag: (adType = 'PREROLL_LINEAR') => {
    if (AD_CONFIG.SETTINGS.USE_TEST_ADS) {
      return AD_CONFIG.AD_TAGS[adType];
    } else {
      // Return production ad tags when available
      return AD_CONFIG.AD_TAGS[`PRODUCTION_${adType.replace('_LINEAR', '').replace('_SKIPPABLE', '')}`];
    }
  },

  // Easy function to switch to production mode
  enableProductionAds: () => {
    AD_CONFIG.SETTINGS.USE_TEST_ADS = false;
  },

  // Easy function to switch back to test mode
  enableTestAds: () => {
    AD_CONFIG.SETTINGS.USE_TEST_ADS = true;
  }
};

export default AD_CONFIG; 