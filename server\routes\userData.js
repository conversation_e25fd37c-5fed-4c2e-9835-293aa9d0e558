import express from 'express';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const router = express.Router();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// File paths for local storage
const PROFILES_FILE = path.join(__dirname, '..', 'storage', 'profiles.json');
const WATCHLISTS_FILE = path.join(__dirname, '..', 'storage', 'watchlists.json');
const WATCH_PROGRESS_FILE = path.join(__dirname, '..', 'storage', 'watch-progress.json');

// Ensure storage directory exists
const ensureStorageDir = async () => {
  const storageDir = path.dirname(PROFILES_FILE);
  try {
    await fs.access(storageDir);
  } catch {
    await fs.mkdir(storageDir, { recursive: true });
  }
};

// Helper to read JSON file safely
const readJsonFile = async (filePath, defaultValue = {}) => {
  try {
    await ensureStorageDir();
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.log(`Creating new ${path.basename(filePath)} file`);
    await writeJsonFile(filePath, defaultValue);
    return defaultValue;
  }
};

// Helper to write JSON file safely with backup and atomic operations
const writeJsonFile = async (filePath, data) => {
  await ensureStorageDir();
  const tempFile = filePath + '.tmp';
  const backupFile = filePath + '.backup';

  // Extra safety: ensure directory exists right before writing temp file
  await fs.mkdir(path.dirname(tempFile), { recursive: true });
  
  try {
    // Create backup of existing file
    try {
      await fs.access(filePath);
      await fs.copyFile(filePath, backupFile);
    } catch (backupError) {
      // Backup failed or file doesn't exist - continue anyway
      console.log(`📝 No existing file to backup: ${path.basename(filePath)}`);
    }
    
    // Write to temporary file first (atomic operation)
    await fs.writeFile(tempFile, JSON.stringify(data, null, 2));
    
    // Verify the temp file was written correctly
    const verifyData = await fs.readFile(tempFile, 'utf8');
    JSON.parse(verifyData); // This will throw if JSON is invalid
    
    // Atomically replace the original file
    await fs.rename(tempFile, filePath);
    
    console.log(`✅ Successfully wrote ${path.basename(filePath)} with atomic operation`);
  } catch (error) {
    console.error(`❌ Failed to write ${path.basename(filePath)}:`, error);
    
    // Clean up temp file if it exists
    try {
      await fs.unlink(tempFile);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    // Try to restore from backup if available
    try {
      await fs.access(backupFile);
      await fs.copyFile(backupFile, filePath);
      console.log(`🔄 Restored ${path.basename(filePath)} from backup due to write failure`);
    } catch (restoreError) {
      console.error(`❌ Could not restore backup for ${path.basename(filePath)}:`, restoreError);
    }
    
    throw error;
  }
};

// GET /api/user-data/profiles/:userId - Get user profiles
router.get('/profiles/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    console.log(`📂 Loading profiles for user ${userId}`);
    
    const profilesData = await readJsonFile(PROFILES_FILE, { users: {} });
    const userProfiles = Array.isArray(profilesData.users[userId]) ? profilesData.users[userId] : [];
    
    if (userProfiles) {
      console.log(`✅ Found ${userProfiles.length} profiles for user ${userId}`);
      res.json({ success: true, profiles: userProfiles });
    } else {
      console.log(`📭 No profiles found for user ${userId}`);
      res.json({ success: true, profiles: [] });
    }
  } catch (error) {
    console.error('❌ Error loading profiles:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// POST /api/user-data/profiles/:userId - Save user profiles
router.post('/profiles/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    const incomingProfiles = Array.isArray(req.body.profiles) ? req.body.profiles : [];

    console.log(`💾 Saving ${incomingProfiles.length} profiles for user ${userId} (merge mode)`);

    const profilesData = await readJsonFile(PROFILES_FILE, { users: {} });

    const existingProfiles = Array.isArray(profilesData.users[userId]) ? profilesData.users[userId] : [];

    // Merge by unique ID – update if ID matches, otherwise append
    const mergedProfiles = [...existingProfiles];
    incomingProfiles.forEach(p => {
      const idx = mergedProfiles.findIndex(e => e.id === p.id);
      if (idx >= 0) {
        mergedProfiles[idx] = { ...mergedProfiles[idx], ...p };
      } else {
        mergedProfiles.push(p);
      }
    });

    profilesData.users[userId] = mergedProfiles;
    
    await writeJsonFile(PROFILES_FILE, profilesData);
    
    console.log(`✅ Successfully saved profiles for user ${userId}`);
    res.json({ success: true });
  } catch (error) {
    console.error('❌ Error saving profiles:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// GET /api/user-data/current-profile/:userId - Get current profile
router.get('/current-profile/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    const profilesData = await readJsonFile(PROFILES_FILE, { users: {} });
    const currentProfile = profilesData.currentProfiles?.[userId] || null;
    
    res.json({ success: true, currentProfile });
  } catch (error) {
    console.error('❌ Error loading current profile:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// POST /api/user-data/current-profile/:userId - Save current profile
router.post('/current-profile/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    const currentProfile = req.body.currentProfile;
    
    console.log(`💾 Saving current profile for user ${userId}:`, currentProfile ? `"${currentProfile.name}" (ID: ${currentProfile.id})` : 'null');
    
    const profilesData = await readJsonFile(PROFILES_FILE, { users: {} });
    if (!profilesData.currentProfiles) {
      profilesData.currentProfiles = {};
    }
    profilesData.currentProfiles[userId] = currentProfile;
    
    await writeJsonFile(PROFILES_FILE, profilesData);
    
    console.log(`✅ Successfully saved current profile for user ${userId}`);
    res.json({ success: true });
  } catch (error) {
    console.error('❌ Error saving current profile:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// GET /api/user-data/watchlist/:userId/:profileId - Get watchlist
router.get('/watchlist/:userId/:profileId', async (req, res) => {
  try {
    const { userId, profileId } = req.params;
    const watchlistKey = `${userId}_${profileId}`;
    
    console.log(`📂 Loading watchlist for user ${userId}, profile ${profileId}`);
    
    const watchlistsData = await readJsonFile(WATCHLISTS_FILE, { watchlists: {} });
    const watchlist = watchlistsData.watchlists[watchlistKey] || [];
    
    console.log(`✅ Found ${watchlist.length} items in watchlist`);
    res.json({ success: true, watchlist });
  } catch (error) {
    console.error('❌ Error loading watchlist:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// POST /api/user-data/watchlist/:userId/:profileId - Save watchlist
router.post('/watchlist/:userId/:profileId', async (req, res) => {
  try {
    const { userId, profileId } = req.params;
    const watchlist = req.body.watchlist;
    const watchlistKey = `${userId}_${profileId}`;
    
    console.log(`💾 Saving ${watchlist.length} items to watchlist for user ${userId}, profile ${profileId}`);
    
    const watchlistsData = await readJsonFile(WATCHLISTS_FILE, { watchlists: {} });
    watchlistsData.watchlists[watchlistKey] = watchlist;
    
    await writeJsonFile(WATCHLISTS_FILE, watchlistsData);
    
    console.log(`✅ Successfully saved watchlist`);
    res.json({ success: true });
  } catch (error) {
    console.error('❌ Error saving watchlist:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// GET /api/user-data/watch-progress/:userId/:profileId - Get all watch progress for profile
router.get('/watch-progress/:userId/:profileId', async (req, res) => {
  try {
    const { userId, profileId } = req.params;
    
    console.log(`📖 SERVER: Loading watch progress for user ${userId}, profile ${profileId} (TYPE: ${typeof profileId})`);
    
    const watchProgressData = await readJsonFile(WATCH_PROGRESS_FILE, { users: {} });
    const userProgress = watchProgressData.users[userId]?.[profileId] || {};
    
    const itemCount = Object.keys(userProgress).length;
    console.log(`✅ SERVER: Found ${itemCount} watch progress items for user ${userId}, profile ${profileId}`);
    
    // DEBUG: Log what's actually stored for this profile
    if (itemCount > 0) {
      console.log(`📋 SERVER: Watch progress items for Profile ${profileId}:`, Object.values(userProgress).map(item => ({
        title: item.title,
        percentage: Math.round(item.watchedPercentage),
        lastWatched: new Date(item.lastWatched).toLocaleString()
      })));
    } else {
      console.log(`📋 SERVER: No watch progress items for Profile ${profileId}`);
    }
    
    res.json({ success: true, watchProgress: userProgress });
  } catch (error) {
    console.error('❌ Error loading watch progress:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// POST /api/user-data/watch-progress/:userId/:profileId - Save watch progress
router.post('/watch-progress/:userId/:profileId', async (req, res) => {
  try {
    const { userId, profileId } = req.params;
    const { contentId, contentType, title, poster, currentTime, duration, watchedPercentage, lastWatched, isCompleted } = req.body;
    
    console.log(`💾 SERVER: Saving watch progress for user ${userId}, profile ${profileId} (TYPE: ${typeof profileId}): "${title}" at ${Math.round(watchedPercentage)}%`);
    
    const watchProgressData = await readJsonFile(WATCH_PROGRESS_FILE, { users: {} });
    
    // CRITICAL: Ensure proper profile isolation
    if (!watchProgressData.users[userId]) {
      watchProgressData.users[userId] = {};
      console.log(`🆕 SERVER: Created new user storage for user ${userId}`);
    }
    if (!watchProgressData.users[userId][profileId]) {
      watchProgressData.users[userId][profileId] = {};
      console.log(`🆕 SERVER: Created new profile storage for user ${userId}, profile ${profileId}`);
    }
    
    // Store progress data
    watchProgressData.users[userId][profileId][contentId] = {
      contentId,
      contentType, // 'movie', 'tv', 'anime'
      title,
      poster,
      currentTime,
      duration,
      watchedPercentage,
      lastWatched,
      isCompleted,
      episodeInfo: req.body.episodeInfo || null // For TV shows: { season, episode, episodeTitle }
    };
    
    // CRITICAL: Enforce per-profile storage limit (20 items max per profile)
    const profileData = watchProgressData.users[userId][profileId];
    const profileItems = Object.values(profileData);
    const MAX_ITEMS_PER_PROFILE = 20;
    
    if (profileItems.length > MAX_ITEMS_PER_PROFILE) {
      console.log(`🚨 STORAGE LIMIT: Profile ${profileId} has ${profileItems.length} items, enforcing limit of ${MAX_ITEMS_PER_PROFILE}`);
      
      // Sort by lastWatched (oldest first) and remove excess items
      const sortedItems = profileItems.sort((a, b) => new Date(a.lastWatched) - new Date(b.lastWatched));
      const itemsToRemove = sortedItems.slice(0, profileItems.length - MAX_ITEMS_PER_PROFILE);
      
      for (const item of itemsToRemove) {
        delete watchProgressData.users[userId][profileId][item.contentId];
        console.log(`🗑️ STORAGE LIMIT: Removed oldest item "${item.title}" from profile ${profileId}`);
      }
      
      const remainingCount = Object.keys(watchProgressData.users[userId][profileId]).length;
      console.log(`✅ STORAGE LIMIT: Profile ${profileId} now has ${remainingCount} items (within limit)`);
    }
    
    await writeJsonFile(WATCH_PROGRESS_FILE, watchProgressData);
    
    // DEBUG: Verify the data was saved correctly
    const verifyData = await readJsonFile(WATCH_PROGRESS_FILE, { users: {} });
    const savedCount = Object.keys(verifyData.users[userId]?.[profileId] || {}).length;
    
    console.log(`💾 SERVER: Watch progress saved for user ${userId}, profile ${profileId} (TYPE: ${typeof profileId}): "${title}" at ${Math.round(watchedPercentage)}%. Total items for this profile: ${savedCount}`);
    res.json({ success: true });
  } catch (error) {
    console.error('❌ Error saving watch progress:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// DELETE /api/user-data/watch-progress/:userId/:profileId/:contentId - Remove watch progress
router.delete('/watch-progress/:userId/:profileId/:contentId', async (req, res) => {
  try {
    const { userId, profileId, contentId } = req.params;
    
    const watchProgressData = await readJsonFile(WATCH_PROGRESS_FILE, { users: {} });
    
    if (watchProgressData.users[userId]?.[profileId]?.[contentId]) {
      delete watchProgressData.users[userId][profileId][contentId];
      await writeJsonFile(WATCH_PROGRESS_FILE, watchProgressData);
      console.log(`🗑️ Watch progress removed for user ${userId}, profile ${profileId}, content ${contentId}`);
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('❌ Error removing watch progress:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// DEBUG endpoint - only enabled if ENV DEBUG_ROUTES=true (disabled by default)
if (process.env.DEBUG_ROUTES === 'true') {
  router.get('/debug', async (req, res) => {
    try {
      const profilesData = await readJsonFile(PROFILES_FILE, { users: {} });
      const watchlistsData = await readJsonFile(WATCHLISTS_FILE, { watchlists: {} });
      
      res.json({
        success: true,
        profiles: profilesData,
        watchlists: watchlistsData,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  });
}

export default router; 