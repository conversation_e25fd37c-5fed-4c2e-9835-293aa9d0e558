import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children, onAuthSuccess }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(() => localStorage.getItem('torvie_jwt') || null);

  // Debug user state changes
  useEffect(() => {
    console.log('🔄 AuthContext: User state changed:', user ? `${user.username} (ID: ${user.id})` : 'null');
  }, [user]);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = () => {
      try {
        const storedUser = localStorage.getItem('torvie_user');
        const rememberMe = localStorage.getItem('torvie_remember_me') === 'true';
        const sessionActive = sessionStorage.getItem('torvie_session_active') === 'true';
        
        console.log('🔐 Auth: Initializing - storedUser:', !!storedUser, 'rememberMe:', rememberMe, 'sessionActive:', sessionActive);
        
        if (storedUser && (rememberMe || sessionActive)) {
          const userData = JSON.parse(storedUser);
          console.log('🔐 Auth: Restoring user session for:', userData.username);
          setUser(userData);
          
          // For returning users (page refresh), don't trigger loading sequence
          // Only trigger loading for new logins
          console.log('🔐 Auth: Returning user session restored, skipping loading sequence');
        } else {
          console.log('🔐 Auth: No valid session found, staying logged out');
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('torvie_user');
        localStorage.removeItem('torvie_remember_me');
        sessionStorage.removeItem('torvie_session_active');
      } finally {
        console.log('🔐 Auth: Setting loading to false');
        setLoading(false);
      }
    };

    initAuth();
  }, [onAuthSuccess]);

  const login = async (username, password, rememberMe = false) => {
    try {
      const resp = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });
      const data = await resp.json();
      if (!data.success) {
        return { success: false, error: data.error || 'Login failed' };
      }
      setToken(data.accessToken);
      setUser(data.user);
      localStorage.setItem('torvie_jwt', data.accessToken);
      localStorage.setItem('torvie_user', JSON.stringify(data.user));
      localStorage.setItem('torvie_remember_me', rememberMe ? 'true' : 'false');
      sessionStorage.setItem('torvie_session_active', 'true');
      if (onAuthSuccess) onAuthSuccess();
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed' };
    }
  };

  const register = async (username, email, password) => {
    try {
      const resp = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, email, password, displayName: username })
      });
      const data = await resp.json();
      if (!data.success) {
        return { success: false, error: data.error || 'Registration failed' };
      }
      setToken(data.accessToken);
      setUser(data.user);
      localStorage.setItem('torvie_jwt', data.accessToken);
      localStorage.setItem('torvie_user', JSON.stringify(data.user));
      localStorage.setItem('torvie_remember_me', 'false');
      sessionStorage.setItem('torvie_session_active', 'true');
      if (onAuthSuccess) onAuthSuccess();
      return { success: true };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed' };
    }
  };

  const logout = () => {
    console.log('🏠 Auth: Logging out user from standalone app');
    setUser(null);
    localStorage.removeItem('torvie_user');
    localStorage.removeItem('torvie_remember_me');
    localStorage.removeItem('torvie_jwt');
    setToken(null);
    
    // Clear session active flag
    sessionStorage.removeItem('torvie_session_active');
    
    // Note: User profiles and watchlists remain in local app storage
    // This is proper standalone app behavior - data persists even after logout
    console.log('🏠 Auth: User data remains safely stored in local app storage');
  };

  const getAuthHeaders = () => {
    if (token && import.meta.env.VITE_REQUIRE_API_AUTH === 'true') {
      return { Authorization: `Bearer ${token}` };
    }
    return {};
  };

  const value = {
    user,
    loading,
    token,
    login,
    register,
    logout,
    getAuthHeaders,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 