import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children, onAuthSuccess }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(() => localStorage.getItem('torvie_jwt') || null);

  // Debug user state changes
  useEffect(() => {
    console.log('🔄 AuthContext: User state changed:', user ? `${user.username} (ID: ${user.id})` : 'null');
  }, [user]);

  // Predefined users with their dedicated server ports
  const PREDEFINED_USERS = [
    { id: 1, username: 'Backwood', password: 'Backwood420$', email: '<EMAIL>', serverPort: 3000, displayName: 'Backwood - Main Account' },
    { id: 2, username: 'user2', password: 'user123', email: '<EMAIL>', serverPort: 3003, displayName: 'User 2' },
    { id: 3, username: 'user3', password: 'user123', email: '<EMAIL>', serverPort: 3004, displayName: 'User 3' },
    { id: 4, username: 'user4', password: 'user123', email: '<EMAIL>', serverPort: 3005, displayName: 'User 4' },
    { id: 5, username: 'user5', password: 'user123', email: '<EMAIL>', serverPort: 3006, displayName: 'User 5' },
    { id: 6, username: 'user6', password: 'user123', email: '<EMAIL>', serverPort: 3007, displayName: 'User 6' }
  ];

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = () => {
      try {
        const storedUser = localStorage.getItem('torvie_user');
        const rememberMe = localStorage.getItem('torvie_remember_me') === 'true';
        const sessionActive = sessionStorage.getItem('torvie_session_active') === 'true';
        
        console.log('🔐 Auth: Initializing - storedUser:', !!storedUser, 'rememberMe:', rememberMe, 'sessionActive:', sessionActive);
        
        if (storedUser && (rememberMe || sessionActive)) {
          const userData = JSON.parse(storedUser);
          console.log('🔐 Auth: Restoring user session for:', userData.username);
          setUser(userData);
          
          // For returning users (page refresh), don't trigger loading sequence
          // Only trigger loading for new logins
          console.log('🔐 Auth: Returning user session restored, skipping loading sequence');
        } else {
          console.log('🔐 Auth: No valid session found, staying logged out');
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('torvie_user');
        localStorage.removeItem('torvie_remember_me');
        sessionStorage.removeItem('torvie_session_active');
      } finally {
        console.log('🔐 Auth: Setting loading to false');
        setLoading(false);
      }
    };

    initAuth();
  }, [onAuthSuccess]);

  const login = async (username, _password, rememberMe = false) => {
    try {
      console.log('🔐 Auth: Attempting login for:', username);
      
      // Find user in predefined users
      const foundUser = PREDEFINED_USERS.find(u => u.username === username && u.password === _password);
      
      if (!foundUser) {
        console.log('🔐 Auth: Invalid credentials for:', username);
        return { success: false, error: 'Invalid username or password' };
      }
      
      let jwtToken = null;

      if (import.meta.env.VITE_REQUIRE_API_AUTH === 'true') {
        try {
          const resp = await fetch(`http://localhost:${foundUser.serverPort}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: username, password: _password })
          });
          const tokenData = await resp.json();
          if (tokenData.success && tokenData.token) {
            jwtToken = tokenData.token;
            localStorage.setItem('torvie_jwt', jwtToken);
            console.log('🔐 Auth: Received JWT token from server');
          } else {
            console.warn('🔐 Auth: JWT token not received:', tokenData.error);
          }
        } catch (err) {
          console.error('🔐 Auth: Error fetching JWT token:', err);
        }
      }

      const userData = {
        id: foundUser.id,
        username: foundUser.username,
        email: foundUser.email,
        displayName: foundUser.displayName,
        serverPort: foundUser.serverPort,
        created_at: new Date().toISOString()
      };
      
      console.log('🔐 Auth: Login successful for:', username, 'Server port:', foundUser.serverPort);
      
      setToken(jwtToken);
      setUser(userData);
      localStorage.setItem('torvie_user', JSON.stringify(userData));
      localStorage.setItem('torvie_remember_me', rememberMe ? 'true' : 'false');
      
      // Always set session active for current session (even if remember me is false)
      sessionStorage.setItem('torvie_session_active', 'true');
      
      // Clear any existing profile selection when switching users
      localStorage.removeItem('torvie_profile');
      
      // Trigger loading sequence for new login
      if (onAuthSuccess) {
        console.log('🔐 Auth: Triggering loading sequence for new login');
        onAuthSuccess();
      }
      
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed' };
    }
  };

  const register = async (username, email, password) => {
    try {
      // Check if username already exists
      const existingUser = PREDEFINED_USERS.find(u => u.username === username);
      if (existingUser) {
        return { success: false, error: 'Username already exists' };
      }
      
      // For now, registration just creates a temporary user (could be expanded)
      const userData = {
        id: Date.now(),
        username,
        email,
        displayName: username,
        serverPort: 3000, // Default to main server
        created_at: new Date().toISOString()
      };
      
      setUser(userData);
      localStorage.setItem('torvie_user', JSON.stringify(userData));
      localStorage.setItem('torvie_remember_me', 'false');
      
      // Set session active for registration
      sessionStorage.setItem('torvie_session_active', 'true');
      
      // Trigger loading sequence for new registration
      if (onAuthSuccess) {
        console.log('🔐 Auth: Triggering loading sequence for new registration');
        onAuthSuccess();
      }
      
      return { success: true };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed' };
    }
  };

  const logout = () => {
    console.log('🏠 Auth: Logging out user from standalone app');
    setUser(null);
    localStorage.removeItem('torvie_user');
    localStorage.removeItem('torvie_remember_me');
    localStorage.removeItem('torvie_jwt');
    setToken(null);
    
    // Clear session active flag
    sessionStorage.removeItem('torvie_session_active');
    
    // Note: User profiles and watchlists remain in local app storage
    // This is proper standalone app behavior - data persists even after logout
    console.log('🏠 Auth: User data remains safely stored in local app storage');
  };

  const getAuthHeaders = () => {
    if (token && import.meta.env.VITE_REQUIRE_API_AUTH === 'true') {
      return { Authorization: `Bearer ${token}` };
    }
    return {};
  };

  const value = {
    user,
    loading,
    token,
    login,
    register,
    logout,
    getAuthHeaders,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 