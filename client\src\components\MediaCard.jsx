import React, { forwardRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { watchlistStorage } from '../utils/watchlistStorage';

// IMPORTANT: This component should ONLY use TMDB data for display (posters, titles, info)
// Torrent data should NEVER be used for display - only for the play button functionality
// The 'media' prop contains TMDB data and should be the only source for visual elements

const genreIdMap = { 
  28: "Action", 12: "Adventure", 16: "Animation", 35: "Comedy", 80: "Crime", 
  99: "Documentary", 18: "Drama", 10751: "Family", 14: "Fantasy", 36: "History", 
  27: "Horror", 10402: "Music", 9648: "Mystery", 10749: "Romance", 878: "Sci-Fi", 
  10770: "TV Movie", 53: "Thriller", 10752: "War", 37: "Western" 
};

const QuickActionButton = ({ icon, label, onClick }) => (
    <button 
        title={label} 
        className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm transform hover:scale-110 hover:bg-white/20 transition-all duration-200 border border-white/20"
        onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            if (onClick) {
                onClick(e);
            }
        }}
    >
        <div className="flex items-center justify-center w-full h-full">
            {icon}
        </div>
    </button>
);

const MediaCard = forwardRef(({ media, onClick, onPlay, onMoreInfo, showActions = true, className = '', isInWatchlist = false, toggleWatchlist, noWatchlistGlow = false, hideWatchlistButton = false, hideMoreInfoButton = false }, ref) => {
  if (!media) return null;

  // Defensive: If media is missing poster_path or title, try to find the full movie object from trending movies in localStorage
  let fullMedia = media;
  if ((!media.poster_path || !media.title) && window.localStorage) {
    const trending = JSON.parse(localStorage.getItem('torvie_trending_movies') || '[]');
    const found = trending.find(m => String(m.id) === String(media.id));
    if (found) fullMedia = { ...found, ...media };
  }
  const imageUrl = fullMedia.poster_path 
    ? (fullMedia.poster_path.startsWith('http') ? fullMedia.poster_path : `https://image.tmdb.org/t/p/w500${fullMedia.poster_path}`) 
    : `https://placehold.co/500x750/000000/1f2937?text=${encodeURIComponent(fullMedia.title || 'No Title')}`;
  
  const handleImageError = (e) => { 
    e.target.onerror = null; 
    e.target.src = `https://placehold.co/500x750/000000/1f2937?text=Image+Not+Found`; 
  };

  return (
    <div 
      ref={ref}
      className={`bg-black rounded-lg overflow-hidden shadow-lg 
                 ${showActions 
                   ? 'transform hover:scale-105 transition-transform duration-300 group cursor-pointer border border-transparent ' + (isInWatchlist && !noWatchlistGlow ? '' : 'hover:border-purple-500/50')
                   : ''
                 }
                 ${className}`}
      style={{
        width: 'var(--media-card-width)',
        minWidth: 'var(--media-card-width)',
        height: 'var(--media-card-height)',
        boxShadow: isInWatchlist && !noWatchlistGlow
          ? '0 0 12px 2px rgba(6,182,212,0.55), 0 0 24px 8px rgba(6,182,212,0.25), 0 0 36px 16px rgba(6,182,212,0.12)'
          : undefined
      }}
      onMouseEnter={showActions ? (e => {
        if (!isInWatchlist || noWatchlistGlow) {
          e.currentTarget.style.boxShadow = '0 0 12px 2px rgba(139,92,246,0.55), 0 0 24px 8px rgba(139,92,246,0.25), 0 0 36px 16px rgba(139,92,246,0.12)';
        }
      }) : undefined}
      onMouseLeave={showActions ? (e => {
        if (!isInWatchlist || noWatchlistGlow) {
          e.currentTarget.style.boxShadow = '';
        }
      }) : undefined}
      onClick={onClick}
    >
      <div className="relative">
        {/* Static backdrop for hero poster (no hover) */}
        {!showActions && (
          <div className="absolute inset-0 z-0 bg-black/40 backdrop-blur-sm" style={{boxShadow:'0 4px 32px 0 rgba(0,0,0,0.45)'}}></div>
        )}
        <img 
          src={imageUrl} 
          alt={`Poster for ${fullMedia.title || fullMedia.name}`} 
          className="w-full h-[330px] object-cover rounded-t-lg relative z-10" 
          onError={handleImageError} 
          loading="lazy" 
        />
        
        {showActions && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20">
            <div className="flex items-center justify-center gap-4">
              <QuickActionButton 
                label="Play" 
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor" className="text-white ml-1">
                    <path d="M8 5v14l11-7z"></path>
                  </svg>
                }
                onClick={() => {
                  onPlay && onPlay(media);
                }}
              />
              {!hideWatchlistButton && (
                <QuickActionButton 
                  label={isInWatchlist ? 'Remove from Watchlist' : 'Add to Watchlist'}
                  icon={isInWatchlist ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" fill="rgba(255,255,255,0.15)" />
                      <path d="M7 12h10" stroke="white" strokeWidth="2" strokeLinecap="round" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleWatchlist && toggleWatchlist(media);
                  }}
                />
              )}
              {!hideMoreInfoButton && (
                <QuickActionButton 
                  label="Trailer" 
                  icon={
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                      <polygon points="5,3 19,12 5,21 5,3"/>
                    </svg>
                  }
                  onClick={(e) => {
                    e.stopPropagation();
                    onMoreInfo && onMoreInfo(media);
                  }}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

MediaCard.displayName = "MediaCard";

export default MediaCard; 