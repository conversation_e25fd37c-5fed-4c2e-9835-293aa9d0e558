# Backend Dockerfile - Optimized Multi-Stage Build
FROM node:18-alpine AS dependencies
# Alpine is much smaller than slim (5MB vs 150MB base)
WORKDIR /app
COPY package*.json ./
# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# Build stage for any compilation needs
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --no-audit --no-fund
COPY . .
# If you have any build steps, add them here

# Final minimal runtime image
FROM node:18-alpine AS runtime
# Install only required runtime dependencies
RUN apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy only production dependencies
COPY --from=dependencies /app/node_modules ./node_modules

# Copy only necessary application files (excluding downloads, etc.)
COPY --chown=node:node package*.json ./
COPY --chown=node:node server.js ./
COPY --chown=node:node routes ./routes
COPY --chown=node:node services ./services
COPY --chown=node:node middleware ./middleware
COPY --chown=node:node api ./api
COPY --chown=node:node diagnostics ./diagnostics

# Create necessary directories (empty, will be mounted as volumes)
RUN mkdir -p storage downloads && \
    chown -R node:node storage downloads

# Switch to non-root user
USER node

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Start the application
CMD ["node", "server.js"] 