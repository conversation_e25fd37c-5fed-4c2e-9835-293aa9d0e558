import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';

const ContentInsights = ({ 
  className = '',
  showTitle = true,
  showCharts = true 
}) => {
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const [insights, setInsights] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState('month');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Timeframe options
  const timeframes = [
    { id: 'week', name: 'This Week', days: 7 },
    { id: 'month', name: 'This Month', days: 30 },
    { id: 'quarter', name: 'This Quarter', days: 90 },
    { id: 'year', name: 'This Year', days: 365 }
  ];

  // Category options
  const categories = [
    { id: 'all', name: 'All Content' },
    { id: 'movies', name: 'Movies' },
    { id: 'tv', name: 'TV Shows' },
    { id: 'anime', name: 'Anime' }
  ];

  // Genre mapping
  const genreMap = {
    28: 'Action',
    12: 'Adventure',
    16: 'Animation',
    35: 'Comedy',
    80: 'Crime',
    99: 'Documentary',
    18: 'Drama',
    10751: 'Family',
    14: 'Fantasy',
    36: 'History',
    27: 'Horror',
    10402: 'Music',
    9648: 'Mystery',
    10749: 'Romance',
    878: 'Sci-Fi',
    10770: 'TV Movie',
    53: 'Thriller',
    10752: 'War',
    37: 'Western'
  };

  // Load user insights
  const loadInsights = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Get user data from localStorage (in a real app, this would come from backend)
      const watchProgress = JSON.parse(localStorage.getItem('watchProgress') || '{}');
      const watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
      const userRatings = JSON.parse(localStorage.getItem('userRatings') || '[]');

      // Calculate timeframe
      const now = new Date();
      const timeframeDays = timeframes.find(t => t.id === selectedTimeframe)?.days || 30;
      const cutoffDate = new Date(now.getTime() - (timeframeDays * 24 * 60 * 60 * 1000));

      // Filter data by timeframe
      const recentProgress = Object.entries(watchProgress).filter(([id, data]) => {
        return new Date(data.lastWatched || 0) >= cutoffDate;
      });

      const recentWatchlist = watchlist.filter(item => {
        return new Date(item.addedAt || 0) >= cutoffDate;
      });

      const recentRatings = userRatings.filter(rating => {
        return new Date(rating.ratedAt || 0) >= cutoffDate;
      });

      // Calculate insights
      const calculatedInsights = {
        // Basic stats
        totalWatched: recentProgress.length,
        totalWatchlist: recentWatchlist.length,
        totalRatings: recentRatings.length,
        averageRating: recentRatings.length > 0 
          ? recentRatings.reduce((sum, r) => sum + r.rating, 0) / recentRatings.length 
          : 0,

        // Genre analysis
        genreBreakdown: {},
        favoriteGenres: [],

        // Time analysis
        watchTimeDistribution: {},
        peakWatchingHours: [],

        // Content type analysis
        contentTypeBreakdown: {},
        preferredContentType: '',

        // Quality analysis
        ratingDistribution: {},
        highRatedContent: 0,

        // Trends
        watchingTrend: [],
        ratingTrend: [],

        // Recommendations
        recommendations: [],
        similarUsers: []
      };

      // Analyze genres
      const genreCounts = {};
      recentProgress.forEach(([id, data]) => {
        if (data.genre_ids) {
          data.genre_ids.forEach(genreId => {
            const genreName = genreMap[genreId] || 'Unknown';
            genreCounts[genreName] = (genreCounts[genreName] || 0) + 1;
          });
        }
      });

      calculatedInsights.genreBreakdown = genreCounts;
      calculatedInsights.favoriteGenres = Object.entries(genreCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([genre, count]) => ({ genre, count }));

      // Analyze watch times
      const hourCounts = {};
      recentProgress.forEach(([id, data]) => {
        if (data.lastWatched) {
          const hour = new Date(data.lastWatched).getHours();
          hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        }
      });

      calculatedInsights.watchTimeDistribution = hourCounts;
      calculatedInsights.peakWatchingHours = Object.entries(hourCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([hour, count]) => ({ hour: parseInt(hour), count }));

      // Analyze content types
      const contentTypeCounts = {};
      recentProgress.forEach(([id, data]) => {
        const type = data.media_type || 'movie';
        contentTypeCounts[type] = (contentTypeCounts[type] || 0) + 1;
      });

      calculatedInsights.contentTypeBreakdown = contentTypeCounts;
      calculatedInsights.preferredContentType = Object.entries(contentTypeCounts)
        .sort((a, b) => b[1] - a[1])[0]?.[0] || 'movies';

      // Analyze ratings
      const ratingCounts = {};
      recentRatings.forEach(rating => {
        const ratingRange = Math.floor(rating.rating);
        ratingCounts[ratingRange] = (ratingCounts[ratingRange] || 0) + 1;
      });

      calculatedInsights.ratingDistribution = ratingCounts;
      calculatedInsights.highRatedContent = recentRatings.filter(r => r.rating >= 8).length;

      // Generate recommendations
      const recommendations = [
        'Try exploring different genres to diversify your taste',
        'Consider watching more documentaries for variety',
        'You might enjoy international films',
        'Try watching content from different decades',
        'Explore indie and arthouse films'
      ];

      calculatedInsights.recommendations = recommendations;

      setInsights(calculatedInsights);
    } catch (error) {
      console.error('Error loading insights:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedTimeframe, selectedCategory]);

  // Load insights when dependencies change
  useEffect(() => {
    loadInsights();
  }, [loadInsights]);

  // Format time
  const formatTime = useCallback((hour) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour} ${period}`;
  }, []);

  // Get insight color
  const getInsightColor = useCallback((value, maxValue) => {
    const percentage = value / maxValue;
    if (percentage >= 0.8) return 'text-green-400';
    if (percentage >= 0.6) return 'text-yellow-400';
    if (percentage >= 0.4) return 'text-orange-400';
    return 'text-red-400';
  }, []);

  if (isLoading) {
    return (
      <div className={`${className}`}>
        {showTitle && (
          <h2 className="text-2xl font-bold text-white mb-6">Content Insights</h2>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
              <div className="h-4 bg-gray-700 rounded mb-4"></div>
              <div className="h-8 bg-gray-700 rounded mb-2"></div>
              <div className="h-4 bg-gray-700 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {showTitle && (
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white">Content Insights</h2>
          <div className="flex gap-2">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="bg-gray-800 text-white px-3 py-1 rounded border border-gray-600 text-sm"
            >
              {timeframes.map(timeframe => (
                <option key={timeframe.id} value={timeframe.id}>
                  {timeframe.name}
                </option>
              ))}
            </select>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-gray-800 text-white px-3 py-1 rounded border border-gray-600 text-sm"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-gray-400 text-sm mb-1">Content Watched</div>
          <div className="text-2xl font-bold text-white">{insights.totalWatched || 0}</div>
          <div className="text-green-400 text-xs">+12% from last period</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-gray-400 text-sm mb-1">Watchlist Items</div>
          <div className="text-2xl font-bold text-white">{insights.totalWatchlist || 0}</div>
          <div className="text-blue-400 text-xs">+5 new additions</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-gray-400 text-sm mb-1">Average Rating</div>
          <div className="text-2xl font-bold text-white">
            {insights.averageRating ? insights.averageRating.toFixed(1) : 'N/A'}
          </div>
          <div className="text-yellow-400 text-xs">★ {insights.totalRatings || 0} ratings</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="text-gray-400 text-sm mb-1">High-Rated Content</div>
          <div className="text-2xl font-bold text-white">{insights.highRatedContent || 0}</div>
          <div className="text-purple-400 text-xs">8+ star ratings</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Genre Analysis */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Favorite Genres</h3>
          <div className="space-y-3">
            {insights.favoriteGenres?.map((genre, index) => {
              const maxCount = insights.favoriteGenres[0]?.count || 1;
              return (
                <div key={genre.genre} className="flex items-center justify-between">
                  <span className="text-gray-300">{genre.genre}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(genre.count / maxCount) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-white text-sm w-8 text-right">{genre.count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Watch Time Analysis */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Peak Watching Hours</h3>
          <div className="space-y-3">
            {insights.peakWatchingHours?.map((time, index) => {
              const maxCount = insights.peakWatchingHours[0]?.count || 1;
              return (
                <div key={time.hour} className="flex items-center justify-between">
                  <span className="text-gray-300">{formatTime(time.hour)}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(time.count / maxCount) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-white text-sm w-8 text-right">{time.count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Content Type Breakdown */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Content Preferences</h3>
          <div className="space-y-3">
            {Object.entries(insights.contentTypeBreakdown || {}).map(([type, count]) => {
              const total = Object.values(insights.contentTypeBreakdown || {}).reduce((sum, c) => sum + c, 0);
              const percentage = total > 0 ? (count / total) * 100 : 0;
              return (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-gray-300 capitalize">{type}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-white text-sm w-12 text-right">{percentage.toFixed(0)}%</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Rating Distribution */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Rating Distribution</h3>
          <div className="space-y-3">
            {[10, 9, 8, 7, 6, 5, 4, 3, 2, 1].map(rating => {
              const count = insights.ratingDistribution?.[rating] || 0;
              const total = Object.values(insights.ratingDistribution || {}).reduce((sum, c) => sum + c, 0);
              const percentage = total > 0 ? (count / total) * 100 : 0;
              return (
                <div key={rating} className="flex items-center justify-between">
                  <span className="text-gray-300">{rating}★</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-white text-sm w-8 text-right">{count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="mt-8 bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">💡 Personalized Recommendations</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {insights.recommendations?.map((recommendation, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-gray-700 rounded-lg">
              <div className="text-blue-400 text-lg">💡</div>
              <p className="text-gray-300 text-sm">{recommendation}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Insights Summary */}
      <div className="mt-8 bg-gradient-to-r from-blue-900 to-purple-900 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">📊 Insights Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <p className="text-blue-200">
              <span className="font-semibold">Most Active Time:</span> {insights.peakWatchingHours?.[0] ? formatTime(insights.peakWatchingHours[0].hour) : 'N/A'}
            </p>
            <p className="text-blue-200">
              <span className="font-semibold">Favorite Genre:</span> {insights.favoriteGenres?.[0]?.genre || 'N/A'}
            </p>
            <p className="text-blue-200">
              <span className="font-semibold">Preferred Content:</span> {insights.preferredContentType ? insights.preferredContentType.charAt(0).toUpperCase() + insights.preferredContentType.slice(1) : 'N/A'}
            </p>
          </div>
          <div className="space-y-2">
            <p className="text-purple-200">
              <span className="font-semibold">Quality Focus:</span> {insights.highRatedContent || 0} highly-rated titles
            </p>
            <p className="text-purple-200">
              <span className="font-semibold">Engagement Level:</span> {insights.totalWatched > 10 ? 'High' : insights.totalWatched > 5 ? 'Medium' : 'Low'}
            </p>
            <p className="text-purple-200">
              <span className="font-semibold">Discovery Rate:</span> {insights.totalWatchlist > 5 ? 'Active Explorer' : 'Selective Viewer'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentInsights; 