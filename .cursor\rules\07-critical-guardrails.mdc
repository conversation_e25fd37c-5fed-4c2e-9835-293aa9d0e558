---
description: "A list of critical anti-patterns, deprecated methods, and security vulnerabilities that MUST be avoided at all costs."
globs:
  - "**/*.js"
  - "**/*.ts"
  - "**/*.tsx"
  - "**/*.py"
  - "**/*.go"
alwaysApply: false
---

# CRITICAL GUARDRAILS AND ANTI-PATTERNS

🚨 **CRITICAL INSTRUCTIONS: VIOLATING THESE RULES WILL BREAK THE APPLICATION.** 🚨

You MUST NOT generate any of the following code patterns under any circumstances. Before generating code, you MUST verify that you are not using any of these patterns. If you see them in existing code, you should recommend their removal.

### Example: Deprecated Library `old-request-handler`
The library `old-request-handler` has been deprecated due to a critical security vulnerability.

**❌ NEVER GENERATE THIS:**
```javascript
import { handleRequest } from 'old-request-handler';

// ...
const response = await handleRequest(url, data);

✅ ALWAYS USE THIS EXACT PATTERN INSTEAD:
The official replacement is the internal module @/utils/safe-fetch.
JavaScript

import { safeFetch } from '@/utils/safe-fetch';

// ...
const response = await safeFetch(url, data);

Verification Checklist:

    Is there any import from old-request-handler? If yes, STOP and FIX.

    Am I using safeFetch for all external requests? If not, STOP and FIX.


---

### 4. The Interactive Review Gate (How to Verify Work)

This rule formalizes the development process into two distinct phases: implementation and review. It forces the AI to stop and verify its own work before marking a task as complete, inviting collaboration and ensuring higher quality.

**File:** `.cursor/rules/interactive-review.mdc`
```mdc
---
description: "A formal two-phase protocol for complex tasks that separates implementation from a final, interactive verification and review stage."
agentRequested: true
---

# Interactive Review Gate Protocol

For any task that involves creating a new feature or making significant code changes, you will follow this two-phase process.

### Phase 1: Implementation
You will follow all other active protocols (Reasoning, Principles, etc.) to develop the solution. This includes writing the code, tests, and documentation as requested.

### Phase 2: Interactive Review (Transition Point)
Once you believe you have completed all substantive actions for the request, **DO NOT CONCLUDE THE CONVERSATION.** You MUST now transition to the Final Interactive Review phase.

**1. Announce the Transition:**
Clearly state: "I have completed the primary implementation. I am now entering the **Interactive Review Phase**."

**2. Perform Self-Validation:**
Propose and describe a set of validation steps you will now take.
* **Example:** "To validate my work, I will:
    1.  Run the unit and integration tests I created to ensure they all pass.
    2.  Perform a static analysis check for any potential security flaws.
    3.  Briefly summarize how the changes meet the initial request."

**3. Await Further Instructions:**
After presenting your validation plan and results, stop and await my feedback. Clearly state: "**The review gate is no