<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 5px; }
        .success { color: #4ade80; }
        .error { color: #f87171; }
        .warning { color: #fbbf24; }
        pre { background: #2a2a2a; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔐 Authentication Debug</h1>
    
    <div class="debug-section">
        <h2>LocalStorage State</h2>
        <div id="localStorageState"></div>
    </div>
    
    <div class="debug-section">
        <h2>SessionStorage State</h2>
        <div id="sessionStorageState"></div>
    </div>
    
    <div class="debug-section">
        <h2>Actions</h2>
        <button onclick="clearAuth()">Clear All Auth Data</button>
        <button onclick="setTestUser()">Set Test User</button>
        <button onclick="refresh()">Refresh</button>
    </div>

    <script>
        function displayStorageState() {
            const localStorageState = document.getElementById('localStorageState');
            const sessionStorageState = document.getElementById('sessionStorageState');
            
            // Check localStorage
            const torvieUser = localStorage.getItem('torvie_user');
            const torvieRememberMe = localStorage.getItem('torvie_remember_me');
            const torvieJwt = localStorage.getItem('torvie_jwt');
            const torvieProfileBackup = localStorage.getItem('torvie_current_profile_backup');
            
            localStorageState.innerHTML = `
                <p><strong>torvie_user:</strong> ${torvieUser ? '<span class="success">✓ Set</span>' : '<span class="error">✗ Not set</span>'}</p>
                <p><strong>torvie_remember_me:</strong> ${torvieRememberMe}</p>
                <p><strong>torvie_jwt:</strong> ${torvieJwt ? '<span class="success">✓ Set</span>' : '<span class="error">✗ Not set</span>'}</p>
                <p><strong>torvie_current_profile_backup:</strong> ${torvieProfileBackup ? '<span class="success">✓ Set</span>' : '<span class="error">✗ Not set</span>'}</p>
                ${torvieUser ? `<pre>${JSON.stringify(JSON.parse(torvieUser), null, 2)}</pre>` : ''}
            `;
            
            // Check sessionStorage
            const torvieSessionActive = sessionStorage.getItem('torvie_session_active');
            
            sessionStorageState.innerHTML = `
                <p><strong>torvie_session_active:</strong> ${torvieSessionActive}</p>
            `;
        }
        
        function clearAuth() {
            localStorage.removeItem('torvie_user');
            localStorage.removeItem('torvie_remember_me');
            localStorage.removeItem('torvie_jwt');
            localStorage.removeItem('torvie_current_profile_backup');
            sessionStorage.removeItem('torvie_session_active');
            refresh();
        }
        
        function setTestUser() {
            const testUser = {
                id: 1,
                username: 'Backwood',
                email: '<EMAIL>',
                displayName: 'Backwood - Main Account',
                serverPort: 3002,
                created_at: new Date().toISOString()
            };
            
            localStorage.setItem('torvie_user', JSON.stringify(testUser));
            localStorage.setItem('torvie_remember_me', 'true');
            sessionStorage.setItem('torvie_session_active', 'true');
            refresh();
        }
        
        function refresh() {
            displayStorageState();
        }
        
        // Initial display
        displayStorageState();
    </script>
</body>
</html> 