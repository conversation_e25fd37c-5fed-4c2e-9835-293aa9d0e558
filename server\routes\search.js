import express from 'express';
import { asyncHandler } from '../middleware/asyncHandler.js';
import { searchTorrents } from '../services/search.js';

const router = express.Router();

// Search endpoint for torrents ONLY - returns torrent data for playback
// Display data should come exclusively from TMDB endpoints
router.get('/', asyncHandler(async (req, res) => {
  const { query, type, season, episode, allEpisodes } = req.query;

  if (!query || query.trim() === '') {
    return res.status(400).json({
      error: 'Search query is required',
      message: 'Please provide a search term in the query parameter',
    });
  }

  console.log(`Search request received for: "${query}" (type: ${type || 'general'})`);

  const torrents = await searchTorrents(query.trim(), type || 'general', {
    season: season ? parseInt(season, 10) : undefined,
    episode: episode ? parseInt(episode, 10) : undefined,
    allEpisodes: allEpisodes === 'true',
  });

  res.json({
    success: true,
    query: query.trim(),
    results: torrents,
    total: Array.isArray(torrents) ? torrents.length : Object.keys(torrents).length,
    sources: Array.isArray(torrents)
      ? [...new Set(torrents.map(t => t.source))]
      : Object.values(torrents).map(t => t.source),
  });
}));

export default router; 