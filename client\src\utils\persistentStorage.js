// Robust persistence utility with multiple storage mechanisms and fallbacks
export class PersistentStorage {
  constructor() {
    this.isAvailable = this.testStorageAvailability();
    this.storageType = this.detectStorageType();
    console.log('🔧 PersistentStorage: Initialized with storage type:', this.storageType);
  }

  // Test if localStorage is available and working
  testStorageAvailability() {
    try {
      const testKey = '__storage_test__';
      const testValue = 'test';
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);
      return retrieved === testValue;
    } catch (e) {
      console.error('🚨 PersistentStorage: localStorage test failed:', e);
      return false;
    }
  }

  // Detect what type of storage environment we're in
  detectStorageType() {
    if (!this.isAvailable) return 'none';
    
    // Check if we're in incognito/private mode
    try {
      localStorage.setItem('__incognito_test__', '1');
      localStorage.removeItem('__incognito_test__');
      return 'standard';
    } catch (e) {
      return 'incognito';
    }
  }

  // Enhanced setItem with verification and retries
  async setItem(key, value, retries = 3) {
    if (!this.isAvailable) {
      console.warn('🚨 PersistentStorage: Storage not available, cannot save:', key);
      return false;
    }

    const serialized = JSON.stringify(value);
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`💾 PersistentStorage: Attempt ${attempt} - Saving ${key}:`, value);
        
        // Save to localStorage
        localStorage.setItem(key, serialized);
        
        // Immediately verify it was saved
        const verification = localStorage.getItem(key);
        if (verification === serialized) {
          console.log(`✅ PersistentStorage: Successfully saved ${key} on attempt ${attempt}`);
          
          // Additional verification after a short delay
          setTimeout(() => {
            const delayedVerification = localStorage.getItem(key);
            if (delayedVerification === serialized) {
              console.log(`✅ PersistentStorage: Delayed verification successful for ${key}`);
            } else {
              console.error(`❌ PersistentStorage: Delayed verification failed for ${key}`);
            }
          }, 100);
          
          return true;
        } else {
          console.error(`❌ PersistentStorage: Verification failed on attempt ${attempt} for ${key}`);
          console.error(`  Expected: ${serialized}`);
          console.error(`  Got: ${verification}`);
        }
      } catch (error) {
        console.error(`❌ PersistentStorage: Error on attempt ${attempt} for ${key}:`, error);
        
        if (attempt === retries) {
          console.error(`🚨 PersistentStorage: Failed to save ${key} after ${retries} attempts`);
          return false;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 50 * attempt));
      }
    }
    
    return false;
  }

  // Enhanced getItem with fallback mechanisms
  getItem(key) {
    if (!this.isAvailable) {
      console.warn('🚨 PersistentStorage: Storage not available, cannot retrieve:', key);
      return null;
    }

    try {
      const value = localStorage.getItem(key);
      if (value === null) {
        console.log(`📭 PersistentStorage: No data found for ${key}`);
        return null;
      }
      
      const parsed = JSON.parse(value);
      console.log(`📦 PersistentStorage: Retrieved ${key}:`, parsed);
      return parsed;
    } catch (error) {
      console.error(`❌ PersistentStorage: Error retrieving ${key}:`, error);
      return null;
    }
  }

  // Remove item with verification
  removeItem(key) {
    if (!this.isAvailable) return false;
    
    try {
      localStorage.removeItem(key);
      const verification = localStorage.getItem(key);
      const success = verification === null;
      console.log(`🗑️ PersistentStorage: Removed ${key} - ${success ? 'Success' : 'Failed'}`);
      return success;
    } catch (error) {
      console.error(`❌ PersistentStorage: Error removing ${key}:`, error);
      return false;
    }
  }

  // Get all Torvie keys
  getAllTorvieKeys() {
    if (!this.isAvailable) return [];
    
    const keys = [];
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('torvie_')) {
          keys.push(key);
        }
      }
    } catch (error) {
      console.error('❌ PersistentStorage: Error getting keys:', error);
    }
    return keys;
  }

  // Comprehensive storage health check
  healthCheck() {
    console.log('🏥 PersistentStorage: Running health check...');
    
    const results = {
      available: this.isAvailable,
      storageType: this.storageType,
      quota: null,
      torvieKeys: [],
      testWrite: false,
      testRead: false
    };

    // Check storage quota
    try {
      if (navigator.storage && navigator.storage.estimate) {
        navigator.storage.estimate().then(estimate => {
          results.quota = {
            used: estimate.usage,
            total: estimate.quota,
            usedMB: Math.round(estimate.usage / 1024 / 1024 * 100) / 100,
            totalMB: Math.round(estimate.quota / 1024 / 1024 * 100) / 100
          };
          console.log('💽 PersistentStorage: Storage quota:', results.quota);
        });
      }
    } catch (e) {
      console.log('💽 PersistentStorage: Storage quota not available');
    }

    // Get Torvie keys
    results.torvieKeys = this.getAllTorvieKeys();

    // Test write/read
    const testKey = 'torvie_health_check';
    const testData = { timestamp: Date.now(), test: true };
    
    results.testWrite = this.setItem(testKey, testData);
    if (results.testWrite) {
      const retrieved = this.getItem(testKey);
      results.testRead = retrieved && retrieved.timestamp === testData.timestamp;
      this.removeItem(testKey);
    }

    console.log('🏥 PersistentStorage: Health check results:', results);
    return results;
  }

  // Force save with extra verification
  forceSave(key, value) {
    console.log(`🚀 PersistentStorage: Force saving ${key}...`);
    
    // Run health check first
    const health = this.healthCheck();
    if (!health.available) {
      console.error('🚨 PersistentStorage: Cannot force save - storage not available');
      return false;
    }

    // Try multiple times with different approaches
    const success = this.setItem(key, value, 5);
    
    if (success) {
      // Additional verification
      setTimeout(() => {
        const verification = this.getItem(key);
        if (JSON.stringify(verification) === JSON.stringify(value)) {
          console.log(`✅ PersistentStorage: Force save verification successful for ${key}`);
        } else {
          console.error(`❌ PersistentStorage: Force save verification failed for ${key}`);
        }
      }, 500);
    }
    
    return success;
  }
}

// Create singleton instance
export const persistentStorage = new PersistentStorage();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.persistentStorage = persistentStorage;
  
  // Add convenient shortcuts
  window.debugTorvie = {
    healthCheck: () => persistentStorage.healthCheck(),
    getAllData: () => {
      const keys = persistentStorage.getAllTorvieKeys();
      const data = {};
      keys.forEach(key => {
        data[key] = persistentStorage.getItem(key);
      });
      return data;
    },
    clearAll: () => {
      const keys = persistentStorage.getAllTorvieKeys();
      keys.forEach(key => persistentStorage.removeItem(key));
      console.log('🧹 Cleared all Torvie data');
    },
    goToDebugPage: () => {
      window.location.href = '/debug-storage';
    }
  };
  
  console.log('🔧 Torvie Debug Tools Available:');
  console.log('  window.persistentStorage - Full persistent storage API');
  console.log('  window.debugTorvie.healthCheck() - Run storage health check');
  console.log('  window.debugTorvie.getAllData() - Get all stored data');
  console.log('  window.debugTorvie.clearAll() - Clear all Torvie data');
  console.log('  window.debugTorvie.goToDebugPage() - Go to debug page');
  console.log('  Or navigate to: http://localhost:5173/debug-storage');
} 