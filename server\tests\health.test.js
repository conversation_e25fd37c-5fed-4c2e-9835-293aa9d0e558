/* eslint-env jest */
import request from 'supertest';
import app from '../server.js';

describe('API smoke tests', () => {
  it('returns 200 on /api/health', async () => {
    const res = await request(app).get('/api/health');
    expect(res.status).toBe(200);
    expect(res.body.status).toBe('ok');
  });

  it('returns 401 on /api/search without auth', async () => {
    const res = await request(app).get('/api/search?query=test');
    expect(res.status).toBe(401);
  });
}); 