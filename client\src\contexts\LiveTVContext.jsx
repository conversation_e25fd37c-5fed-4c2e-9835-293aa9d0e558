import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiFetch } from '../utils/api';

const LiveTVContext = createContext();

export function LiveTVProvider({ children }) {
  const [channels, setChannels] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchChannels = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const res = await apiFetch('/api/live-tv');
        const data = await res.json();
        setChannels(data);
        localStorage.setItem('torvie_live_tv_ready', 'true');
      } catch (err) {
        setError('Failed to load live TV channels');
        localStorage.setItem('torvie_live_tv_ready', 'false');
      } finally {
        setIsLoading(false);
      }
    };
    fetchChannels();
  }, []);

  return (
    <LiveTVContext.Provider value={{ channels, isLoading, error }}>
      {children}
    </LiveTVContext.Provider>
  );
}

export function useLiveTV() {
  return useContext(LiveTVContext);
} 