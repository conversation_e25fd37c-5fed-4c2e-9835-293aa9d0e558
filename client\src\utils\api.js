// Central API utility for Torvie
// Always attaches JWT token if present

// API base URL - automatically detects <PERSON><PERSON> vs local development
const getApiBaseUrl = () => {
  // In Docker, frontend runs on port 80, backend on port 3000
  // Use relative paths so nginx can proxy to backend
  if (window.location.port === '80' || window.location.port === '') {
    return ''; // Use relative paths in Docker
  }
  // Local development - backend typically runs on port 3000
  return 'http://localhost:3000';
};

export const API_BASE_URL = getApiBaseUrl();

export async function apiFetch(endpoint, options = {}) {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  const token = localStorage.getItem('torvie_jwt');
  const headers = {
    ...(options.headers || {}),
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  };
  return fetch(url, { ...options, headers });
} 