# Multi-Source Torrent Search Implementation

## Overview

The Torvie app now uses multiple torrent sources to provide comprehensive search results, prioritized by seeder count for the best streaming experience.

## Sources by Content Type

### 🎬 Movies
- **YTS** - Primary source for high-quality movie torrents
- **1337x** - Secondary source for additional movie options
- **The Pirate Bay** - Tertiary source for broader coverage

### 📺 TV Shows
- **EZTV** - Primary source for TV show torrents
- **1337x** - Secondary source for additional TV options
- **The Pirate Bay** - Tertiary source for broader coverage

### 🎌 Anime
- **Nyaa.si** - Primary source for anime torrents
- **1337x** - Secondary source for additional anime options
- **The Pirate Bay** - Tertiary source for broader coverage

## API Usage

### Search Endpoint
```
GET /api/search?query={search_term}&type={content_type}
```

### Parameters
- `query` (required): Search term
- `type` (optional): Content type filter
  - `movie` - Search movie sources (YTS, 1337x, TPB)
  - `tv` - Search TV sources (EZTV, 1337x, TPB)
  - `anime` - Search anime sources (Nyaa.si, 1337x, TPB)
  - `general` - Search all sources (default)

### Response Format
```json
{
  "success": true,
  "query": "search term",
  "results": [
    {
      "title": "Torrent Title",
      "magnetLink": "magnet:?xt=urn:btih:...",
      "seeders": 150,
      "leechers": 25,
      "size": "1.5 GB",
      "source": "YTS"
    }
  ],
  "total": 15,
  "sources": ["YTS", "1337x", "The Pirate Bay"]
}
```

## Features

### ✅ Prioritization by Seeders
- All results are sorted by seeder count (highest first)
- Ensures the best streaming quality and speed

### ✅ Duplicate Removal
- Automatic removal of duplicate torrents
- Based on normalized title comparison

### ✅ Error Handling
- Graceful handling of failed sources
- Continues search even if some sources are unavailable

### ✅ Content Type Filtering
- Smart filtering based on content type
- TV searches prioritize episode-based results
- Movie searches exclude TV show results

### ✅ Parallel Searching
- All sources are searched simultaneously
- Faster response times

## Implementation Details

### Search Flow
1. **Content Type Detection**: Determines which sources to use
2. **Parallel Search**: Searches all relevant sources simultaneously
3. **Result Aggregation**: Combines results from all sources
4. **Deduplication**: Removes duplicate torrents
5. **Sorting**: Sorts by seeder count (highest first)
6. **Response**: Returns top results

### Source Priority
Each content type uses the best sources for that type:
- **Movies**: YTS (quality) → 1337x (variety) → TPB (coverage)
- **TV**: EZTV (episodes) → 1337x (variety) → TPB (coverage)
- **Anime**: Nyaa.si (anime-focused) → 1337x (variety) → TPB (coverage)

### Error Resilience
- Uses `Promise.allSettled()` to handle failed sources
- Continues search even if some sources are down
- Logs errors for debugging without breaking the search

## Usage Examples

### Frontend Integration
```javascript
// Search for movies
const movieResponse = await fetch('/api/search?query=inception&type=movie');
const movieData = await movieResponse.json();

// Search for TV shows
const tvResponse = await fetch('/api/search?query=breaking bad&type=tv');
const tvData = await tvResponse.json();

// Search for anime
const animeResponse = await fetch('/api/search?query=naruto&type=anime');
const animeData = await animeResponse.json();
```

### Best Practices
1. **Always specify content type** for better results
2. **Use the first result** (highest seeders) for best streaming
3. **Check seeder count** before playing
4. **Handle empty results** gracefully

## Maintenance

### Adding New Sources
1. Create new source module in `server/services/`
2. Export search function
3. Import in `search.js`
4. Add to appropriate content type arrays
5. Update server startup message

### Monitoring
- Check server logs for source failures
- Monitor response times
- Track success rates per source

## Notes

- **TMDB Integration**: Display data (posters, titles, cast) still comes exclusively from TMDB
- **Torrent Data**: Only used for magnet links and streaming metadata
- **No Display Mixing**: Torrent data is never used for UI display elements 