version: '3.8'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - certbot-data:/var/www/certbot:ro
      - certbot-conf:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - backend
    networks:
      - torvie-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Let's Encrypt SSL Certificate Management
  certbot:
    image: certbot/certbot
    volumes:
      - certbot-data:/var/www/certbot
      - certbot-conf:/etc/letsencrypt
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d yourdomain.com -d www.yourdomain.com
    profiles:
      - ssl-setup

  # Frontend service (React + Nginx)
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    expose:
      - "80"
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://yourdomain.com/api
    networks:
      - torvie-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend service (Node.js + Express) - Optimized for CCX43
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET}
      - TMDB_API_KEY=${TMDB_API_KEY}
      - CORS_ORIGIN=https://yourdomain.com
      # WebTorrent optimizations for CCX43
      - WEBTORRENT_MAX_CONNS=50
      - WEBTORRENT_DHT_PORT=6881
      - DOWNLOAD_PATH=/app/downloads
      # Memory optimizations
      - NODE_OPTIONS=--max-old-space-size=8192
    volumes:
      # Persistent storage with optimized paths
      - torvie-storage:/app/storage
      - torvie-downloads:/app/downloads
      - torvie-logs:/app/logs
    networks:
      - torvie-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Resource limits optimized for CCX43
    deploy:
      resources:
        limits:
          memory: 16G
          cpus: '12'
        reservations:
          memory: 4G
          cpus: '4'

  # Redis for session management and caching (optional but recommended)
  redis:
    image: redis:7-alpine
    expose:
      - "6379"
    volumes:
      - redis-data:/data
    networks:
      - torvie-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - torvie-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Log aggregation
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    networks:
      - torvie-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  torvie-storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/torvie/storage
  torvie-downloads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/torvie/downloads
  torvie-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/torvie/logs
  certbot-data:
    driver: local
  certbot-conf:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  loki-data:
    driver: local

networks:
  torvie-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
