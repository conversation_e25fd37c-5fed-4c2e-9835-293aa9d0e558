import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { apiFetch } from '../utils/api';
import { debounce } from '../utils/performance';

const SmartSearchSuggestions = ({
  query = '',
  onSuggestionSelect,
  maxSuggestions = 8,
  showTrending = true,
  showRecent = true,
  showPersonalized = true,
  className = ''
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const [suggestions, setSuggestions] = useState([]);
  const [trendingSearches, setTrendingSearches] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [personalizedSuggestions, setPersonalizedSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const searchTimeoutRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Popular search terms for trending
  const popularTerms = [
    'action movies', 'comedy shows', 'horror films', 'romance movies',
    'sci-fi series', 'documentaries', 'anime', 'thriller movies',
    'drama series', 'family movies', 'adventure films', 'mystery shows',
    'superhero movies', 'fantasy series', 'war films', 'musical movies',
    'western films', 'crime shows', 'historical drama', 'sports movies'
  ];

  // Load trending searches
  const loadTrendingSearches = useCallback(async () => {
    try {
      // In a real app, this would come from your backend analytics
      const response = await apiFetch('/api/trending-searches');
      if (response.ok) {
        const data = await response.json();
        setTrendingSearches(data.trending || popularTerms.slice(0, 10));
      } else {
        // Fallback to popular terms
        setTrendingSearches(popularTerms.slice(0, 10));
      }
    } catch (error) {
      console.error('Error loading trending searches:', error);
      setTrendingSearches(popularTerms.slice(0, 10));
    }
  }, []);

  // Load recent searches
  const loadRecentSearches = useCallback(() => {
    try {
      const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
      setRecentSearches(recent.slice(0, 5));
    } catch (error) {
      console.error('Error loading recent searches:', error);
      setRecentSearches([]);
    }
  }, []);

  // Load personalized suggestions
  const loadPersonalizedSuggestions = useCallback(async () => {
    if (!user || !profile) return;

    try {
      // Get user's watch history and preferences
      const watchHistory = JSON.parse(localStorage.getItem('watchProgress') || '{}');
      const watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
      
      // Extract genres and titles from user's content
      const userGenres = new Set();
      const userTitles = new Set();
      
      Object.values(watchHistory).forEach(item => {
        if (item.genre_ids) {
          item.genre_ids.forEach(id => userGenres.add(id));
        }
        if (item.title) userTitles.add(item.title);
      });
      
      watchlist.forEach(item => {
        if (item.genre_ids) {
          item.genre_ids.forEach(id => userGenres.add(id));
        }
        if (item.title) userTitles.add(item.title);
      });

      // Generate personalized suggestions
      const personalized = [];
      
      // Genre-based suggestions
      const genreMap = {
        28: 'action movies',
        12: 'adventure films',
        16: 'animated movies',
        35: 'comedy shows',
        80: 'crime dramas',
        99: 'documentaries',
        18: 'drama series',
        10751: 'family movies',
        14: 'fantasy films',
        36: 'historical movies',
        27: 'horror films',
        10402: 'musical movies',
        9648: 'mystery shows',
        10749: 'romance movies',
        878: 'sci-fi series',
        10770: 'tv movies',
        53: 'thriller films',
        10752: 'war movies',
        37: 'western films'
      };

      userGenres.forEach(genreId => {
        const suggestion = genreMap[genreId];
        if (suggestion && !personalized.includes(suggestion)) {
          personalized.push(suggestion);
        }
      });

      // Similar content suggestions
      const similarTerms = [
        'movies like your favorites',
        'shows similar to what you watch',
        'recommended for you',
        'trending in your genres'
      ];

      personalized.push(...similarTerms);

      setPersonalizedSuggestions(personalized.slice(0, 6));
    } catch (error) {
      console.error('Error loading personalized suggestions:', error);
      setPersonalizedSuggestions([]);
    }
  }, [user, profile]);

  // Search for suggestions
  const searchSuggestions = useCallback(async (searchQuery) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    
    try {
      // Search movies and TV shows
      const [moviesResponse, tvResponse] = await Promise.all([
        apiFetch(`/api/search/movies?query=${encodeURIComponent(searchQuery)}&page=1`),
        apiFetch(`/api/search/tv?query=${encodeURIComponent(searchQuery)}&page=1`)
      ]);

      const movies = moviesResponse.ok ? (await moviesResponse.json()).results : [];
      const tvShows = tvResponse.ok ? (await tvResponse.json()).results : [];

      // Combine and format suggestions
      const allResults = [
        ...movies.map(item => ({
          id: item.id,
          title: item.title,
          type: 'movie',
          year: new Date(item.release_date).getFullYear(),
          rating: item.vote_average,
          poster: item.poster_path
        })),
        ...tvShows.map(item => ({
          id: item.id,
          title: item.name,
          type: 'tv',
          year: new Date(item.first_air_date).getFullYear(),
          rating: item.vote_average,
          poster: item.poster_path
        }))
      ];

      // Sort by relevance (rating and popularity)
      const sortedResults = allResults
        .sort((a, b) => (b.rating || 0) - (a.rating || 0))
        .slice(0, maxSuggestions);

      setSuggestions(sortedResults);
    } catch (error) {
      console.error('Error searching suggestions:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, [maxSuggestions]);

  // Debounced search
  const debouncedSearch = useCallback(
    debounce(searchSuggestions, 300),
    [searchSuggestions]
  );

  // Handle query changes
  useEffect(() => {
    if (query.trim()) {
      debouncedSearch(query);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [query, debouncedSearch]);

  // Load initial data
  useEffect(() => {
    loadTrendingSearches();
    loadRecentSearches();
    loadPersonalizedSuggestions();
  }, [loadTrendingSearches, loadRecentSearches, loadPersonalizedSuggestions]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!showSuggestions) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < suggestions.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && suggestions[selectedIndex]) {
            handleSuggestionSelect(suggestions[selectedIndex]);
          }
          break;
        case 'Escape':
          setShowSuggestions(false);
          setSelectedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showSuggestions, suggestions, selectedIndex]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    } else if (suggestion.id) {
      // Navigate to content
      const route = suggestion.type === 'movie' ? 'movie' : 'show';
      navigate(`/${route}/${suggestion.id}`);
    } else {
      // Search term
      navigate(`/search?q=${encodeURIComponent(suggestion)}`);
    }

    // Save to recent searches
    const searchTerm = suggestion.title || suggestion;
    const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    const updated = [searchTerm, ...recent.filter(term => term !== searchTerm)].slice(0, 10);
    localStorage.setItem('recentSearches', JSON.stringify(updated));

    setShowSuggestions(false);
    setSelectedIndex(-1);
  }, [onSuggestionSelect, navigate]);

  // Handle search term click
  const handleSearchTermClick = useCallback((term) => {
    navigate(`/search?q=${encodeURIComponent(term)}`);
    
    // Save to recent searches
    const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    const updated = [term, ...recent.filter(t => t !== term)].slice(0, 10);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
    
    setShowSuggestions(false);
  }, [navigate]);

  // Clear recent searches
  const clearRecentSearches = useCallback(() => {
    localStorage.removeItem('recentSearches');
    setRecentSearches([]);
  }, []);

  if (!showSuggestions && !query.trim()) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Search Suggestions */}
      {showSuggestions && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl z-50 max-h-96 overflow-y-auto"
        >
          {/* Loading State */}
          {isLoading && (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-400 text-sm mt-2">Searching...</p>
            </div>
          )}

          {/* Search Results */}
          {!isLoading && suggestions.length > 0 && (
            <div className="p-2">
              <h3 className="text-gray-400 text-xs font-semibold px-3 py-2 uppercase tracking-wide">
                Search Results
              </h3>
              {suggestions.map((suggestion, index) => (
                <div
                  key={`${suggestion.id}-${suggestion.type}`}
                  className={`flex items-center p-3 rounded-lg cursor-pointer transition-colors ${
                    index === selectedIndex
                      ? 'bg-blue-600 text-white'
                      : 'hover:bg-gray-800 text-gray-200'
                  }`}
                  onClick={() => handleSuggestionSelect(suggestion)}
                >
                  {/* Poster */}
                  {suggestion.poster && (
                    <img
                      src={`https://image.tmdb.org/t/p/w45${suggestion.poster}`}
                      alt={suggestion.title}
                      className="w-10 h-15 object-cover rounded mr-3"
                    />
                  )}
                  
                  {/* Content Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium truncate">{suggestion.title}</h4>
                      <span className="text-xs text-gray-400 ml-2">
                        {suggestion.year}
                      </span>
                    </div>
                    <div className="flex items-center text-xs text-gray-400">
                      <span className="capitalize">{suggestion.type}</span>
                      {suggestion.rating && (
                        <>
                          <span className="mx-2">•</span>
                          <span className="text-yellow-400">★ {suggestion.rating.toFixed(1)}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Trending Searches */}
          {!isLoading && showTrending && trendingSearches.length > 0 && (
            <div className="p-2 border-t border-gray-700">
              <h3 className="text-gray-400 text-xs font-semibold px-3 py-2 uppercase tracking-wide">
                🔥 Trending Searches
              </h3>
              <div className="flex flex-wrap gap-2 px-3 py-2">
                {trendingSearches.slice(0, 6).map((term, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearchTermClick(term)}
                    className="bg-gray-800 hover:bg-gray-700 text-gray-300 text-sm px-3 py-1 rounded-full transition-colors"
                  >
                    {term}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Recent Searches */}
          {!isLoading && showRecent && recentSearches.length > 0 && (
            <div className="p-2 border-t border-gray-700">
              <div className="flex items-center justify-between px-3 py-2">
                <h3 className="text-gray-400 text-xs font-semibold uppercase tracking-wide">
                  ⏰ Recent Searches
                </h3>
                <button
                  onClick={clearRecentSearches}
                  className="text-gray-500 hover:text-gray-300 text-xs"
                >
                  Clear
                </button>
              </div>
              <div className="flex flex-wrap gap-2 px-3 py-2">
                {recentSearches.map((term, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearchTermClick(term)}
                    className="bg-gray-800 hover:bg-gray-700 text-gray-300 text-sm px-3 py-1 rounded-full transition-colors"
                  >
                    {term}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Personalized Suggestions */}
          {!isLoading && showPersonalized && personalizedSuggestions.length > 0 && (
            <div className="p-2 border-t border-gray-700">
              <h3 className="text-gray-400 text-xs font-semibold px-3 py-2 uppercase tracking-wide">
                🎯 For You
              </h3>
              <div className="flex flex-wrap gap-2 px-3 py-2">
                {personalizedSuggestions.slice(0, 4).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearchTermClick(suggestion)}
                    className="bg-blue-900 hover:bg-blue-800 text-blue-200 text-sm px-3 py-1 rounded-full transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* No Results */}
          {!isLoading && suggestions.length === 0 && query.trim() && (
            <div className="p-4 text-center">
              <p className="text-gray-400">No results found for "{query}"</p>
              <p className="text-gray-500 text-sm mt-1">Try different keywords or check spelling</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SmartSearchSuggestions; 