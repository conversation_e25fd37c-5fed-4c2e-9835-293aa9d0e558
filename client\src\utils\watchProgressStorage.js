// Watch Progress Storage Utility for Torvie
// Manages saving and loading watch progress for users and profiles

import { apiFetch, API_BASE_URL } from './api.js';

const API_BASE = `${API_BASE_URL}/api/user-data`;

const buildHeaders = (extra = {}) => {
  const token = localStorage.getItem('torvie_jwt');
  if (token) {
    return { 'Content-Type': 'application/json', Authorization: `Bearer ${token}`, ...extra };
  }
  return { 'Content-Type': 'application/json', ...extra };
};

export class WatchProgressStorage {
  constructor() {
    this.isAvailable = this.checkAvailability();
  }

  async checkAvailability() {
    try {
      const response = await apiFetch('/api/health');
      return response.ok;
    } catch (error) {
      console.error('❌ Watch Progress Storage: Backend not available:', error);
      return false;
    }
  }

  // CRITICAL: Validate profile ID to ensure complete isolation
  validateProfileId(userId, profileId, operation) {
    if (!userId || !profileId) {
      console.error(`❌ PROFILE ISOLATION ERROR: Missing userId (${userId}) or profileId (${profileId}) for ${operation}`);
      return false;
    }
    
    // Ensure profile ID is a valid integer between 1-6
    const numericProfileId = parseInt(profileId);
    if (isNaN(numericProfileId) || numericProfileId < 1 || numericProfileId > 6) {
      console.error(`❌ PROFILE ISOLATION ERROR: Invalid profileId ${profileId} for ${operation}. Must be 1-6.`);
      return false;
    }
    
    console.log(`✅ PROFILE ISOLATION: Valid IDs for ${operation} - User: ${userId}, Profile: ${numericProfileId} (${typeof numericProfileId})`);
    return true;
  }

  // Save watch progress for a user/profile
  async saveProgress(userId, profileId, progressData) {
    if (!this.validateProfileId(userId, profileId, 'saveProgress')) {
      return false;
    }

    try {
      console.log(`💾 WATCH PROGRESS: Saving for user ${userId}, profile ${profileId} (TYPE: ${typeof profileId}):`, {
        title: progressData.title,
        contentId: progressData.contentId,
        watchedPercentage: Math.round(progressData.watchedPercentage)
      });
      
      const response = await fetch(`${API_BASE}/watch-progress/${userId}/${profileId}`, {
        method: 'POST',
        headers: buildHeaders(),
        body: JSON.stringify({
          contentId: progressData.contentId,
          contentType: progressData.contentType,
          title: progressData.title,
          poster: progressData.poster,
          currentTime: progressData.currentTime,
          duration: progressData.duration,
          watchedPercentage: progressData.watchedPercentage,
          lastWatched: progressData.lastWatched || new Date().toISOString(),
          isCompleted: progressData.isCompleted || false,
          episodeInfo: progressData.episodeInfo || null
        }),
      });

      const data = await response.json();
      if (data.success) {
        console.log(`✅ WATCH PROGRESS: Successfully saved for user ${userId}, profile ${profileId}: "${progressData.title}" at ${Math.round(progressData.watchedPercentage)}%`);
        return true;
      } else {
        console.error(`❌ WATCH PROGRESS: Failed to save for user ${userId}, profile ${profileId}:`, data.error);
        return false;
      }
    } catch (error) {
      console.error(`❌ WATCH PROGRESS: Error saving for user ${userId}, profile ${profileId}:`, error);
      return false;
    }
  }

  // Get all watch progress for a user/profile
  async getProgress(userId, profileId) {
    if (!this.validateProfileId(userId, profileId, 'getProgress')) {
      return {};
    }

    try {
      console.log(`📖 WATCH PROGRESS: Loading for user ${userId}, profile ${profileId} (TYPE: ${typeof profileId})`);
      
      const response = await fetch(`${API_BASE}/watch-progress/${userId}/${profileId}`, { headers: buildHeaders() });
      const data = await response.json();
      
      if (data.success) {
        const itemCount = Object.keys(data.watchProgress).length;
        console.log(`✅ WATCH PROGRESS: Loaded ${itemCount} entries for user ${userId}, profile ${profileId}`);
        
        // DEBUG: Log the actual items for this profile
        if (itemCount > 0) {
          console.log(`📋 WATCH PROGRESS ITEMS for Profile ${profileId}:`, Object.values(data.watchProgress).map(item => ({
            title: item.title,
            percentage: Math.round(item.watchedPercentage),
            lastWatched: new Date(item.lastWatched).toLocaleString()
          })));
        }
        
        return data.watchProgress;
      } else {
        console.error(`❌ WATCH PROGRESS: Failed to load for user ${userId}, profile ${profileId}:`, data.error);
        return {};
      }
    } catch (error) {
      console.error(`❌ WATCH PROGRESS: Error loading for user ${userId}, profile ${profileId}:`, error);
      return {};
    }
  }

  // Get progress for a specific content item
  async getContentProgress(userId, profileId, contentId) {
    if (!this.validateProfileId(userId, profileId, 'getContentProgress')) {
      return null;
    }

    try {
      const allProgress = await this.getProgress(userId, profileId);
      const contentProgress = allProgress[contentId] || null;
      
      if (contentProgress) {
        console.log(`📺 CONTENT PROGRESS: Found for user ${userId}, profile ${profileId}, content ${contentId}: ${Math.round(contentProgress.watchedPercentage)}%`);
      } else {
        console.log(`📺 CONTENT PROGRESS: None found for user ${userId}, profile ${profileId}, content ${contentId}`);
      }
      
      return contentProgress;
    } catch (error) {
      console.error(`❌ CONTENT PROGRESS: Error for user ${userId}, profile ${profileId}, content ${contentId}:`, error);
      return null;
    }
  }

  // Remove progress for a specific content item
  async removeProgress(userId, profileId, contentId) {
    if (!this.validateProfileId(userId, profileId, 'removeProgress')) {
      return false;
    }

    try {
      console.log(`🗑️ WATCH PROGRESS: Removing content ${contentId} for user ${userId}, profile ${profileId}`);
      
      const response = await fetch(`${API_BASE}/watch-progress/${userId}/${profileId}/${contentId}`, {
        method: 'DELETE',
        headers: buildHeaders(),
      });

      const data = await response.json();
      if (data.success) {
        console.log(`✅ WATCH PROGRESS: Successfully removed content ${contentId} for user ${userId}, profile ${profileId}`);
        return true;
      } else {
        console.error(`❌ WATCH PROGRESS: Failed to remove content ${contentId} for user ${userId}, profile ${profileId}:`, data.error);
        return false;
      }
    } catch (error) {
      console.error(`❌ WATCH PROGRESS: Error removing content ${contentId} for user ${userId}, profile ${profileId}:`, error);
      return false;
    }
  }

  // Get continue watching items (sorted by last watched)
  async getContinueWatching(userId, profileId, limit = 100) { // Increased from 20 to 100
    if (!this.validateProfileId(userId, profileId, 'getContinueWatching')) {
      return [];
    }

    try {
      console.log(`📺 CONTINUE WATCHING: Loading for user ${userId}, profile ${profileId} (TYPE: ${typeof profileId})`);
      
      const allProgress = await this.getProgress(userId, profileId);
      
      // DEBUG: Log all items before filtering
      const allProgressItems = Object.values(allProgress);
      console.log(`🔍 CONTINUE WATCHING FILTER DEBUG - All progress items for Profile ${profileId}:`, 
        allProgressItems.map(item => ({
          title: item.title,
          watchedPercentage: Math.round(item.watchedPercentage),
          isCompleted: item.isCompleted,
          willBeFiltered: item.isCompleted || item.watchedPercentage <= 0,
          reason: item.isCompleted ? 'marked as completed' : (item.watchedPercentage <= 0 ? 'no progress' : 'will show')
        }))
      );
      
      // Filter out completed items and sort by last watched
      const allItems = Object.values(allProgress)
        .filter(item => !item.isCompleted && item.watchedPercentage > 0) // Track from the very beginning
        .sort((a, b) => new Date(b.lastWatched) - new Date(a.lastWatched));
      
      const continueWatching = allItems.slice(0, limit);
      
      console.log(`📺 CONTINUE WATCHING: Found ${continueWatching.length} of ${allItems.length} total items for user ${userId}, profile ${profileId}`);
      
      // Log if items are being limited
      if (allItems.length > limit) {
        console.warn(`⚠️ CONTINUE WATCHING: ${allItems.length - limit} items hidden due to display limit of ${limit}`);
      }
      
      // DEBUG: Log continue watching items for this specific profile
      if (continueWatching.length > 0) {
        console.log(`📋 CONTINUE WATCHING ITEMS for Profile ${profileId}:`, continueWatching.map(item => ({
          title: item.title,
          percentage: Math.round(item.watchedPercentage),
          lastWatched: new Date(item.lastWatched).toLocaleString()
        })));
      } else {
        console.log(`📋 CONTINUE WATCHING: No items for Profile ${profileId}`);
      }
      
      return continueWatching;
    } catch (error) {
      console.error(`❌ CONTINUE WATCHING: Error for user ${userId}, profile ${profileId}:`, error);
      return [];
    }
  }

  // Check if content should be marked as completed
  shouldMarkAsCompleted(currentTime, duration) {
    if (!duration || duration <= 0) return false;
    const percentage = (currentTime / duration) * 100;
    return percentage >= 98; // Mark as completed if watched 98% or more
  }

  // Calculate watch percentage
  calculateWatchPercentage(currentTime, duration) {
    if (!duration || duration <= 0) return 0;
    return Math.min(100, Math.max(0, (currentTime / duration) * 100));
  }

  // Format time for display (e.g., "1h 23m left")
  formatTimeRemaining(currentTime, duration) {
    if (!duration || duration <= 0) return '';
    
    const remaining = Math.max(0, duration - currentTime);
    const hours = Math.floor(remaining / 3600);
    const minutes = Math.floor((remaining % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m left`;
    } else if (minutes > 0) {
      return `${minutes}m left`;
    } else {
      return 'Almost finished';
    }
  }

  // Format watch percentage for display
  formatWatchPercentage(percentage) {
    return `${Math.round(percentage)}% watched`;
  }
}

// Create and export a singleton instance
export const watchProgressStorage = new WatchProgressStorage(); 

// Test backend connectivity
export async function testBackendConnectivity() {
  try {
    const response = await apiFetch('/api/health');
    return response.ok;
  } catch (error) {
    console.error('Backend connectivity test failed:', error);
    return false;
  }
} 