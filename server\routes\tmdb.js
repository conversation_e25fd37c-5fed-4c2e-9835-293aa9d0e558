import express from 'express';
import fetch from 'node-fetch';
import NodeCache from 'node-cache';
import { asyncHandler } from '../middleware/asyncHandler.js';

const TMDB_BASE_URL = 'https://api.themoviedb.org/3';

const cache = new NodeCache({ stdTTL: 600, checkperiod: 120 });

async function fetchWithCache(cacheKey, url) {
  const cached = cache.get(cacheKey);
  if (cached) return cached;
  const resp = await fetch(url);
  if (!resp.ok) throw new Error(`TMDB API error: ${resp.status} ${resp.statusText}`);
  const data = await resp.json();
  cache.set(cacheKey, data);
  return data;
}

const router = express.Router();

// Paged helpers ------------------------------------------------------------
function pagedRoute(path, buildUrlFn, cachePrefix) {
  router.get(path, asyncHandler(async (req, res) => {
    const TMDB_API_KEY = process.env.TMDB_API_KEY;
    if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
    const page = parseInt(req.query.page) || 1;
    const url = buildUrlFn(page, TMDB_API_KEY);
    const data = await fetchWithCache(`${cachePrefix}_${page}`, url);
    res.json(data);
  }));
}

// Trending & Top-Rated ------------------------------------------------------
pagedRoute('/trending/movies', (p, key) => `${TMDB_BASE_URL}/trending/movie/week?api_key=${key}&page=${p}`, 'trending_movies');
pagedRoute('/top-rated/movies', (p, key) => `${TMDB_BASE_URL}/movie/top_rated?api_key=${key}&page=${p}`, 'top_rated_movies');
pagedRoute('/trending/tv', (p, key) => `${TMDB_BASE_URL}/trending/tv/week?api_key=${key}&page=${p}`, 'trending_tv');
pagedRoute('/top-rated/tv', (p, key) => `${TMDB_BASE_URL}/tv/top_rated?api_key=${key}&page=${p}`, 'top_rated_tv');

// Person details -----------------------------------------------------------
router.get('/person/:id', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
  const { id } = req.params;
  const url = `${TMDB_BASE_URL}/person/${id}?api_key=${TMDB_API_KEY}&append_to_response=movie_credits,tv_credits,combined_credits`;
  const data = await fetchWithCache(`person_${id}`, url);
  res.json(data);
}));

// Movie details ------------------------------------------------------------
router.get('/movies/:id', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
  const { id } = req.params;
  const url = `${TMDB_BASE_URL}/movie/${id}?api_key=${TMDB_API_KEY}`;
  const data = await fetchWithCache(`movie_${id}`, url);
  res.json(data);
}));

// Movie sub-resources helper
function movieSubResource(subPath) {
  router.get(`/movies/:id/${subPath}`, asyncHandler(async (req, res) => {
    const TMDB_API_KEY = process.env.TMDB_API_KEY;
    if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
    const { id } = req.params;
    const url = `${TMDB_BASE_URL}/movie/${id}/${subPath}?api_key=${TMDB_API_KEY}`;
    const data = await fetchWithCache(`movie_${id}_${subPath}`, url);
    res.json(data);
  }));
}
['credits', 'reviews', 'similar', 'videos'].forEach(movieSubResource);

// Genre lists --------------------------------------------------------------
router.get('/genre/movie/list', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
  const url = `${TMDB_BASE_URL}/genre/movie/list?api_key=${TMDB_API_KEY}&language=en-US`;
  const data = await fetchWithCache('genre_movie_list', url);
  res.json(data);
}));

router.get('/genre/tv/list', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });
  const url = `${TMDB_BASE_URL}/genre/tv/list?api_key=${TMDB_API_KEY}&language=en-US`;
  const data = await fetchWithCache('genre_tv_list', url);
  res.json(data);
}));

// Unified genres endpoint for legacy front-end compatibility
router.get('/genres', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });

  const type = req.query.type === 'tv' ? 'tv' : 'movie';
  const cacheKey = `genre_${type}_list`; // reuse existing cache
  const url = `${TMDB_BASE_URL}/genre/${type}/list?api_key=${TMDB_API_KEY}&language=en-US`;
  const data = await fetchWithCache(cacheKey, url);
  res.json(data);
}));

// Anime trending (Nyaa + TMDB) --------------------------------------------
router.get('/trending/anime', asyncHandler(async (req, res) => {
  const TMDB_API_KEY = process.env.TMDB_API_KEY;
  if (!TMDB_API_KEY) return res.status(500).json({ error: 'TMDB API key not configured' });

  const { getTrendingAnime } = await import('../services/nyaa.js');
  const nyaaAnime = await getTrendingAnime();

  const tmdbAnime = [];
  const processing = nyaaAnime.slice(0, 10).map(async (anime) => {
    const searchUrl = `${TMDB_BASE_URL}/search/tv?api_key=${TMDB_API_KEY}&query=${encodeURIComponent(anime.name)}&include_adult=false`;
    const result = await fetchWithCache(`anime_${anime.name}`, searchUrl);
    const bestMatch = result.results?.filter(r => r.poster_path && r.backdrop_path)
      .sort((a, b) => (b.vote_count || 0) - (a.vote_count || 0))[0];
    if (bestMatch) return bestMatch;
    return null;
  });

  const matches = (await Promise.all(processing)).filter(Boolean);

  // Fallback popular anime if few matches
  if (matches.length < 5) {
    const fallbackUrl = `${TMDB_BASE_URL}/discover/tv?api_key=${TMDB_API_KEY}&with_genres=16&with_origin_country=JP&sort_by=popularity.desc&page=1`;
    const fallbackData = await fetchWithCache('fallback_anime', fallbackUrl);
    matches.push(...(fallbackData.results || []).slice(0, 10 - matches.length));
  }

  res.json({ page: 1, results: matches, total_pages: 1, total_results: matches.length });
}));

export default router; 