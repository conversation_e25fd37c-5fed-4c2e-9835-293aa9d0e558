import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import OptimizedMediaCard from './OptimizedMediaCard';

const VirtualizedMediaList = ({
  items = [],
  columns = 6,
  gap = 4,
  itemHeight = 400,
  itemWidth = 200,
  className = '',
  onItemClick,
  onPlay,
  onTrailer,
  showGenre = true,
  showRating = true,
  showYear = true,
  showWatchlistButton = true,
  showPlayButton = true,
  showTrailerButton = true,
  priorityRange = 12 // Number of items to load immediately (above the fold)
}) => {
  const containerRef = useRef(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);

  // Calculate dimensions
  const rowHeight = itemHeight + gap;
  const totalRows = Math.ceil(items.length / columns);
  const totalHeight = totalRows * rowHeight;

  // Calculate visible range
  const visibleStart = Math.max(0, Math.floor(scrollTop / rowHeight) - 1);
  const visibleEnd = Math.min(
    totalRows - 1,
    Math.ceil((scrollTop + containerHeight) / rowHeight) + 1
  );

  // Get visible items
  const visibleItems = useMemo(() => {
    const startIndex = visibleStart * columns;
    const endIndex = Math.min(items.length, (visibleEnd + 1) * columns);
    return items.slice(startIndex, endIndex);
  }, [items, visibleStart, visibleEnd, columns]);

  // Calculate item positions
  const getItemStyle = useCallback((index) => {
    const row = Math.floor(index / columns);
    const col = index % columns;
    const top = row * rowHeight;
    const left = col * (itemWidth + gap);
    
    return {
      position: 'absolute',
      top: `${top}px`,
      left: `${left}px`,
      width: `${itemWidth}px`,
      height: `${itemHeight}px`
    };
  }, [columns, rowHeight, itemWidth, gap, itemHeight]);

  // Handle scroll
  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  // Resize observer
  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    
    const updateDimensions = () => {
      setContainerHeight(container.clientHeight);
      setContainerWidth(container.clientWidth);
    };

    updateDimensions();

    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Auto-adjust columns based on container width
  const adjustedColumns = useMemo(() => {
    if (containerWidth === 0) return columns;
    
    const availableWidth = containerWidth - gap;
    const calculatedColumns = Math.floor(availableWidth / (itemWidth + gap));
    return Math.max(1, Math.min(calculatedColumns, columns));
  }, [containerWidth, itemWidth, gap, columns]);

  // Adjust item width to fit container
  const adjustedItemWidth = useMemo(() => {
    if (adjustedColumns === 1) return itemWidth;
    
    const availableWidth = containerWidth - (gap * (adjustedColumns - 1));
    return availableWidth / adjustedColumns;
  }, [containerWidth, adjustedColumns, gap, itemWidth]);

  // Recalculate positions with adjusted dimensions
  const getAdjustedItemStyle = useCallback((index) => {
    const row = Math.floor(index / adjustedColumns);
    const col = index % adjustedColumns;
    const top = row * rowHeight;
    const left = col * (adjustedItemWidth + gap);
    
    return {
      position: 'absolute',
      top: `${top}px`,
      left: `${left}px`,
      width: `${adjustedItemWidth}px`,
      height: `${itemHeight}px`
    };
  }, [adjustedColumns, rowHeight, adjustedItemWidth, gap, itemHeight]);

  // Calculate visible range with adjusted columns
  const adjustedVisibleItems = useMemo(() => {
    const startIndex = visibleStart * adjustedColumns;
    const endIndex = Math.min(items.length, (visibleEnd + 1) * adjustedColumns);
    return items.slice(startIndex, endIndex);
  }, [items, visibleStart, visibleEnd, adjustedColumns]);

  // Calculate adjusted total height
  const adjustedTotalHeight = useMemo(() => {
    const adjustedTotalRows = Math.ceil(items.length / adjustedColumns);
    return adjustedTotalRows * rowHeight;
  }, [items.length, adjustedColumns, rowHeight]);

  if (items.length === 0) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <p className="text-gray-400">No items found</p>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`relative overflow-auto ${className}`}
      onScroll={handleScroll}
      style={{ height: '100%' }}
    >
      {/* Scrollable container with total height */}
      <div style={{ height: `${adjustedTotalHeight}px`, position: 'relative' }}>
        {/* Visible items */}
        {adjustedVisibleItems.map((item, index) => {
          const globalIndex = visibleStart * adjustedColumns + index;
          const isPriority = globalIndex < priorityRange;
          
          return (
            <div
              key={`${item.id}-${item.media_type}-${globalIndex}`}
              style={getAdjustedItemStyle(globalIndex)}
            >
              <OptimizedMediaCard
                item={item}
                showGenre={showGenre}
                showRating={showRating}
                showYear={showYear}
                showWatchlistButton={showWatchlistButton}
                showPlayButton={showPlayButton}
                showTrailerButton={showTrailerButton}
                onCardClick={onItemClick}
                onPlay={onPlay}
                onTrailer={onTrailer}
                priority={isPriority}
                className="w-full h-full"
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Responsive wrapper component
const ResponsiveVirtualizedMediaList = ({
  items = [],
  className = '',
  onItemClick,
  onPlay,
  onTrailer,
  showGenre = true,
  showRating = true,
  showYear = true,
  showWatchlistButton = true,
  showPlayButton = true,
  showTrailerButton = true,
  priorityRange = 12
}) => {
  // Responsive breakpoints
  const breakpoints = {
    sm: { columns: 2, itemWidth: 150, itemHeight: 300 },
    md: { columns: 4, itemWidth: 180, itemHeight: 350 },
    lg: { columns: 6, itemWidth: 200, itemHeight: 400 },
    xl: { columns: 8, itemWidth: 220, itemHeight: 450 }
  };

  return (
    <div className={`w-full h-full ${className}`}>
      {/* Mobile */}
      <div className="block sm:hidden">
        <VirtualizedMediaList
          items={items}
          columns={breakpoints.sm.columns}
          itemWidth={breakpoints.sm.itemWidth}
          itemHeight={breakpoints.sm.itemHeight}
          gap={3}
          onItemClick={onItemClick}
          onPlay={onPlay}
          onTrailer={onTrailer}
          showGenre={showGenre}
          showRating={showRating}
          showYear={showYear}
          showWatchlistButton={showWatchlistButton}
          showPlayButton={showPlayButton}
          showTrailerButton={showTrailerButton}
          priorityRange={priorityRange}
        />
      </div>

      {/* Tablet */}
      <div className="hidden sm:block md:hidden">
        <VirtualizedMediaList
          items={items}
          columns={breakpoints.md.columns}
          itemWidth={breakpoints.md.itemWidth}
          itemHeight={breakpoints.md.itemHeight}
          gap={4}
          onItemClick={onItemClick}
          onPlay={onPlay}
          onTrailer={onTrailer}
          showGenre={showGenre}
          showRating={showRating}
          showYear={showYear}
          showWatchlistButton={showWatchlistButton}
          showPlayButton={showPlayButton}
          showTrailerButton={showTrailerButton}
          priorityRange={priorityRange}
        />
      </div>

      {/* Desktop */}
      <div className="hidden md:block lg:hidden">
        <VirtualizedMediaList
          items={items}
          columns={breakpoints.lg.columns}
          itemWidth={breakpoints.lg.itemWidth}
          itemHeight={breakpoints.lg.itemHeight}
          gap={4}
          onItemClick={onItemClick}
          onPlay={onPlay}
          onTrailer={onTrailer}
          showGenre={showGenre}
          showRating={showRating}
          showYear={showYear}
          showWatchlistButton={showWatchlistButton}
          showPlayButton={showPlayButton}
          showTrailerButton={showTrailerButton}
          priorityRange={priorityRange}
        />
      </div>

      {/* Large Desktop */}
      <div className="hidden lg:block">
        <VirtualizedMediaList
          items={items}
          columns={breakpoints.xl.columns}
          itemWidth={breakpoints.xl.itemWidth}
          itemHeight={breakpoints.xl.itemHeight}
          gap={4}
          onItemClick={onItemClick}
          onPlay={onPlay}
          onTrailer={onTrailer}
          showGenre={showGenre}
          showRating={showRating}
          showYear={showYear}
          showWatchlistButton={showWatchlistButton}
          showPlayButton={showPlayButton}
          showTrailerButton={showTrailerButton}
          priorityRange={priorityRange}
        />
      </div>
    </div>
  );
};

export default ResponsiveVirtualizedMediaList; 