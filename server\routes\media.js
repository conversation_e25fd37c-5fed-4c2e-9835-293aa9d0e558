import express from 'express';
import axios from 'axios';
// import { startStream } from '../services/stream.js'; // Uncomment and adjust if you have this
// import { searchTorrentsYTS } from '../services/yts.js'; // Uncomment and adjust if you have this

const router = express.Router();

// --- Helper function to search TMDB for movie details and trailer ---
async function getMovieAndTrailerFromTMDB(title, year, TMDB_API_KEY, TMDB_BASE_URL) {
    try {
        const searchResponse = await axios.get(`${TMDB_BASE_URL}/search/movie`, {
            params: {
                api_key: TMDB_API_KEY,
                query: title,
                year: year,
                language: 'en-US'
            }
        });
        const movie = searchResponse.data.results[0];
        if (!movie) {
            console.log(`TMDB: No movie found for "${title}" (${year}).`);
            return null;
        }
        const videosResponse = await axios.get(`${TMDB_BASE_URL}/movie/${movie.id}/videos`, {
            params: {
                api_key: TMDB_API_KEY,
                language: 'en-US'
            }
        });
        const trailer = videosResponse.data.results.find(
            video => video.site === 'YouTube' && video.type === 'Trailer' && video.official
        );
        return {
            id: movie.id,
            title: movie.title,
            release_date: movie.release_date,
            overview: movie.overview,
            poster_path: movie.poster_path ? `https://image.tmdb.org/t/p/w500${movie.poster_path}` : null,
            trailer_url: trailer ? `https://www.youtube.com/watch?v=${trailer.key}` : null,
            imdb_id: movie.imdb_id
        };
    } catch (error) {
        console.error(`Error fetching movie/trailer from TMDB for "${title}" (${year}):`, error.message);
        return null;
    }
}

// --- NEW ROUTE: /api/media/play ---
router.post('/play', async (req, res) => {
    const { title, year, mediaType } = req.body;
    if (!title || !mediaType) {
        return res.status(400).json({ success: false, message: 'Movie title and media type are required.' });
    }
    try {
        const movieDetails = await getMovieAndTrailerFromTMDB(title, year, req.TMDB_API_KEY, req.TMDB_BASE_URL);
        if (!movieDetails) {
            return res.status(404).json({ success: false, message: `Could not find movie details for "${title}" (${year}).` });
        }
        if (mediaType === 'trailer') {
            if (movieDetails.trailer_url) {
                return res.json({
                    success: true,
                    type: 'trailer',
                    url: movieDetails.trailer_url,
                    title: movieDetails.title,
                    poster: movieDetails.poster_path
                });
            } else {
                return res.status(404).json({ success: false, message: `No official trailer found for "${movieDetails.title}".` });
            }
        } else if (mediaType === 'movie') {
            // Uncomment and implement this block if you have YTS/torrent streaming
            // const torrentSearchResults = await searchTorrentsYTS(movieDetails.title, movieDetails.year);
            // if (!torrentSearchResults || torrentSearchResults.length === 0) {
            //     return res.status(404).json({ success: false, message: `No torrents found for "${movieDetails.title}".` });
            // }
            // const bestTorrent = torrentSearchResults.find(t => t.magnet);
            // if (!bestTorrent || !bestTorrent.magnet) {
            //     return res.status(404).json({ success: false, message: `No streamable torrents found for "${movieDetails.title}".` });
            // }
            // const streamInfo = await startStream(bestTorrent.magnet);
            // return res.json({
            //     success: true,
            //     type: 'movie',
            //     url: streamInfo.filePath,
            //     title: streamInfo.fileName,
            //     poster: movieDetails.poster_path
            // });
            return res.status(501).json({ success: false, message: 'Movie streaming not yet implemented in this endpoint.' });
        } else {
            return res.status(400).json({ success: false, message: 'Invalid media type specified. Must be "trailer" or "movie".' });
        }
    } catch (error) {
        console.error(`Server error during media playback request for "${title}":`, error);
        res.status(500).json({ success: false, message: 'An internal server error occurred.' });
    }
});

// --- NEW ROUTE: /api/genre-tvshows/:genreId ---
router.get('/genre-tvshows/:genreId', async (req, res) => {
    const { genreId } = req.params;
    const page = req.query.page || 1;
    if (!req.TMDB_API_KEY) {
        return res.status(500).json({ success: false, message: 'TMDB API key not set.' });
    }
    try {
        console.log(`Fetching TV shows for genre ${genreId}, page ${page} from TMDB...`);
        const tmdbRes = await axios.get(`${req.TMDB_BASE_URL}/discover/tv`, {
            params: {
                api_key: req.TMDB_API_KEY,
                with_genres: genreId,
                sort_by: 'popularity.desc',
                page,
                language: 'en-US',
                include_adult: false
            }
        });
        const shows = (tmdbRes.data.results || []).map(show => ({
            id: show.id,
            name: show.name,
            overview: show.overview,
            poster_path: show.poster_path,
            genre_ids: show.genre_ids,
            popularity: show.popularity,
            vote_average: show.vote_average,
            vote_count: show.vote_count,
            first_air_date: show.first_air_date,
            origin_country: show.origin_country,
            backdrop_path: show.backdrop_path
        }));
        console.log(`Genre ${genreId} returned ${shows.length} shows (page ${page})`);
        res.json({ shows });
    } catch (error) {
        console.error(`Error fetching TV shows for genre ${genreId}:`, error.message);
        res.status(500).json({ success: false, message: 'Failed to fetch TV shows.' });
    }
});

// --- NEW ROUTE: /api/media/genre-movies/:genreId ---
router.get('/genre-movies/:genreId', async (req, res) => {
    const { genreId } = req.params;
    const page = req.query.page || 1;
    if (!req.TMDB_API_KEY) {
        return res.status(500).json({ success: false, message: 'TMDB API key not set.' });
    }
    try {
        console.log(`Fetching movies for genre ${genreId}, page ${page} from TMDB...`);
        const tmdbRes = await axios.get(`${req.TMDB_BASE_URL}/discover/movie`, {
            params: {
                api_key: req.TMDB_API_KEY,
                with_genres: genreId,
                sort_by: 'popularity.desc',
                page,
                language: 'en-US',
                include_adult: false
            }
        });
        const movies = (tmdbRes.data.results || []).map(movie => ({
            id: movie.id,
            title: movie.title,
            overview: movie.overview,
            poster_path: movie.poster_path,
            genre_ids: movie.genre_ids,
            popularity: movie.popularity,
            vote_average: movie.vote_average,
            vote_count: movie.vote_count,
            release_date: movie.release_date,
            backdrop_path: movie.backdrop_path
        }));
        console.log(`Genre ${genreId} returned ${movies.length} movies (page ${page})`);
        res.json({ movies });
    } catch (error) {
        console.error(`Error fetching movies for genre ${genreId}:`, error.message);
        res.status(500).json({ success: false, message: 'Failed to fetch movies.' });
    }
});

export default router; 