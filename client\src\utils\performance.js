/**
 * Performance Optimization Utilities
 * Handles lazy loading, caching, and resource optimization
 */

// Image lazy loading with intersection observer
class ImageLazyLoader {
  constructor() {
    this.observer = null;
    this.imageCache = new Map();
    this.initObserver();
  }

  initObserver() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadImage(entry.target);
              this.observer.unobserve(entry.target);
            }
          });
        },
        {
          rootMargin: '50px 0px', // Start loading 50px before image enters viewport
          threshold: 0.01
        }
      );
    }
  }

  loadImage(imgElement) {
    const src = imgElement.dataset.src;
    if (!src) return;

    // Check cache first
    if (this.imageCache.has(src)) {
      imgElement.src = this.imageCache.get(src);
      imgElement.classList.remove('lazy');
      return;
    }

    // Create a new image to preload
    const tempImg = new Image();
    tempImg.onload = () => {
      imgElement.src = src;
      imgElement.classList.remove('lazy');
      this.imageCache.set(src, src);
    };
    tempImg.onerror = () => {
      // Fallback to placeholder
      imgElement.src = imgElement.dataset.fallback || '/placeholder.jpg';
      imgElement.classList.remove('lazy');
    };
    tempImg.src = src;
  }

  observe(imgElement) {
    if (this.observer) {
      this.observer.observe(imgElement);
    } else {
      // Fallback for browsers without IntersectionObserver
      this.loadImage(imgElement);
    }
  }

  unobserve(imgElement) {
    if (this.observer) {
      this.observer.unobserve(imgElement);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// API response caching
class APICache {
  constructor() {
    this.cache = new Map();
    this.maxAge = 5 * 60 * 1000; // 5 minutes default
  }

  set(key, data, maxAge = this.maxAge) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      maxAge
    });
  }

  get(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > cached.maxAge;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  clear() {
    this.cache.clear();
  }

  clearExpired() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > value.maxAge) {
        this.cache.delete(key);
      }
    }
  }
}

// Resource preloader
class ResourcePreloader {
  constructor() {
    this.preloaded = new Set();
  }

  preloadImage(src) {
    if (this.preloaded.has(src)) return Promise.resolve();
    
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.preloaded.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }

  preloadImages(srcs) {
    return Promise.allSettled(srcs.map(src => this.preloadImage(src)));
  }

  preloadCSS(href) {
    if (this.preloaded.has(href)) return Promise.resolve();
    
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.onload = () => {
        this.preloaded.add(href);
        resolve();
      };
      link.onerror = reject;
      document.head.appendChild(link);
    });
  }

  preloadJS(src) {
    if (this.preloaded.has(src)) return Promise.resolve();
    
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        this.preloaded.add(src);
        resolve();
      };
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
}

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
  }

  startTimer(name) {
    this.metrics.set(name, {
      start: performance.now(),
      end: null,
      duration: null
    });
  }

  endTimer(name) {
    const metric = this.metrics.get(name);
    if (!metric) return null;

    metric.end = performance.now();
    metric.duration = metric.end - metric.start;
    return metric.duration;
  }

  measureTime(name, fn) {
    this.startTimer(name);
    const result = fn();
    this.endTimer(name);
    return result;
  }

  async measureTimeAsync(name, fn) {
    this.startTimer(name);
    const result = await fn();
    this.endTimer(name);
    return result;
  }

  getMetrics() {
    return Object.fromEntries(this.metrics);
  }

  observeWebVitals() {
    if ('PerformanceObserver' in window) {
      // Observe Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.set('LCP', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Observe First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.metrics.set('FID', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Observe Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.set('CLS', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }
}

// Debounce utility
function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

// Throttle utility
function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memory management
class MemoryManager {
  constructor() {
    this.cleanupInterval = null;
    this.startCleanup();
  }

  startCleanup() {
    // Run cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  cleanup() {
    // Clear expired cache entries
    if (window.apiCache) {
      window.apiCache.clearExpired();
    }

    // Clear old image cache entries (keep last 100)
    if (window.imageLoader && window.imageLoader.imageCache.size > 100) {
      const entries = Array.from(window.imageLoader.imageCache.entries());
      const toDelete = entries.slice(0, entries.length - 100);
      toDelete.forEach(([key]) => window.imageLoader.imageCache.delete(key));
    }

    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// Initialize performance utilities
const imageLoader = new ImageLazyLoader();
const apiCache = new APICache();
const resourcePreloader = new ResourcePreloader();
const performanceMonitor = new PerformanceMonitor();
const memoryManager = new MemoryManager();

// Make utilities globally available
window.imageLoader = imageLoader;
window.apiCache = apiCache;
window.resourcePreloader = resourcePreloader;
window.performanceMonitor = performanceMonitor;

// Start performance monitoring
performanceMonitor.observeWebVitals();

// Export utilities
export {
  imageLoader,
  apiCache,
  resourcePreloader,
  performanceMonitor,
  memoryManager,
  debounce,
  throttle
};

// React hook for lazy loading images (requires React import in component)
export const useLazyImage = (src, fallback = '/placeholder.jpg') => {
  // This hook should be used in a React component with proper React import
  // Example usage:
  // import React from 'react';
  // const { imgRef, isLoaded } = useLazyImage(src, fallback);
  
  return {
    imgRef: null,
    isLoaded: false,
    // Implementation would go here with proper React import
  };
}; 