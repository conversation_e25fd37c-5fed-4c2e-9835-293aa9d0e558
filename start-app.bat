@echo off
echo Starting Torvie Application...
echo.

echo Starting Backend Server...
start "Torvie Server" cmd /k "cd server && npm start"

echo Waiting for server to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend Client...
start "Torvie Client" cmd /k "cd client && npm run dev"

echo.
echo Both servers are starting...
echo Backend: http://localhost:3002
echo Frontend: http://localhost:3000
echo.
echo Waiting for frontend to be ready...
timeout /t 8 /nobreak > nul

echo Opening Torvie in your default browser...
start http://localhost:3000

echo.
echo ✅ Torvie is now running!
echo 🌐 Frontend: http://localhost:3000 (opened automatically)
echo 🚀 Backend: http://localhost:3002
echo.
echo Keep this window open to see server status.
echo Press any key to close this window and stop all servers...
pause > nul

echo Stopping servers...
taskkill /f /im node.exe > nul 2>&1 