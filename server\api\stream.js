import express from 'express';
import { startStream, getFileStream, getSubtitleFile, getAvailableSubtitles, activeTorrents } from '../services/stream.js';

const router = express.Router();

// --- Route to INITIALIZE a torrent stream ---
router.post('/start', async (req, res) => {
    const { magnetLink } = req.body;
    if (!magnetLink) {
        return res.status(400).json({ success: false, message: 'Magnet link is required.' });
    }

    try {
        const streamInfo = await startStream(magnetLink);
        res.json({ success: true, ...streamInfo });
    } catch (error) {
        console.error(`Stream start failed for magnet: ${magnetLink}`, error.message);
        res.status(500).json({ success: false, message: error.message || 'Failed to start stream.' });
    }
});

// --- Route to SERVE the actual video content ---
router.get('/:infoHash/*', (req, res) => {
    console.log(`\n--- Stream GET Route Hit! ---`);
    console.log(`Received request for: ${req.url}`);
    const { infoHash } = req.params;
    const filePath = req.params[0];
    const range = req.headers.range;

    // Always ignore conditional headers for video streaming
    delete req.headers['if-none-match'];
    delete req.headers['if-modified-since'];

    console.log(`\n--- Stream Request Received ---`);
    console.log(`InfoHash: ${infoHash}`);
    console.log(`File Path: ${filePath}`);
    console.log(`Range Header: ${range}`);
    console.log(`User Agent: ${req.headers['user-agent']}`);
    
    // Range header is not always required - browsers can request without range on first load
    if (!range) {
        console.log("No range header - serving entire file");
    }

    const streamDetails = getFileStream(infoHash, filePath, range);

    if (!streamDetails) {
        console.error(`Stream lookup failed: No active stream found for infoHash "${infoHash}" and filePath "${filePath}".`);
        return res.status(404).send('Stream not found. It may be stopped or still buffering.');
    }

    if (streamDetails.error) {
        console.error(`Stream error: ${streamDetails.error}`);
        return res.status(500).json({ success: false, message: streamDetails.error });
    }

    const { stream, start, end, fileSize, chunksize, mimeType } = streamDetails;
    
    const headers = {
        'Accept-Ranges': 'bytes',
        'Content-Type': mimeType || 'video/mp4',
        'Connection': 'keep-alive',
        // Optimize for streaming: allow some caching but prioritize freshness
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
        // Remove strict no-cache directives that can slow down streaming
        'X-Content-Type-Options': 'nosniff',
        // Add streaming-specific headers
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Range, Content-Type',
        'Access-Control-Expose-Headers': 'Content-Range, Accept-Ranges, Content-Length'
    };

    // Set appropriate status code and headers based on whether range was requested
    if (range) {
        headers['Content-Range'] = `bytes ${start}-${end}/${fileSize}`;
        headers['Content-Length'] = chunksize;
        console.log('Serving video PARTIAL CONTENT with headers:', headers);
        res.writeHead(206, headers); // Partial Content
    } else {
        headers['Content-Length'] = fileSize;
        console.log('Serving video FULL CONTENT with headers:', headers);
        res.writeHead(200, headers); // OK
    }
    
    console.log(`-----------------------------\n`);

    // Robust error handling for stream disconnects
    stream.pipe(res);

    // Handle errors on the stream (e.g., client disconnects, premature close)
    stream.on('error', (err) => {
        if (err.message && err.message.includes('prematurely')) {
            console.warn(`Stream closed prematurely for ${filePath}`);
        } else {
            console.error(`Streaming error for ${filePath}:`, err);
        }
        // Ensure response is closed
        if (!res.headersSent) {
            res.status(500).end('Stream error');
        } else {
            res.end();
        }
    });

    // If the client disconnects, destroy the stream
    res.on('close', () => {
        stream.destroy();
    });
});

// NEW: Route to serve subtitle files
router.get('/subtitles/:infoHash/*', (req, res) => {
    console.log(`\n--- Subtitle Request ---`);
    const { infoHash } = req.params;
    const filePath = req.params[0]; // This will be the subtitle file path
    
    console.log(`InfoHash: ${infoHash}`);
    console.log(`Subtitle File: ${filePath}`);
    
    try {
        // Get the subtitle file from the stream service
        const subtitleData = getSubtitleFile(infoHash, filePath);
        
        if (!subtitleData) {
            console.error(`Subtitle not found: ${filePath}`);
            return res.status(404).send('Subtitle not found');
        }
        
        if (subtitleData.error) {
            console.error(`Subtitle error: ${subtitleData.error}`);
            return res.status(500).json({ success: false, message: subtitleData.error });
        }
        
        // Set appropriate headers for subtitle files
        res.set({
            'Content-Type': 'text/plain; charset=utf-8',
            'Access-Control-Allow-Origin': '*',
            'Cache-Control': 'public, max-age=3600'
        });
        
        console.log(`Serving subtitle: ${filePath}`);
        res.send(subtitleData.content);
        
    } catch (error) {
        console.error(`Error serving subtitle ${filePath}:`, error);
        res.status(500).send('Error loading subtitle');
    }
});

// NEW: Route to get available subtitles for a stream
router.get('/subtitles-list/:infoHash', (req, res) => {
    console.log(`\n--- Subtitle List Request ---`);
    const { infoHash } = req.params;
    
    try {
        const availableSubtitles = getAvailableSubtitles(infoHash);
        
        if (!availableSubtitles) {
            return res.json({ success: true, subtitles: [] });
        }
        
        console.log(`Found ${availableSubtitles.length} subtitle files for ${infoHash}`);
        res.json({ success: true, subtitles: availableSubtitles });
        
    } catch (error) {
        console.error(`Error getting subtitle list for ${infoHash}:`, error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// NEW: Heartbeat endpoint to keep torrent streams alive during long pauses
router.post('/heartbeat/:infoHash', (req, res) => {
    const { infoHash } = req.params;
    
    // Update access timestamp for active torrents to prevent cleanup (only for torrents, not local files)
    if (activeTorrents[infoHash]) {
        // Don't update heartbeat for local files - they don't need it
        if (activeTorrents[infoHash].file && activeTorrents[infoHash].file.isLocalFile) {
            return res.json({ success: true, message: 'Local file - heartbeat not needed' });
        }
        
        activeTorrents[infoHash].lastAccessed = Date.now();
        activeTorrents[infoHash].isActivelyStreaming = true;
        
        // Resume torrent if it was paused due to inactivity
        if (activeTorrents[infoHash].torrent && activeTorrents[infoHash].torrent.paused) {
            console.log(`▶️ Resuming paused torrent from heartbeat: ${activeTorrents[infoHash].torrent.name}`);
            activeTorrents[infoHash].torrent.resume();
        }
        
        console.log(`💓 Heartbeat received for torrent ${infoHash}, extending stream lifetime`);
        res.json({ success: true, message: 'Torrent stream heartbeat updated' });
    } else {
        console.warn(`💓 Heartbeat for unknown stream: ${infoHash}`);
        res.status(404).json({ success: false, error: 'Stream not found' });
    }
});

// NEW: Pause endpoint to pause torrent when user closes video
router.post('/pause/:infoHash', (req, res) => {
    const { infoHash } = req.params;
    
    console.log(`⏸️ Pause request received for torrent ${infoHash}`);
    
    if (activeTorrents[infoHash]) {
        // Don't pause local files - they don't need bandwidth management
        if (activeTorrents[infoHash].file && activeTorrents[infoHash].file.isLocalFile) {
            console.log(`⏸️ Local file detected - no need to pause: ${infoHash}`);
            return res.json({ success: true, message: 'Local file - pause not needed' });
        }
        
        // Mark as not actively streaming
        activeTorrents[infoHash].isActivelyStreaming = false;
        activeTorrents[infoHash].lastAccessed = Date.now(); // Update for tracking
        
        // Pause the torrent to stop downloading and free up bandwidth
        if (activeTorrents[infoHash].torrent && !activeTorrents[infoHash].torrent.paused) {
            const torrent = activeTorrents[infoHash].torrent;
            torrent.pause();
            
            console.log(`⏸️ Torrent paused successfully: ${torrent.name}`);
            console.log(`⏸️ Download progress preserved: ${(torrent.progress * 100).toFixed(1)}% completed`);
            console.log(`⏸️ Downloaded: ${(torrent.downloaded / 1024 / 1024).toFixed(1)}MB / ${(torrent.length / 1024 / 1024).toFixed(1)}MB`);
            
            // Disconnect peers to free up connections but keep torrent in memory
            torrent.wires.forEach(wire => {
                try {
                    wire.destroy();
                } catch (destroyErr) {
                    console.warn(`⏸️ Error disconnecting peer during pause: ${destroyErr.message}`);
                }
            });
            
            res.json({ 
                success: true, 
                message: 'Torrent paused successfully',
                downloadProgress: torrent.progress,
                downloadedBytes: torrent.downloaded,
                totalBytes: torrent.length
            });
        } else {
            res.json({ success: true, message: 'Torrent already paused or not found' });
        }
    } else {
        console.warn(`⏸️ Pause request for unknown torrent: ${infoHash}`);
        res.status(404).json({ success: false, error: 'Torrent not found' });
    }
});

// NEW: Resume endpoint to resume torrent when user reopens video  
router.post('/resume/:infoHash', (req, res) => {
    const { infoHash } = req.params;
    
    console.log(`▶️ Resume request received for torrent ${infoHash}`);
    
    if (activeTorrents[infoHash]) {
        // Don't need to resume local files
        if (activeTorrents[infoHash].file && activeTorrents[infoHash].file.isLocalFile) {
            console.log(`▶️ Local file detected - already ready: ${infoHash}`);
            return res.json({ success: true, message: 'Local file - already available' });
        }
        
        // Mark as actively streaming
        activeTorrents[infoHash].isActivelyStreaming = true;
        activeTorrents[infoHash].lastAccessed = Date.now();
        
        // Resume the torrent to continue downloading
        if (activeTorrents[infoHash].torrent && activeTorrents[infoHash].torrent.paused) {
            const torrent = activeTorrents[infoHash].torrent;
            torrent.resume();
            
            console.log(`▶️ Torrent resumed successfully: ${torrent.name}`);
            console.log(`▶️ Resuming from: ${(torrent.progress * 100).toFixed(1)}% completed`);
            console.log(`▶️ Downloaded: ${(torrent.downloaded / 1024 / 1024).toFixed(1)}MB / ${(torrent.length / 1024 / 1024).toFixed(1)}MB`);
            
            res.json({ 
                success: true, 
                message: 'Torrent resumed successfully',
                downloadProgress: torrent.progress,
                downloadedBytes: torrent.downloaded,
                totalBytes: torrent.length
            });
        } else {
            res.json({ success: true, message: 'Torrent already active or not found' });
        }
    } else {
        console.warn(`▶️ Resume request for unknown torrent: ${infoHash}`);
        res.status(404).json({ success: false, error: 'Torrent not found' });
    }
});

// NEW: Status endpoint to check torrent download progress
router.get('/status/:infoHash', (req, res) => {
    const { infoHash } = req.params;
    
    if (activeTorrents[infoHash]) {
        const torrentData = activeTorrents[infoHash];
        
        // For local files, return completed status
        if (torrentData.file && torrentData.file.isLocalFile) {
            return res.json({
                success: true,
                isLocalFile: true,
                status: 'completed',
                progress: 1.0,
                downloadedBytes: torrentData.file.length,
                totalBytes: torrentData.file.length,
                downloadSpeed: 0,
                uploadSpeed: 0,
                peers: 0
            });
        }
        
        // For torrents, return current status
        if (torrentData.torrent) {
            const torrent = torrentData.torrent;
            res.json({
                success: true,
                isLocalFile: false,
                status: torrent.paused ? 'paused' : 'downloading',
                progress: torrent.progress,
                downloadedBytes: torrent.downloaded,
                totalBytes: torrent.length,
                downloadSpeed: torrent.downloadSpeed,
                uploadSpeed: torrent.uploadSpeed,
                peers: torrent.numPeers,
                isActivelyStreaming: torrentData.isActivelyStreaming || false
            });
        } else {
            res.json({
                success: true,
                status: 'initializing',
                progress: 0,
                downloadedBytes: 0,
                totalBytes: 0,
                downloadSpeed: 0,
                uploadSpeed: 0,
                peers: 0
            });
        }
    } else {
        res.status(404).json({ success: false, error: 'Torrent not found' });
    }
});

export default router; 