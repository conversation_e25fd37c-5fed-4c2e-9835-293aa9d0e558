// User configuration with passwords from environment variables
export const USERS = [
  { 
    id: 1, 
    username: 'Backwood', 
    password: process.env.USER1_PASSWORD || 'change-me-in-production',
    email: '<EMAIL>',
    displayName: 'Backwood - Main Account'
  },
  { 
    id: 2, 
    username: 'user2', 
    password: process.env.USER2_PASSWORD || 'change-me-in-production',
    email: '<EMAIL>',
    displayName: 'User 2'
  },
  { 
    id: 3, 
    username: 'user3', 
    password: process.env.USER3_PASSWORD || 'change-me-in-production',
    email: '<EMAIL>',
    displayName: 'User 3'
  },
  { 
    id: 4, 
    username: 'user4', 
    password: process.env.USER4_PASSWORD || 'change-me-in-production',
    email: '<EMAIL>',
    displayName: 'User 4'
  },
  { 
    id: 5, 
    username: 'user5', 
    password: process.env.USER5_PASSWORD || 'change-me-in-production',
    email: '<EMAIL>',
    displayName: 'User 5'
  },
  { 
    id: 6, 
    username: 'user6', 
    password: process.env.USER6_PASSWORD || 'change-me-in-production',
    email: '<EMAIL>',
    displayName: 'User 6'
  }
];

// Warn if using default passwords in production
if (process.env.NODE_ENV === 'production') {
  const usingDefaults = USERS.some(user => user.password === 'change-me-in-production');
  if (usingDefaults) {
    console.warn('⚠️  WARNING: Using default passwords in production! Set USER1_PASSWORD through USER6_PASSWORD environment variables.');
  }
} 