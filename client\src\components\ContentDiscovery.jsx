import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import OptimizedMediaCard from './OptimizedMediaCard';
import { apiFetch } from '../utils/api';
import recommendationEngine from '../utils/recommendationEngine';
import { watchlistStorage } from '../utils/watchlistStorage';

const ContentDiscovery = ({ 
  className = '',
  gridClassName = '', // Add gridClassName prop
  showTitle = true,
  maxItems = 20,
  onItemClick,
  onPlay,
  onTrailer
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  
  const [recommendations, setRecommendations] = useState([]);
  const [trending, setTrending] = useState([]);
  const [newReleases, setNewReleases] = useState([]);
  const [hiddenGems, setHiddenGems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('recommendations');
  const [selectedGenre, setSelectedGenre] = useState('all');
  const [selectedYear, setSelectedYear] = useState('all');
  const [selectedRating, setSelectedRating] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [watchlist, setWatchlist] = useState([]);

  // Recommendation types
  const recommendationTypes = [
    { id: 'recommendations', label: 'For You', icon: '🎯' },
    { id: 'trending', label: 'Trending', icon: '🔥' },
    { id: 'new', label: 'New Releases', icon: '🆕' },
    { id: 'gems', label: 'Hidden Gems', icon: '💎' }
  ];

  // Genre options
  const genres = [
    { id: 'all', name: 'All Genres' },
    { id: 'action', name: 'Action' },
    { id: 'comedy', name: 'Comedy' },
    { id: 'drama', name: 'Drama' },
    { id: 'horror', name: 'Horror' },
    { id: 'romance', name: 'Romance' },
    { id: 'sci-fi', name: 'Sci-Fi' },
    { id: 'thriller', name: 'Thriller' },
    { id: 'documentary', name: 'Documentary' },
    { id: 'animation', name: 'Animation' },
    { id: 'family', name: 'Family' }
  ];

  // Year options
  const years = [
    { id: 'all', name: 'All Years' },
    { id: '2024', name: '2024' },
    { id: '2023', name: '2023' },
    { id: '2022', name: '2022' },
    { id: '2021', name: '2021' },
    { id: '2020', name: '2020' },
    { id: '2019', name: '2019' },
    { id: 'older', name: 'Older' }
  ];

  // Rating options
  const ratings = [
    { id: 'all', name: 'All Ratings' },
    { id: '9+', name: '9+ Stars' },
    { id: '8+', name: '8+ Stars' },
    { id: '7+', name: '7+ Stars' },
    { id: '6+', name: '6+ Stars' }
  ];

  // Filter out watchlist items from all content lists
  const filterOutWatchlist = useCallback((items) => {
    if (!Array.isArray(items) || watchlist.length === 0) return items;
    const watchlistSet = new Set(watchlist.map(item => `${item.id}-${item.media_type}`));
    return items.filter(item => !watchlistSet.has(`${item.id}-${item.media_type}`));
  }, [watchlist]);

  // Load user watch history
  const loadWatchHistory = useCallback(async () => {
    if (!user || !profile) return [];
    
    try {
      // In a real app, this would come from your backend
      const watchProgress = JSON.parse(localStorage.getItem('watchProgress') || '{}');
      const watchlist = JSON.parse(localStorage.getItem('watchlist') || '[]');
      
      // Combine watch progress and watchlist for history
      const history = Object.entries(watchProgress).map(([id, progress]) => ({
        id: parseInt(id),
        progress: progress.percentage || 0,
        lastWatched: progress.lastWatched || new Date().toISOString(),
        media_type: progress.media_type || 'movie'
      }));
      
      return history;
    } catch (error) {
      console.error('Error loading watch history:', error);
      return [];
    }
  }, [user, profile]);

  // Load user ratings
  const loadUserRatings = useCallback(async () => {
    if (!user || !profile) return [];
    
    try {
      // In a real app, this would come from your backend
      const ratings = JSON.parse(localStorage.getItem('userRatings') || '[]');
      return ratings;
    } catch (error) {
      console.error('Error loading user ratings:', error);
      return [];
    }
  }, [user, profile]);

  // Load content data
  const loadContentData = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Load different content types
      const [moviesResponse, tvResponse] = await Promise.all([
        apiFetch('/api/trending/movies?page=1'),
        apiFetch('/api/trending/tv?page=1')
      ]);

      const movies = moviesResponse.ok ? (await moviesResponse.json()).results : [];
      const tvShows = tvResponse.ok ? (await tvResponse.json()).results : [];
      
      // Combine and add media type
      const allContent = [
        ...movies.map(item => ({ ...item, media_type: 'movie' })),
        ...tvShows.map(item => ({ ...item, media_type: 'tv' }))
      ];

      // Load user data
      const watchHistory = await loadWatchHistory();
      const userRatings = await loadUserRatings();

      // Generate recommendations
      const personalizedRecs = await recommendationEngine.getPersonalizedRecommendations(
        user?.id || 'anonymous',
        profile?.id || 'default',
        allContent,
        {
          limit: maxItems,
          userWatchHistory: watchHistory,
          userRatings: userRatings
        }
      );

      // When setting recommendations, trending, new releases, hidden gems, filter out watchlist
      setRecommendations(filterOutWatchlist(personalizedRecs.map(rec => rec.item)));
      setTrending(filterOutWatchlist(recommendationEngine.getTrendingContent(allContent, maxItems)));
      setNewReleases(filterOutWatchlist(recommendationEngine.getNewReleases(allContent, maxItems)));
      setHiddenGems(filterOutWatchlist(recommendationEngine.getHiddenGems(allContent, maxItems)));

    } catch (error) {
      console.error('Error loading content data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user, profile, maxItems, loadWatchHistory, loadUserRatings, filterOutWatchlist]);

  // Load data on mount
  useEffect(() => {
    loadContentData();
  }, [loadContentData]);

  // Load watchlist on mount or when user/profile changes
  useEffect(() => {
    const loadWatchlist = async () => {
      if (user && profile && profile.id) {
        const stored = await watchlistStorage.getWatchlist(user.id, profile.id);
        setWatchlist(stored || []);
      } else {
        setWatchlist([]);
      }
    };
    loadWatchlist();
  }, [user, profile]);

  // Get current content based on active tab
  const currentContent = useMemo(() => {
    switch (activeTab) {
      case 'recommendations':
        return recommendations;
      case 'trending':
        return trending;
      case 'new':
        return newReleases;
      case 'gems':
        return hiddenGems;
      default:
        return recommendations;
    }
  }, [activeTab, recommendations, trending, newReleases, hiddenGems]);

  // Apply filters
  const filteredContent = useMemo(() => {
    let filtered = currentContent;

    // Genre filter
    if (selectedGenre !== 'all') {
      const genreMap = {
        'action': [28, 12],
        'comedy': [35],
        'drama': [18],
        'horror': [27],
        'romance': [10749],
        'sci-fi': [878],
        'thriller': [53],
        'documentary': [99],
        'animation': [16],
        'family': [10751]
      };
      
      const targetGenres = genreMap[selectedGenre];
      if (targetGenres) {
        filtered = filtered.filter(item => 
          item.genre_ids && item.genre_ids.some(id => targetGenres.includes(id))
        );
      }
    }

    // Year filter
    if (selectedYear !== 'all') {
      filtered = filtered.filter(item => {
        if (!item.release_date) return false;
        const year = new Date(item.release_date).getFullYear();
        
        if (selectedYear === 'older') {
          return year < 2019;
        }
        return year === parseInt(selectedYear);
      });
    }

    // Rating filter
    if (selectedRating !== 'all') {
      const minRating = parseInt(selectedRating);
      filtered = filtered.filter(item => 
        (item.vote_average || 0) >= minRating
      );
    }

    return filtered;
  }, [currentContent, selectedGenre, selectedYear, selectedRating]);

  // Handle item click
  const handleItemClick = useCallback((item) => {
    if (onItemClick) {
      onItemClick(item);
    } else {
      const route = item.media_type === 'movie' ? 'movie' : 'show';
      navigate(`/${route}/${item.id}`);
    }
  }, [onItemClick, navigate]);

  // Get recommendation reason
  const getRecommendationReason = useCallback((item) => {
    const reasons = [
      'Based on your watch history',
      'Trending in your region',
      'Similar to content you enjoyed',
      'Highly rated by critics',
      'New release you might like',
      'Hidden gem worth discovering'
    ];
    return reasons[Math.floor(Math.random() * reasons.length)];
  }, []);

  if (isLoading) {
    return (
      <div className={`${className}`}>
        {showTitle && (
          <h2 className="text-2xl font-bold text-white mb-6">Discover Content</h2>
        )}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg h-64 animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {showTitle && (
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white">Discover Content</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-900 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Genre Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Genre</label>
              <select
                value={selectedGenre}
                onChange={(e) => setSelectedGenre(e.target.value)}
                className="w-full bg-gray-800 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
              >
                {genres.map(genre => (
                  <option key={genre.id} value={genre.id}>{genre.name}</option>
                ))}
              </select>
            </div>

            {/* Year Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Year</label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="w-full bg-gray-800 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
              >
                {years.map(year => (
                  <option key={year.id} value={year.id}>{year.name}</option>
                ))}
              </select>
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Rating</label>
              <select
                value={selectedRating}
                onChange={(e) => setSelectedRating(e.target.value)}
                className="w-full bg-gray-800 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
              >
                {ratings.map(rating => (
                  <option key={rating.id} value={rating.id}>{rating.name}</option>
                ))}
              </select>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                onClick={() => {
                  setSelectedGenre('all');
                  setSelectedYear('all');
                  setSelectedRating('all');
                }}
                className="w-full bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Recommendation Type Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {recommendationTypes.map(type => (
          <button
            key={type.id}
            onClick={() => setActiveTab(type.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              activeTab === type.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
            }`}
          >
            <span className="mr-2">{type.icon}</span>
            {type.label}
          </button>
        ))}
      </div>

      {/* Content Grid */}
      {filteredContent.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-400 text-lg mb-4">No content found with current filters</p>
          <button
            onClick={() => {
              setSelectedGenre('all');
              setSelectedYear('all');
              setSelectedRating('all');
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded transition-colors"
          >
            Clear Filters
          </button>
        </div>
      ) : (
        <div className={gridClassName || "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"}>
          {filteredContent.map((item, index) => (
            <div key={`${item.id}-${item.media_type}`} className="relative group">
              <OptimizedMediaCard
                item={item}
                onCardClick={handleItemClick}
                priority={index < 6} // Prioritize first 6 items
                className="w-full"
                onPlay={onPlay}
                onTrailer={onTrailer}
              />
              
              {/* Recommendation reason tooltip */}
              {activeTab === 'recommendations' && (
                <div className="absolute bottom-full left-0 right-0 mb-2 bg-black bg-opacity-90 text-white text-xs p-2 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                  {getRecommendationReason(item)}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Load More Button */}
      {filteredContent.length >= maxItems && (
        <div className="text-center mt-8">
          <button
            onClick={() => loadContentData()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Load More Recommendations
          </button>
        </div>
      )}
    </div>
  );
};

export default ContentDiscovery; 