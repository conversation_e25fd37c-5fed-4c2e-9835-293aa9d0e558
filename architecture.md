# Torvie Architecture Overview

## 1. System Overview
Torvie is a modern, AI-powered streaming discovery and playback platform. It features a React (Vite) frontend and a Node.js (Express) backend, with modular architecture for scalability and maintainability.

---

## 2. High-Level Architecture Diagram

```mermaid
graph TD
  A[Client (React)] -- REST API --> B[Backend (Express)]
  B -- Auth & Data --> C[Storage (JSON/DB)]
  B -- Streams --> D[Media Files]
  B -- Live TV --> E[Live TV Scrapers]
  A -- Auth --> B
  A -- Profile Mgmt --> B
  A -- Watchlist/Progress --> B
```
```

---

## 3. Technology Stack
- **Frontend:** React, Vite, Tailwind CSS, React Router
- **Backend:** Node.js, Express
- **Data Storage:** JSON files (profiles, watchlists, progress); future: PostgreSQL
- **Authentication:** JWT (JSON Web Tokens)
- **DevOps:** <PERSON>er (planned), GitHub Actions (planned)
- **Testing:** Jest (backend), React Testing Library (frontend, planned)

---

## 4. Data Flow
1. **User loads app:**
   - App checks authentication (JWT in localStorage).
   - If not authenticated, shows login/register.
2. **After login:**
   - Loads user profiles from backend.
   - User selects profile; app loads watchlist, progress, etc.
3. **Browsing/Discovery:**
   - Frontend fetches media/search data from backend APIs.
   - Live TV and streaming handled via backend proxy endpoints.
4. **Playback:**
   - Media streams proxied through backend for security and compatibility.

---

## 5. Authentication & Profiles
- **JWT-based authentication** for secure, stateless sessions.
- **Profile management** allows multiple user profiles per account.
- **Watchlist and progress** are stored per profile.

---

## 6. Modular Structure
- **Frontend:**
  - `components/` – Reusable UI elements
  - `pages/` – Route-based views
  - `contexts/` – Global state (auth, profiles, live TV)
  - `utils/` – Utility functions (API, storage, etc.)
- **Backend:**
  - `routes/` – API endpoints (auth, media, search, user data)
  - `services/` – External integrations (torrent, TV scrapers)
  - `storage/` – Data persistence (JSON, future DB)
  - `middleware/` – Auth, error handling, env checks

---

## 7. Extensibility & Future Plans
- **Database migration:** Move from JSON to PostgreSQL for scalability.
- **Containerization:** Dockerize app for local/dev/prod parity.
- **CI/CD:** Add GitHub Actions for automated testing and deployment.
- **Testing:** Expand test coverage (unit, integration, E2E).
- **Observability:** Add logging, metrics, and tracing.

---

## 8. Rationale for Key Decisions
- **React + Vite:** Fast dev experience, modern ecosystem.
- **Express:** Simple, flexible, widely supported for REST APIs.
- **JWT Auth:** Secure, stateless, easy to integrate with frontend.
- **Modular structure:** Eases maintenance and future scaling.
- **JSON storage (initial):** Fast prototyping; will migrate to DB as user base grows.

---

## 9. Diagrams
### Authentication & Profile Flow
```mermaid
sequenceDiagram
  participant U as User
  participant F as Frontend
  participant B as Backend
  U->>F: Load App
  F->>B: Check Auth (JWT)
  alt Not Authenticated
    F->>U: Show Login
    U->>F: Submit Credentials
    F->>B: Login Request
    B->>F: JWT Token
    F->>U: Store Token
  end
  F->>B: Get Profiles
  B->>F: Profile List
  U->>F: Select Profile
  F->>B: Set Active Profile
  B->>F: Profile Data
  F->>U: Main App
```
```

---

## 10. Contact & Contribution
- For architecture changes, update this document and notify the team.
- See `tech_debt.md` for known issues and improvement areas. 