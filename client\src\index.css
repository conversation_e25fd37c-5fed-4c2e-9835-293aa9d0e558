@tailwind base;
@tailwind components;
@tailwind utilities;

/* Simple, Working Responsive System */
:root {
  /* Fixed, reasonable sizes that work */
  --media-card-width: 180px;
  --media-card-height: 270px;
  --header-height: 80px;
  --container-max-width: 1600px;
  --row-container-width: 1500px;
  
  /* Simple text sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  
  /* Fixed spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
}

/* Phone - make cards smaller but readable */
@media (max-width: 768px) {
  :root {
    --media-card-width: 140px;
    --media-card-height: 210px;
    --row-container-width: 100vw;
    --text-4xl: 1.5rem;
  }
}

/* Tablet - slightly smaller */
@media (min-width: 769px) and (max-width: 1024px) {
  :root {
    --media-card-width: 160px;
    --media-card-height: 240px;
    --row-container-width: 95vw;
  }
}

/* Large screens - slightly bigger cards */
@media (min-width: 1921px) {
  :root {
    --media-card-width: 200px;
    --media-card-height: 300px;
    --row-container-width: 1800px;
    --text-4xl: 2.5rem;
  }
}

@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Drag-to-scroll functionality */
  .drag-scroll {
    cursor: grab;
    user-select: none;
    scroll-behavior: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    scroll-snap-type: none; /* Remove any scroll snapping */
  }
  
  .drag-scroll:active,
  .drag-scroll.cursor-grabbing {
    cursor: grabbing;
  }
  
  .drag-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  
  /* Prevent text selection during drag */
  .drag-scroll * {
    user-select: none;
    -webkit-user-drag: none;
  }
  
  /* Enable pointer events on cards while maintaining drag scroll */
  .drag-scroll .media-card {
    pointer-events: auto;
    user-select: none;
  }
  
  /* Simple responsive text classes */
  .text-responsive-xs { font-size: var(--text-xs); }
  .text-responsive-sm { font-size: var(--text-sm); }
  .text-responsive-base { font-size: var(--text-base); }
  .text-responsive-lg { font-size: var(--text-lg); }
  .text-responsive-xl { font-size: var(--text-xl); }
  .text-responsive-2xl { font-size: var(--text-2xl); }
  .text-responsive-3xl { font-size: var(--text-3xl); }
  .text-responsive-4xl { font-size: var(--text-4xl); }
  .text-responsive-5xl { font-size: var(--text-5xl); }
  
  /* Container classes */
  .container-responsive { max-width: var(--container-max-width); }
  .row-container-responsive { max-width: var(--row-container-width); }
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Simple glow effect without scaling */
.media-card-glow {
  position: relative;
  z-index: 1;
}
.media-card-glow::before {
  content: '';
  position: absolute;
  z-index: 0;
  top: -18px;
  left: -18px;
  right: -18px;
  bottom: -18px;
  border-radius: 1.25rem;
  pointer-events: none;
  box-shadow: 0 0 32px 12px rgba(139, 92, 246, 0.7), 0 0 64px 24px rgba(139, 92, 246, 0.3);
  opacity: 1;
}

/* Loading Screen Animations */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(1.2); opacity: 0.6; }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

/* Typewriter cursor */
.typewriter-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: #00bfff;
  font-weight: bold;
}

/* Glow animation for loading elements */
.loading-glow {
  animation: fadeInOut 3s infinite ease-in-out;
} 

/* Override Chrome autofill yellow background */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-text-fill-color: #000 !important;
  transition: background-color 5000s ease-in-out 0s;
} 