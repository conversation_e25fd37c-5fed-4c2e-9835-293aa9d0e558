// Simple persistence test to help debug storage issues
export const runPersistenceTest = () => {
  console.log('🧪 Running persistence test on page load...');
  
  const testKey = 'torvie_persistence_test';
  const testData = {
    timestamp: Date.now(),
    test: 'browser-restart-test',
    userAgent: navigator.userAgent,
    domain: window.location.hostname
  };
  
  try {
    // Save test data
    localStorage.setItem(testKey, JSON.stringify(testData));
    console.log('💾 Saved test data:', testData);
    
    // Immediately read it back
    const retrieved = localStorage.getItem(testKey);
    if (retrieved) {
      const parsed = JSON.parse(retrieved);
      console.log('📖 Retrieved test data:', parsed);
      
      // Check if this is from a previous browser session
      const timeDiff = Date.now() - parsed.timestamp;
      if (timeDiff > 30000) { // More than 30 seconds old
        console.log('⏰ Test data is old (from previous session):', Math.round(timeDiff/1000), 'seconds');
      } else {
        console.log('⏰ Test data is fresh (from current session):', Math.round(timeDiff/1000), 'seconds');
      }
    } else {
      console.error('❌ Failed to retrieve test data immediately after saving!');
    }
    
    // Clean up
    localStorage.removeItem(testKey);
    
  } catch (error) {
    console.error('❌ Persistence test failed:', error);
  }
};

// Auto-run on import
runPersistenceTest(); 