// localStorage debugging utility
const originalSetItem = localStorage.setItem;
const originalGetItem = localStorage.getItem;
const originalRemoveItem = localStorage.removeItem;
const originalClear = localStorage.clear;

// Override localStorage methods to add logging for watchlist operations
localStorage.setItem = function(key, value) {
  if (key.includes('watchlist')) {
    console.log(`🔧 STORAGE DEBUG: Setting ${key} =`, value);
    console.trace('Stack trace for watchlist setItem:');
  }
  return originalSetItem.call(this, key, value);
};

localStorage.getItem = function(key) {
  const result = originalGetItem.call(this, key);
  if (key.includes('watchlist')) {
    console.log(`🔍 STORAGE DEBUG: Getting ${key} =`, result);
  }
  return result;
};

localStorage.removeItem = function(key) {
  if (key.includes('watchlist')) {
    console.log(`❌ STORAGE DEBUG: Removing ${key}`);
    console.trace('Stack trace for watchlist removeItem:');
  }
  return originalRemoveItem.call(this, key);
};

localStorage.clear = function() {
  console.log(`🧹 STORAGE DEBUG: Clearing ALL localStorage`);
  console.trace('Stack trace for localStorage.clear:');
  return originalClear.call(this);
};

// Log all existing watchlist keys on startup
console.log('🔍 STORAGE DEBUG: Existing watchlist keys on startup:');
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && key.includes('watchlist')) {
    console.log(`  ${key}: ${localStorage.getItem(key)}`);
  }
}

export default {}; 