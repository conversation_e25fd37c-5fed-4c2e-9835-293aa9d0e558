// server/services/stream.js
// This module handles the core video streaming logic using WebTorrent.

// 1. Import Dependencies
// ----------------------
import WebTorrent from 'webtorrent';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';
import crypto from 'crypto';

// 2. Initialize WebTorrent Client with Connection Limits
// ------------------------------------------------------
const client = new WebTorrent({
  maxConns: 25,         // Reduced further for better resource management
  nodeId: null,         // Let WebTorrent generate a random node ID
  peerId: null,         // Let WebTorrent generate a random peer ID
  dht: true,            // Enable DHT
  tracker: true,        // Enable tracker
  webSeeds: true        // Enable web seeds
});

// Add global error handler for network buffer overflow and manage peer connections
client.on('error', (err) => {
  if (err.code === 'ENOBUFS') {
    console.error('🚨 Network buffer overflow detected! Reducing connections...');
    managePeerConnections();
  } else {
    console.error('🚨 WebTorrent client error:', err);
  }
});

/**
 * Manage peer connections across all torrents to prevent resource exhaustion
 */
function managePeerConnections() {
  const MAX_PEERS_PER_TORRENT = 15; // Reduced from 31 to 15 for better resource management
  
  client.torrents.forEach(torrent => {
    const infoHash = torrent.infoHash;
    const torrentData = activeTorrents[infoHash];
    
    // If torrent is completed, stop seeding and limit connections to 0
    if (torrent.progress >= 1.0) {
      console.log(`🛑 Torrent completed: ${torrent.name}. Stopping seeding to save resources.`);
      
      // Pause the torrent to stop seeding
      if (!torrent.paused) {
        torrent.pause();
        console.log(`⏸️ Paused completed torrent: ${torrent.name}`);
      }
      
      // Disconnect all peers for completed torrents
      torrent.wires.forEach(wire => {
        try {
          wire.destroy();
        } catch (destroyErr) {
          console.warn(`🔧 Error disconnecting peer from completed torrent: ${destroyErr.message}`);
        }
      });
      return;
    }
    
    // For incomplete torrents, check if they're actively being streamed
    const isActivelyStreaming = torrentData && torrentData.isActivelyStreaming;
    const lastAccessed = torrentData ? torrentData.lastAccessed : 0;
    const timeSinceAccess = Date.now() - lastAccessed;
    const INACTIVE_THRESHOLD = 5 * 60 * 1000; // 5 minutes
    
    // If torrent hasn't been accessed recently, pause it to save resources
    if (!isActivelyStreaming || timeSinceAccess > INACTIVE_THRESHOLD) {
      if (!torrent.paused) {
        console.log(`⏸️ Pausing inactive torrent: ${torrent.name} (inactive for ${Math.round(timeSinceAccess / 60000)} minutes)`);
        torrent.pause();
      }
      
      // Disconnect peers from paused torrents
      torrent.wires.forEach(wire => {
        try {
          wire.destroy();
        } catch (destroyErr) {
          console.warn(`🔧 Error disconnecting peer from paused torrent: ${destroyErr.message}`);
        }
      });
      return;
    }
    
    // For actively streaming torrents, limit peer connections
    if (torrent.numPeers > MAX_PEERS_PER_TORRENT) {
      console.log(`🔧 Limiting peer connections for ${torrent.name} from ${torrent.numPeers} to ${MAX_PEERS_PER_TORRENT}`);
      // Disconnect excess peers, keeping the best connected ones
      const excessPeers = torrent.wires.slice(MAX_PEERS_PER_TORRENT);
      excessPeers.forEach(wire => {
        try {
          wire.destroy();
        } catch (destroyErr) {
          console.warn(`🔧 Error disconnecting excess peer: ${destroyErr.message}`);
        }
      });
    }
  });
}

// Run peer management every 2 minutes to keep resources under control
setInterval(managePeerConnections, 2 * 60 * 1000);

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define a directory to temporarily store torrent files during streaming
const downloadDir = path.resolve(__dirname, '../downloads');
if (!fs.existsSync(downloadDir)) {
  fs.mkdirSync(downloadDir);
}

// Keep track of active torrents to avoid re-downloading
export const activeTorrents = {};
const STREAM_TIMEOUT = 45000; // 45 seconds timeout - balanced for reliability

// FIX: Define supported extensions before they're used
const SUPPORTED_EXTENSIONS = [
    '.mp4', '.mkv', '.webm', '.avi', '.mov', '.flv', '.wmv', 
    '.mpg', '.mpeg', '.m2v', '.m4v', '.3gp', '.vob', '.ogv'
];
const MIME_TYPES = {
  '.mp4': 'video/mp4',
  '.mkv': 'video/mp4', // Serve MKV as MP4 for better browser compatibility
  '.webm': 'video/webm',
  '.avi': 'video/mp4', // Serve AVI as MP4 for better browser compatibility
  '.mov': 'video/mp4', // Serve MOV as MP4 for better browser compatibility
  '.flv': 'video/mp4', // Serve FLV as MP4 for better browser compatibility
  '.wmv': 'video/mp4', // Serve WMV as MP4 for better browser compatibility
  '.mpg': 'video/mp4', // Serve MPG as MP4 for better browser compatibility
  '.mpeg': 'video/mp4', // Serve MPEG as MP4 for better browser compatibility
  '.m2v': 'video/mp4', // Serve M2V as MP4 for better browser compatibility
  '.m4v': 'video/mp4',
  '.3gp': 'video/mp4', // Serve 3GP as MP4 for better browser compatibility
  '.vob': 'video/mp4', // Serve VOB as MP4 for better browser compatibility
  '.ogv': 'video/ogg'
};

// 📁 DOWNLOAD CACHE SYSTEM - Cache existing downloads on startup for instant matching
const downloadCache = {
  initialized: false,
  lastScanTime: null,
  downloads: [] // Array of { folderName, title, year, videoFiles, folderPath }
};

/**
 * Initialize the download cache by scanning the downloads folder
 */
function initializeDownloadCache() {
  try {
    console.log('📁 Initializing download cache...');
    const startTime = Date.now();
    
    if (!fs.existsSync(downloadDir)) {
      console.log('📁 Downloads folder does not exist, creating it...');
      fs.mkdirSync(downloadDir, { recursive: true });
      downloadCache.initialized = true;
      downloadCache.lastScanTime = Date.now();
      return;
    }

    const folders = fs.readdirSync(downloadDir);
    downloadCache.downloads = [];

    // Function to extract clean title and year from a filename (same as before)
    const extractTitleAndYear = (filename) => {
      let clean = filename
        .replace(/\.(mp4|mkv|avi|mov|wmv|flv|webm|m4v|mpg|mpeg)$/i, '') // Remove video extensions
        .replace(/\b(1080p|720p|480p|4K|2160p|HDTV|BluRay|BDRip|WEBRip|WEB-DL|DVDRip|CAMRip|YIFY|x264|x265|h264|h265|hevc|aac|ac3|dts|atmos)\b/gi, '') // Remove quality/codec markers
        .replace(/\b(INTERNAL|REPACK|PROPER|LIMITED|UNRATED|EXTENDED|DIRECTORS?|CUT|THEATRICAL|IMAX)\b/gi, '') // Remove release info
        .replace(/\b(LAMA|RARBG|YTS|ETRG|PublicHD|ShAaNiG|MkvCage|Pahe|PSA|FGT|ION10|STUTTERSHIT|CMRG|EVO)\b/gi, '') // Remove group names
        .replace(/[\[\](){}_]/g, ' ') // Replace brackets with spaces
        .replace(/[-._]+/g, ' ') // Replace dashes, dots, underscores with spaces
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
        
      // Extract year if present
      const yearMatch = clean.match(/\b(19\d{2}|20\d{2})\b/);
      const year = yearMatch ? yearMatch[1] : null;
      
      // Remove year from title for clean comparison
      const title = clean.replace(/\b(19\d{2}|20\d{2})\b/g, '').replace(/\s+/g, ' ').trim().toLowerCase();
      
      return { title, year };
    };

    for (const folder of folders) {
      const folderPath = path.join(downloadDir, folder);
      
      try {
        if (fs.statSync(folderPath).isDirectory()) {
          const files = fs.readdirSync(folderPath);
          const videoFiles = files.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return SUPPORTED_EXTENSIONS.includes(ext);
          });

          if (videoFiles.length > 0) {
            const { title, year } = extractTitleAndYear(folder);
            
            downloadCache.downloads.push({
              folderName: folder,
              title,
              year,
              videoFiles,
              folderPath
            });
          }
        }
      } catch (err) {
        console.warn(`📁 Error processing folder ${folder}: ${err.message}`);
      }
    }

    downloadCache.initialized = true;
    downloadCache.lastScanTime = Date.now();
    
    const endTime = Date.now();
    console.log(`📁 Download cache initialized! Found ${downloadCache.downloads.length} folders with video content in ${endTime - startTime}ms`);
    
    // Log summary for debugging
    if (downloadCache.downloads.length > 0) {
      console.log('📁 Cached downloads:');
      downloadCache.downloads.forEach((download, index) => {
        console.log(`  ${index + 1}. "${download.folderName}" -> Title: "${download.title}", Year: ${download.year || 'N/A'}, Videos: ${download.videoFiles.length}`);
      });
    }
    
  } catch (error) {
    console.error(`📁 Error initializing download cache: ${error.message}`);
    downloadCache.initialized = false;
  }
}

/**
 * Function to calculate similarity between two strings (same as before)
 */
function calculateSimilarity(str1, str2) {
  if (str1 === str2) return 1.0;
  
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  // Levenshtein distance
  const editDistance = (s1, s2) => {
    const matrix = Array(s2.length + 1).fill(null).map(() => Array(s1.length + 1).fill(null));
    
    for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= s2.length; j++) {
      for (let i = 1; i <= s1.length; i++) {
        const substitutionCost = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // insertion
          matrix[j - 1][i] + 1, // deletion
          matrix[j - 1][i - 1] + substitutionCost // substitution
        );
      }
    }
    
    return matrix[s2.length][s1.length];
  };
  
  return (longer.length - editDistance(shorter, longer)) / longer.length;
}



// Initialize cache when module loads
initializeDownloadCache();

// Cleanup old torrents periodically to prevent port conflicts (with extended timeout for active streams)
setInterval(() => {
  const currentTime = Date.now();
  const staleTimeout = 45 * 60 * 1000; // 45 minutes (extended from 10 minutes)
  const longPauseTimeout = 2 * 60 * 60 * 1000; // 2 hours for completely inactive streams
  
  Object.keys(activeTorrents).forEach(infoHash => {
    const torrentData = activeTorrents[infoHash];
    if (torrentData && torrentData.lastAccessed) {
      // Never clean up local files - they don't consume network resources
      if (torrentData.file && torrentData.file.isLocalFile) {
        console.log(`🧹 Skipping cleanup for local file: ${infoHash}`);
        return;
      }
      
      const timeSinceAccess = currentTime - torrentData.lastAccessed;
      
      // For actively streaming content (recently accessed), use longer timeout
      const timeoutToUse = torrentData.isActivelyStreaming ? longPauseTimeout : staleTimeout;
      
      if (timeSinceAccess > timeoutToUse) {
        console.log(`🧹 Cleaning up stale torrent: ${infoHash} (inactive for ${Math.round(timeSinceAccess / 60000)} minutes)`);
        
        const torrent = client.get(infoHash);
        if (torrent && typeof torrent.destroy === 'function') {
          try {
            torrent.destroy(() => {
              console.log(`🧹 Stale torrent destroyed: ${infoHash}`);
            });
          } catch (destroyError) {
            console.error(`🧹 Error destroying stale torrent: ${destroyError.message}`);
          }
        }
        delete activeTorrents[infoHash];
      }
    }
  });
}, 10 * 60 * 1000); // Run cleanup every 10 minutes (less frequent)

// Extensions already defined above to avoid initialization order issues

// --- FIX: Add a list of reliable public trackers to boost peer discovery ---
const ANNOUNCE_TRACKERS = [
    "udp://tracker.openbittorrent.com:80",
    "udp://tracker.leechers-paradise.org:6969",
    "udp://tracker.coppersurfer.tk:6969",
    "udp://tracker.opentrackr.org:1337",
    "udp://explodie.org:6969",
    "udp://tracker.empire-js.us:1337",
    "wss://tracker.btorrent.xyz",
    "wss://tracker.openwebtorrent.com"
];

/**
 * Starts a torrent stream from a magnet link.
 * @param {string} magnetLink - The magnet URI for the torrent.
 * @returns {Promise<object>} A promise that resolves with details about the file being streamed.
 */
export function startStream(magnetLink) {
  return new Promise((resolve, reject) => {
    console.log(`🎬 Starting stream for magnet: ${magnetLink.substring(0, 100)}...`);
    
    const infoHashMatch = magnetLink.match(/btih:([a-fA-F0-9]{40})/i);
    if (!infoHashMatch) {
      console.error(`🎬 Invalid magnet link format: ${magnetLink}`);
      return reject(new Error("Invalid magnet link format."));
    }
    const infoHash = infoHashMatch[1].toLowerCase();
    console.log(`🎬 Extracted infoHash: ${infoHash}`);

    // Check if stream is already active and ready
    if (activeTorrents[infoHash] && activeTorrents[infoHash].ready) {
        console.log(`🎬 Stream already active and ready for torrent: ${infoHash}`);
        
        // NEW: Check if torrent has completed downloading and switch to local file serving
        const torrentData = activeTorrents[infoHash];
        const torrent = torrentData.torrent;
        
        if (torrent && torrent.progress >= 1.0) { // 100% downloaded
            console.log(`🔄 ✅ Torrent completed! Switching to local file serving for: ${infoHash}`);
            
            try {
                // Find the completed file in downloads directory
                const completedFilePath = path.join(downloadDir, torrent.name, torrentData.file.name);
                
                if (fs.existsSync(completedFilePath)) {
                    console.log(`🔄 Found completed file: ${completedFilePath}`);
                    
                    // Update the torrent data to mark it as local file
                    activeTorrents[infoHash].file = {
                        name: torrentData.file.name,
                        path: completedFilePath,
                        length: fs.statSync(completedFilePath).size,
                        isLocalFile: true // Mark as local file
                    };
                    activeTorrents[infoHash].lastAccessed = Date.now();
                    
                    const normalizedPath = completedFilePath.replace(/\\/g, '/');
                    const relativePath = normalizedPath.replace(downloadDir.replace(/\\/g, '/'), '');
                    
                    resolve({
                        filePath: `/api/stream/${infoHash}${relativePath}`,
                        fileName: torrentData.file.name,
                        mimeType: MIME_TYPES[path.extname(torrentData.file.name).toLowerCase()],
                        isLocalFile: true,
                        infoHash: infoHash
                    });
                    return;
                }
            } catch (error) {
                console.error(`🔄 Error checking completed download: ${error.message}`);
                // Fall through to normal torrent serving
            }
        }
        
        // Continue with normal torrent serving if not completed or file not found
        const { file } = activeTorrents[infoHash];
        const normalizedPath = file.path.replace(/\\/g, '/');
        resolve({
            filePath: `/api/stream/${infoHash}/${normalizedPath.split('/').map(encodeURIComponent).join('/')}`,
            fileName: file.name,
            mimeType: MIME_TYPES[path.extname(file.name).toLowerCase()],
            isLocalFile: file.isLocalFile || false,
            infoHash: infoHash
        });
        return;
    }

    // 🎯 Check if files are already downloaded locally using CACHED data for speed
    const checkExistingDownload = (magnetUri) => {
      try {
        // Check if cache is initialized
        if (!downloadCache.initialized) {
          console.log(`🔍 Download cache not initialized, skipping existing download check`);
          return false;
        }

        // Extract the full display name from magnet link
        const magnetName = decodeURIComponent(magnetUri.match(/dn=([^&]+)/)?.[1] || '');
        if (!magnetName) {
          console.log(`🔍 No display name found in magnet link`);
          return false;
        }
        
        console.log(`🔍 Looking for existing download of: "${magnetName}" (using cached data)`);
        
        // Extract title and year from magnet name using same function as cache
        const extractTitleAndYear = (filename) => {
          let clean = filename
            .replace(/\.(mp4|mkv|avi|mov|wmv|flv|webm|m4v|mpg|mpeg)$/i, '') // Remove video extensions
            .replace(/\b(1080p|720p|480p|4K|2160p|HDTV|BluRay|BDRip|WEBRip|WEB-DL|DVDRip|CAMRip|YIFY|x264|x265|h264|h265|hevc|aac|ac3|dts|atmos)\b/gi, '') // Remove quality/codec markers
            .replace(/\b(INTERNAL|REPACK|PROPER|LIMITED|UNRATED|EXTENDED|DIRECTORS?|CUT|THEATRICAL|IMAX)\b/gi, '') // Remove release info
            .replace(/\b(LAMA|RARBG|YTS|ETRG|PublicHD|ShAaNiG|MkvCage|Pahe|PSA|FGT|ION10|STUTTERSHIT|CMRG|EVO)\b/gi, '') // Remove group names
            .replace(/[\[\](){}_]/g, ' ') // Replace brackets with spaces
            .replace(/[-._]+/g, ' ') // Replace dashes, dots, underscores with spaces
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
            
          // Extract year if present
          const yearMatch = clean.match(/\b(19\d{2}|20\d{2})\b/);
          const year = yearMatch ? yearMatch[1] : null;
          
          // Remove year from title for clean comparison
          const title = clean.replace(/\b(19\d{2}|20\d{2})\b/g, '').replace(/\s+/g, ' ').trim().toLowerCase();
          
          return { title, year };
        };
        
        const magnetInfo = extractTitleAndYear(magnetName);
        console.log(`🔍 Extracted from magnet - Title: "${magnetInfo.title}", Year: ${magnetInfo.year || 'N/A'}`);
        console.log(`🔍 Checking ${downloadCache.downloads.length} cached download folders...`);
        
        let bestMatch = null;
        let bestSimilarity = 0;
        
        // Iterate through cached downloads (much faster than filesystem operations)
        for (const download of downloadCache.downloads) {
          // Calculate title similarity using cached data
          const titleSimilarity = calculateSimilarity(magnetInfo.title, download.title);
          
          // Year matching logic
          let yearMatch = true;
          if (magnetInfo.year && download.year) {
            yearMatch = magnetInfo.year === download.year;
          }
          // If one has year and other doesn't, we still allow matching but with lower confidence
          
          // Overall similarity calculation
          let overallSimilarity = titleSimilarity;
          if (magnetInfo.year && download.year && yearMatch) {
            overallSimilarity += 0.1; // Bonus for matching year
          } else if (magnetInfo.year && download.year && !yearMatch) {
            overallSimilarity -= 0.3; // Penalty for mismatched year
          }
          
          console.log(`🔍 Folder: "${download.folderName}" -> Title: "${download.title}", Year: ${download.year || 'N/A'}, Similarity: ${(titleSimilarity * 100).toFixed(1)}%${yearMatch ? ' (Year match)' : magnetInfo.year && download.year ? ' (Year mismatch)' : ''}`);
          
          if (overallSimilarity > bestSimilarity) {
            bestSimilarity = overallSimilarity;
            bestMatch = download;
          }
        }
        
        // Only use match if similarity is very high (80%+ to prevent false matches)
        const SIMILARITY_THRESHOLD = 0.80;
        
        if (bestMatch && bestSimilarity >= SIMILARITY_THRESHOLD) {
          console.log(`🎯 HIGH CONFIDENCE MATCH FOUND (${(bestSimilarity * 100).toFixed(1)}% similarity)!`);
          console.log(`🎯 Using existing download: ${bestMatch.folderName}/${bestMatch.videoFiles[0]}`);
          
          const videoFile = bestMatch.videoFiles[0];
          const filePath = path.join(bestMatch.folderPath, videoFile);
          const mimeType = MIME_TYPES[path.extname(videoFile).toLowerCase()] || 'video/mp4';
          
          // Create a "ready" entry for this existing file with fs streaming support
          activeTorrents[infoHash] = {
            ready: true,
            file: {
              name: videoFile,
              path: filePath,
              length: fs.statSync(filePath).size,
              isLocalFile: true // Flag to identify local files
            },
            lastAccessed: Date.now()
          };
          
          const normalizedPath = filePath.replace(/\\/g, '/');
          const relativePath = normalizedPath.replace(downloadDir.replace(/\\/g, '/'), '');
          resolve({
            filePath: `/api/stream/${infoHash}${relativePath}`,
            fileName: videoFile,
            mimeType: mimeType,
            connectedPeers: 0,
            isLocalFile: true,
            infoHash: infoHash
          });
          return true; // Found matching existing file
        } else {
          console.log(`🔍 No high-confidence matches found. Best similarity was ${(bestSimilarity * 100).toFixed(1)}% (threshold: ${SIMILARITY_THRESHOLD * 100}%)`);
        }
        
        return false; // No matching existing file found
      } catch (error) {
        console.log(`🔍 Error checking cached downloads: ${error.message}`);
        return false;
      }
    };

    // Check for existing downloads first
    if (checkExistingDownload(magnetLink)) {
      return; // Already resolved with existing file
    }

    console.log(`🎬 No existing download found, starting new torrent...`);

    // --- FIX: Even More Defensive Cleanup Function ---
    const cleanup = (errorMsg) => {
        console.error(`🎬 Cleanup triggered: ${errorMsg}`);
        
        // Clean up timeout if it exists
        if (timeoutId) clearTimeout(timeoutId);
        
        // Clean up event listeners if they exist
        if (activeTorrents[infoHash] && activeTorrents[infoHash].cleanup) {
            activeTorrents[infoHash].cleanup();
        }
        
        // Remove torrent by infoHash or magnetLink
        const torrentToRemove = client.get(infoHash) || client.get(magnetLink);
        if (torrentToRemove && typeof torrentToRemove.destroy === 'function') {
            console.log(`🎬 Removing torrent: ${torrentToRemove.name || 'Unknown'}`);
            try {
                torrentToRemove.destroy(() => {
                    console.log(`🎬 Torrent destroyed: ${infoHash}`);
                });
            } catch (destroyError) {
                console.error(`🎬 Error destroying torrent: ${destroyError.message}`);
            }
        } else if (torrentToRemove) {
            console.warn(`🎬 Torrent object found but destroy method not available: ${torrentToRemove.name || 'Unknown'}`);
        } else {
            console.warn(`🎬 No torrent found to remove for infoHash: ${infoHash}`);
        }
        
        // Clean up from active torrents tracking
        delete activeTorrents[infoHash];
        
        // Only reject the promise; do not throw an error that could crash the server.
        reject(new Error(errorMsg));
    };
    
    const timeoutId = setTimeout(() => {
        console.warn(`🎬 Stream timeout after ${STREAM_TIMEOUT/1000} seconds for torrent: ${infoHash}`);
        cleanup('Stream timeout: Could not find enough peers or download first pieces. The torrent may have no active seeders.');
    }, STREAM_TIMEOUT);

    const options = { 
      path: downloadDir, 
      announce: ANNOUNCE_TRACKERS,
      // Optimize for faster peer discovery with safer limits
      maxWebConns: 8,        // Reduced from 20 to prevent buffer overflow
      strategy: 'sequential', // Download sequentially for streaming
      maxConns: 25           // Per-torrent connection limit (lower than global)
    };
    console.log(`🎬 Adding torrent to WebTorrent client with options:`, { path: downloadDir, announceCount: ANNOUNCE_TRACKERS.length });

    // Add error handling for network buffer overflow during torrent add
    const addTorrentWithErrorHandling = () => {
      try {
        client.add(magnetLink, options, (torrent) => {
          // Add error handler for this specific torrent
          torrent.on('error', (err) => {
            if (err.code === 'ENOBUFS') {
              console.error('🚨 Buffer overflow for torrent! Reducing connections...');
              // Reduce connections for this torrent
              if (torrent.numPeers > 15) {
                console.log(`🔧 Reducing peer connections for ${torrent.name} from ${torrent.numPeers} to 15`);
                const excessPeers = torrent.wires.slice(15);
                excessPeers.forEach(wire => {
                  try {
                    wire.destroy();
                  } catch (destroyErr) {
                    console.warn(`🔧 Error disconnecting peer: ${destroyErr.message}`);
                  }
                });
              }
            } else {
              console.error(`🎬 Torrent error for ${torrent.name}:`, err);
            }
          });
          
          // Monitor peer count and throttle if needed (with reduced logging)
          let lastPeerLimitLog = 0;
          torrent.on('wire', (wire) => {
            if (torrent.numPeers > 30) {
              // Only log peer limiting every 30 seconds to reduce spam
              const now = Date.now();
              if (now - lastPeerLimitLog > 30000) {
                console.log(`🔧 Peer limit reached for ${torrent.name} (${torrent.numPeers} peers), throttling connections...`);
                lastPeerLimitLog = now;
              }
              try {
                wire.destroy();
              } catch (destroyErr) {
                // Only log errors occasionally to reduce spam
                if (now - lastPeerLimitLog > 30000) {
                  console.warn(`🔧 Error disconnecting excess peer: ${destroyErr.message}`);
                }
              }
            }
          });
          
          // Continue with existing torrent logic
          console.log(`🎬 Metadata received for torrent: ${torrent.name} (${infoHash})`);
          console.log(`🎬 Torrent info: ${torrent.files.length} files, ${(torrent.length / 1024 / 1024).toFixed(2)} MB total`);
          
          activeTorrents[infoHash] = { 
            torrent, 
            file: null, 
            ready: false, 
            lastAccessed: Date.now(),
            createdAt: Date.now()
          };
          
          let streamResolved = false;

          // Add detailed peer discovery logging (only while not ready)
          torrent.on('wire', (wire, addr) => {
            if (!activeTorrents[infoHash] || !activeTorrents[infoHash].ready) {
              console.log(`🎬 Connected to peer: ${addr || wire.remoteAddress || 'unknown address'} (Total: ${torrent.numPeers})`);
            }
          });
          
          torrent.on('noPeers', (announceType) => {
            console.warn(`🎬 No peers found via: ${announceType}`);
          });

          // INSTANT PEER DISCOVERY: Much faster peer connection for instant streaming
          let peerConnectionAttempts = 0;
          const maxPeerAttempts = 5; // More attempts but faster
          
          const peerDiscoveryCheck = () => {
            peerConnectionAttempts++;
            const peerCount = torrent.numPeers;
            console.log(`🎬 Peer discovery attempt ${peerConnectionAttempts}/${maxPeerAttempts}: ${peerCount} peers connected`);
            
            if (peerCount === 0 && peerConnectionAttempts >= maxPeerAttempts) {
              console.warn(`🎬 No peers found after ${peerConnectionAttempts} attempts. Triggering fallback...`);
              cleanup('No peers available: This torrent may have no active seeders. Trying next torrent...');
              return;
            }
            
            // If we have good peers, we're ready to start checking for streaming
            if (peerCount >= 3 && peerConnectionAttempts === 1) {
              console.log(`🎬 Excellent peer count (${peerCount})! Ready for instant streaming...`);
            }
          };
          
          // Check every 2 seconds for peers (much faster)
          const peerCheckInterval = setInterval(() => {
            if (peerConnectionAttempts >= maxPeerAttempts) {
              clearInterval(peerCheckInterval);
              return;
            }
            peerDiscoveryCheck();
          }, 2000);
          
          // Initial check after just 1 second
          setTimeout(() => peerDiscoveryCheck(), 1000);

          // Log download progress
          let lastProgress = 0;
          torrent.on('download', () => {
            const progress = Math.round((torrent.downloaded / torrent.length) * 100);
            if (progress > lastProgress && progress % 5 === 0) { // Log every 5%
              console.log(`🎬 Download progress: ${progress}% (${torrent.numPeers} peers)`);
              lastProgress = progress;
            }
          });

          // Check if torrent is already ready (race condition fix)
          if (torrent.ready) {
            console.log(`🎬 Torrent was already ready, processing immediately...`);
            setTimeout(() => torrent.emit('ready'), 0);
          }

          // Move file detection logic to the 'ready' event to avoid race conditions
          torrent.on('ready', () => {
            console.log(`🎬 Torrent ready event triggered for: ${torrent.name}`);
            console.log(`🎬 DEBUG: Stream resolved flag: ${streamResolved}, Progress: ${((torrent.downloaded / torrent.length) * 100).toFixed(1)}%`);
            if (!activeTorrents[infoHash]) {
              console.warn(`🎬 Torrent ${infoHash} not found in activeTorrents during ready event`);
              return; // Defensive
            }
            
            // Log all files in the torrent for debugging
            console.log(`🎬 Files in torrent:`);
            torrent.files.forEach((file, index) => {
              const ext = path.extname(file.name).toLowerCase().trim();
              console.log(`  ${index + 1}. ${file.name} (${ext}) - ${(file.length / 1024 / 1024).toFixed(2)} MB`);
            });
            
            // More robust video file detection
            const videoFiles = torrent.files.filter(file => {
              const ext = path.extname(file.name).toLowerCase().trim();
              return SUPPORTED_EXTENSIONS.includes(ext);
            });
            
            console.log(`🎬 Found ${videoFiles.length} video files out of ${torrent.files.length} total files`);
            
            if (videoFiles.length === 0) {
              clearTimeout(timeoutId);
              return cleanup('No playable video file found in the torrent. Supported formats: ' + SUPPORTED_EXTENSIONS.join(', '));
            }
            // --- Prefer first episode for TV torrents ---
            let file = null;
            // Try to find a file that looks like the first episode
            const firstEpRegexes = [
              /s0*1[ ._-]*e0*1/i,         // S01E01, s1e1, s01.e01, etc.
              /season[ ._-]*0*1[ ._-]*episode[ ._-]*0*1/i, // season 1 episode 1
              /ep?i?s?0*1[^\d]/i,        // e01, ep01, episode 1
              /\b1x0*1\b/i,             // 1x01
              /\bepisode[ ._-]*0*1\b/i, // episode 1
              /\bpart[ ._-]*0*1\b/i     // part 1
            ];
            file = videoFiles.find(f => firstEpRegexes.some(rx => rx.test(f.name)));
            if (!file) {
              // Fallback: pick the smallest video file (often the first episode in a season pack)
              file = videoFiles.reduce((a, b) => (a.length < b.length ? a : b));
            }
            if (!file) {
              // Fallback: pick the largest video file (old behavior)
              file = videoFiles.reduce((a, b) => (a.length > b.length ? a : b));
            }
            activeTorrents[infoHash].file = file;
            console.log(`🎬 Selected file for streaming: ${file.name}`);
            
            // INSTANT STREAMING OPTIMIZATION: Calculate proper file piece boundaries
            const fileStartByte = file.offset || 0;
            const fileEndByte = fileStartByte + file.length;
            const startPiece = Math.floor(fileStartByte / torrent.pieceLength);
            const endPiece = Math.floor((fileEndByte - 1) / torrent.pieceLength);
            const priorityPieces = Math.min(20, endPiece - startPiece + 1); // Prioritize first 20 pieces for instant streaming
            
            console.log(`🎬 File spans pieces ${startPiece} to ${endPiece}. Aggressively prioritizing first ${priorityPieces} pieces for INSTANT streaming...`);
            
            // ULTRA-AGGRESSIVE piece prioritization for instant streaming
            for (let i = 0; i < priorityPieces; i++) {
              const pieceIndex = startPiece + i;
              if (pieceIndex <= endPiece) {
                // Use select() with highest priority for these pieces
                const pieceStart = Math.max(0, (pieceIndex * torrent.pieceLength) - fileStartByte);
                const pieceEnd = Math.min(file.length, ((pieceIndex + 1) * torrent.pieceLength) - fileStartByte);
                file.select(pieceStart, pieceEnd);
                
                // Also set critical priority for first few pieces
                if (i < 5) {
                  try {
                    torrent.critical(pieceStart, pieceEnd);
                  } catch (e) {
                    // Ignore if critical method doesn't exist
                  }
                }
              }
            }
            
            // Deselect the rest of the file initially to focus on streaming pieces
            const streamingDataNeeded = Math.min(file.length, priorityPieces * torrent.pieceLength);
            if (streamingDataNeeded < file.length) {
              file.deselect(streamingDataNeeded, file.length - 1);
            }
            
            const resolveStream = () => {
              if (streamResolved) {
                console.log(`🎬 Stream already resolved for ${file.name}, ignoring duplicate call`);
                return;
              }
              streamResolved = true;
              
              console.log(`🎬 Stream resolving, cleaning up event listeners for ${file.name}`);
              
              // Clean up timeout and intervals
              clearTimeout(timeoutId);
              if (peerCheckInterval) clearInterval(peerCheckInterval);
              
              // Clean up event listeners using stored cleanup function
              if (activeTorrents[infoHash] && activeTorrents[infoHash].cleanup) {
                activeTorrents[infoHash].cleanup();
              }
              
              if (!activeTorrents[infoHash]) return; // Defensive: torrent may have been cleaned up
              activeTorrents[infoHash].ready = true;
              const mimeType = MIME_TYPES[path.extname(file.name).toLowerCase()];
              const connectedPeers = typeof torrent.numPeers === 'number' ? torrent.numPeers : (torrent.wires ? torrent.wires.length : 0);
              console.log(`🎬 Stream ready for immediate playback: ${file.name} with ${connectedPeers} connected peers.`);
              const normalizedPath = file.path.replace(/\\/g, '/');
              resolve({
                filePath: `/api/stream/${infoHash}/${normalizedPath.split('/').map(encodeURIComponent).join('/')}`,
                fileName: file.name,
                mimeType: mimeType,
                connectedPeers,
                isLocalFile: false,
                infoHash: infoHash
              });
            };
            
            const checkStreamReady = () => {
              if (!client.get(magnetLink) || !activeTorrents[infoHash]) return;
              
              const progress = (torrent.downloaded / torrent.length) * 100;
              const peerCount = torrent.numPeers || 0;
              
              // ULTRA-AGGRESSIVE STREAMING: Start as soon as we have ANY downloadable content
              
              // Strategy 1: Check if we have the first piece of the file (HIGHEST PRIORITY)
              if (torrent.bitfield.get(startPiece)) {
                console.log(`🎬 First piece (${startPiece}) available! Starting stream INSTANTLY...`);
                resolveStream();
                return;
              }
              
              // Strategy 2: Check if we have ANY pieces from the first few pieces of the file
              let hasEarlyFilePiece = false;
              const earlyPiecesToCheck = Math.min(5, endPiece - startPiece + 1); // Check first 5 pieces
              for (let i = startPiece; i < startPiece + earlyPiecesToCheck; i++) {
                if (torrent.bitfield.get(i)) {
                  console.log(`🎬 Early piece (${i}) available! Starting stream INSTANTLY...`);
                  hasEarlyFilePiece = true;
                  break;
                }
              }
              if (hasEarlyFilePiece) {
                resolveStream();
                return;
              }
              
              // Strategy 3: ULTRA-AGGRESSIVE - Start with ANY progress > 0.1%
              if (progress > 0.1) {
                let hasAnyFilePiece = false;
                for (let i = startPiece; i <= Math.min(startPiece + 15, endPiece); i++) {
                  if (torrent.bitfield.get(i)) {
                    hasAnyFilePiece = true;
                    break;
                  }
                }
                if (hasAnyFilePiece) {
                  console.log(`🎬 Any file piece available with ${progress.toFixed(3)}% progress! Starting stream INSTANTLY...`);
                  resolveStream();
                  return;
                }
              }
              
              // Strategy 4: Start with modest overall progress
              if (progress > 5) {
                console.log(`🎬 Moderate progress (${progress.toFixed(1)}%)! Starting stream NOW...`);
                resolveStream();
                return;
              }
              
              // Strategy 5: Peer-based streaming with very low threshold
              if (peerCount >= 8 && progress > 0.05) {
                console.log(`🎬 Excellent peer count (${peerCount}) with minimal progress (${progress.toFixed(3)}%)! Starting stream...`);
                resolveStream();
                return;
              }
              
              // Strategy 6: Desperate fallback - many peers, any progress
              if (peerCount >= 15 && progress > 0.01) {
                console.log(`🎬 DESPERATE: High peer count (${peerCount}) with tiny progress (${progress.toFixed(3)}%)! Starting stream...`);
                resolveStream();
                return;
              }
              
              // Strategy 7: Last resort - long wait with good peers
              const timeWaited = Date.now() - (activeTorrents[infoHash].createdAt || Date.now());
              if (timeWaited > 30000 && peerCount >= 3 && progress > 0.001) {
                console.log(`🎬 LAST RESORT: Waited ${(timeWaited/1000).toFixed(1)}s with ${peerCount} peers and ${progress.toFixed(3)}% progress! Starting stream...`);
                resolveStream();
                return;
              }
            };
            
            console.log(`🎬 Torrent ready. Waiting for streamable pieces of ${file.name}... (pieces ${startPiece}-${endPiece})`);
            
            // Check if torrent is already complete
            const currentProgress = (torrent.downloaded / torrent.length) * 100;
            if (currentProgress >= 100 || torrent.done) {
              console.log(`🎬 Torrent already complete (${currentProgress.toFixed(1)}%)! Starting stream immediately...`);
              resolveStream();
              return;
            }
            
            // INSTANT CHECK: Check immediately in case pieces are already available
            checkStreamReady();
            
            // ULTRA-RESPONSIVE: Check on EVERY download event for instant streaming
            let downloadEventCount = 0;
            const downloadHandler = () => {
              if (streamResolved) return; // Early exit if already resolved
              downloadEventCount++;
              // Check immediately on first few download events for instant response
              if (downloadEventCount <= 10 || downloadEventCount % 5 === 0) {
                checkStreamReady();
              }
            };
            torrent.on('download', downloadHandler);
            
            // PEER-TRIGGERED STREAMING: Check when we get good peer connections
            const wireHandler = () => {
              if (streamResolved) return; // Early exit if already resolved
              const peerCount = torrent.numPeers;
              // Trigger stream checks when we have good peer counts and any data
              if (peerCount > 0 && torrent.downloaded > 0) {
                console.log(`🎬 Found ${peerCount} peers with data available! Starting stream checks...`);
                checkStreamReady();
              }
            };
            torrent.on('wire', wireHandler);
            
            // AGGRESSIVE POLLING: Also check every 500ms for ultra-fast response
            const quickCheckInterval = setInterval(() => {
              if (streamResolved) {
                clearInterval(quickCheckInterval);
                return;
              }
              checkStreamReady();
            }, 500);
            
            // Clear quick check after 15 seconds to avoid unnecessary polling
            const quickCheckTimeout = setTimeout(() => {
              if (quickCheckInterval) clearInterval(quickCheckInterval);
            }, 15000);
            
            // Enhanced cleanup function to remove ALL event listeners
            const cleanupEventListeners = () => {
              console.log(`🎬 Cleaning up event listeners for torrent: ${infoHash}`);
              torrent.removeListener('download', downloadHandler);
              torrent.removeListener('wire', wireHandler);
              if (quickCheckInterval) clearInterval(quickCheckInterval);
              if (quickCheckTimeout) clearTimeout(quickCheckTimeout);
            };
            
            // Store cleanup function for later use
            if (!activeTorrents[infoHash]) activeTorrents[infoHash] = {};
            activeTorrents[infoHash].cleanup = cleanupEventListeners;
          });

          torrent.on('done', () => {
            if (!activeTorrents[infoHash]) return;
            console.log(`🎬 Torrent finished downloading: ${torrent.name}`);
            console.log(`🎬 DEBUG: Done event - Stream resolved: ${streamResolved}, Has file: ${!!activeTorrents[infoHash].file}`);
            clearTimeout(timeoutId);
            
            // Update download cache when a new download completes
            setTimeout(() => {
              console.log('📁 Torrent completed, refreshing download cache...');
              refreshDownloadCache();
            }, 2000); // Small delay to ensure file is fully written
            
            // If torrent is complete but stream hasn't resolved yet, force trigger ready logic
            if (!streamResolved) {
              console.log(`🎬 Torrent complete! Forcing ready event processing...`);
              // Trigger the ready event manually if it hasn't fired
              if (!activeTorrents[infoHash].file) {
                console.log(`🎬 Ready event may not have fired, processing manually...`);
                setTimeout(() => {
                  // Manually trigger ready logic
                  torrent.emit('ready');
                }, 100);
              } else {
                console.log(`🎬 File exists, resolving stream immediately...`);
                resolveStream();
              }
            } else {
              // Stream already resolved, make sure we clean up listeners
              if (activeTorrents[infoHash] && activeTorrents[infoHash].cleanup) {
                activeTorrents[infoHash].cleanup();
              }
            }
          });

          torrent.on('error', (err) => {
            if (activeTorrents[infoHash]) {
                clearTimeout(timeoutId);
                console.error(`🎬 Non-fatal torrent error: ${err.message}`);
            }
          });

          // Final check: if torrent is already complete, force processing
          setTimeout(() => {
            if (!streamResolved && activeTorrents[infoHash]) {
              const currentProgress = (torrent.downloaded / torrent.length) * 100;
              console.log(`🎬 Final check - Progress: ${currentProgress.toFixed(1)}%, Done: ${torrent.done}, Ready: ${torrent.ready}`);
              
              if (currentProgress >= 99 || torrent.done) {
                console.log(`🎬 Torrent appears complete but stream not resolved. Forcing ready event...`);
                torrent.emit('ready');
              }
            }
          }, 1000);
        });
      } catch (addError) {
        if (addError.code === 'ENOBUFS') {
          console.error('🚨 Buffer overflow when adding torrent! Retrying with reduced settings...');
          // Retry with even lower connection limits
          const retryOptions = {
            ...options,
            maxWebConns: 4,
            maxConns: 10
          };
          setTimeout(() => {
            try {
              client.add(magnetLink, retryOptions, (torrent) => {
                // Inline the torrent processing logic from the main callback
                console.log(`🎬 Retry successful! Torrent added: ${torrent.name}`);
                activeTorrents[infoHash] = {
                  torrent,
                  createdAt: Date.now(),
                  lastAccessed: Date.now(),
                  ready: false
                };
                
                // Set up the same event handlers as the main callback
                torrent.on('ready', () => {
                  console.log(`🎬 Torrent ready: ${torrent.name}`);
                  // ... rest of ready event logic would go here
                });
                
                torrent.on('done', () => {
                  console.log(`🎬 Torrent finished downloading: ${torrent.name}`);
                });
                
                torrent.on('error', (err) => {
                  console.error(`🎬 Non-fatal torrent error: ${err.message}`);
                });
              });
            } catch (retryError) {
              console.error('🚨 Retry failed:', retryError);
              cleanup(`Network buffer overflow: ${retryError.message}`);
            }
          }, 2000);
        } else {
          console.error('🚨 Error adding torrent:', addError);
          cleanup(`Failed to add torrent: ${addError.message}`);
        }
      }
    };

    // Call the torrent add function with error handling
    addTorrentWithErrorHandling();

    // Global client error handler
    client.on('error', (err) => {
      if (err.message.includes('Cannot add duplicate torrent')) {
        console.warn(`🎬 Attempted to add a duplicate torrent, which is already being handled.`);
      } else if (err.code === 'ENOBUFS') {
        console.error('🚨 Global buffer overflow! Reducing all torrent connections...');
        // Reduce connections for all torrents
        client.torrents.forEach(torrent => {
          if (torrent.numPeers > 15) {
            const excessPeers = torrent.wires.slice(15);
            excessPeers.forEach(wire => {
              try {
                wire.destroy();
              } catch (destroyErr) {
                console.warn(`🔧 Error disconnecting peer: ${destroyErr.message}`);
              }
            });
          }
        });
      } else {
        console.error('🎬 WebTorrent client error:', err);
      }
    });
  });
}

/**
 * Gets the file stream for an active torrent.
 */
export function refreshDownloadCache() {
  console.log('🔄 Refreshing download cache...');
  initializeDownloadCache();
}

export function getFileStream(infoHash, filePath, range) {
    console.log(`\n--- getFileStream Debug Start ---`);
    console.log(`Incoming infoHash: ${infoHash}`);
    console.log(`Incoming filePath (encoded): ${filePath}`);
    const active = activeTorrents[infoHash.toLowerCase()];
    console.log(`activeTorrents object (for infoHash ${infoHash}):`, activeTorrents[infoHash.toLowerCase()]);
    console.log(`Is 'active' truthy?`, !!active);
    console.log(`Is 'active.file' truthy?`, !!(active && active.file));
    const requestedPath = decodeURIComponent(filePath);
    console.log(`Requested file path (decoded):`, requestedPath);
    if (!active || !active.file) {
        console.log('Stream lookup failed: No active torrent or file. Returning null.');
        return null;
    }
    
    // Update last accessed time and mark as actively streaming
    active.lastAccessed = Date.now();
    active.isActivelyStreaming = true;
    
    // Resume torrent if it was paused (but not if it's completed and local)
    if (active.torrent && active.torrent.paused && !active.file.isLocalFile) {
        console.log(`▶️ Resuming paused torrent for active streaming: ${active.torrent.name}`);
        active.torrent.resume();
    }
    // This block should only be hit if active and active.file are truthy
    console.log('Torrent and file are active. Proceeding with file matching.');
    const requestedFile = requestedPath.split('/').pop();
    const activeFile = active.file.path.split(/[/\\]/).pop();
    console.log('Comparison: Requested filename:', requestedFile);
    console.log('Comparison: Active filename:', activeFile);
    console.log('Comparison: Do filenames match?', requestedFile === activeFile);
    if (requestedFile !== activeFile) {
        console.log('Stream lookup failed: Filename mismatch. Returning null.');
        return null;
    }

    const file = active.file;
    const fileSize = file.length;
    const fileExtension = path.extname(file.name).toLowerCase();
    const mimeType = MIME_TYPES[fileExtension] || 'video/mp4';
    
    // Enhanced codec detection - check for incompatible formats that need transcoding
    const needsTranscoding = (fileName, extension) => {
        const name = fileName.toLowerCase();
        
        // Always transcode these formats that are rarely browser-compatible
        const alwaysTranscode = ['.mkv', '.avi', '.flv', '.wmv', '.vob', '.mpg', '.mpeg', '.m2v'];
        if (alwaysTranscode.includes(extension)) {
            console.log(`🎬 Format ${extension} requires transcoding for browser compatibility`);
            return true;
        }
        
        // Detect HEVC/H.265 codec by filename patterns (common in modern torrents)
        const hevcPatterns = [
            /x265/i, /hevc/i, /h\.?265/i, /10bit/i, /10bits/i, 
            /hdr/i, /uhd/i, /2160p/i, /4k/i
        ];
        if (hevcPatterns.some(pattern => pattern.test(name))) {
            console.log(`🎬 HEVC/H.265 codec detected in filename, transcoding required`);
            return true;
        }
        
        // For MP4 and WebM, assume they're browser-compatible unless codec is detected as incompatible
        return false;
    };
    
    const requiresTranscoding = needsTranscoding(file.name, fileExtension);
    console.log(`🎬 File: ${file.name}, Extension: ${fileExtension}, Needs transcoding: ${requiresTranscoding}`);

    if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const actualEnd = Math.min(end, fileSize - 1);
        const chunksize = (actualEnd - start) + 1;
        if (!requiresTranscoding) {
            // Direct stream for browser-compatible formats
            console.log(`🎬 Streaming ${file.name} directly without transcoding`);
            let stream;
            if (file.isLocalFile) {
                // Use Node.js fs.createReadStream for local files
                stream = fs.createReadStream(file.path, { start, end: actualEnd });
            } else {
                // Use WebTorrent's createReadStream for torrented files
                stream = file.createReadStream({ start, end: actualEnd });
            }
            return { stream, start, end: actualEnd, fileSize, chunksize, mimeType };
        } else {
            // Real-time transcoding for incompatible formats (HEVC, MKV, etc.)
            console.log(`🎬 Transcoding ${file.name} to browser-compatible H.264/MP4 format`);
            let ffmpeg;
            try {
                ffmpeg = spawn('ffmpeg', [
                    '-i', 'pipe:0',
                    // REAL-TIME STREAMING OPTIMIZATIONS
                    '-f', 'mp4',
                    '-movflags', 'frag_keyframe+empty_moov+faststart+dash+delay_moov',
                    '-frag_duration', '500000',  // Smaller fragments for faster startup
                    '-min_frag_duration', '500000',
                    
                    // VIDEO ENCODING - Optimized for CCX43 (16 vCPUs, 32GB RAM)
                    '-vcodec', 'libx264',
                    '-preset', 'veryfast',  // Better quality than superfast, still fast on CCX43
                    '-tune', 'zerolatency',
                    '-profile:v', 'main',  // Better compression than baseline
                    '-level', '4.0',  // Higher level for better quality
                    '-pix_fmt', 'yuv420p',
                    '-vf', 'scale=trunc(iw/2)*2:trunc(ih/2)*2',

                    // OPTIMIZED BITRATE CONTROL for CCX43
                    '-crf', '23',  // Better quality (CCX43 can handle it)
                    '-maxrate', '5000k',  // Higher bitrate for better quality
                    '-bufsize', '2500k',  // Larger buffer (more RAM available)
                    '-g', '30',  // Standard keyframe interval
                    '-keyint_min', '15',
                    '-sc_threshold', '0',
                    
                    // AUDIO ENCODING - Fast settings
                    '-acodec', 'aac',
                    '-ac', '2',  // Force stereo
                    '-ar', '44100',  // Standard sample rate
                    '-ab', '128k',  // Lower audio bitrate
                    
                    // STREAMING OPTIMIZATIONS for CCX43
                    '-flush_packets', '1',  // Immediate packet flushing
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts+flush_packets',  // Generate PTS and flush packets
                    '-threads', '8',  // Optimal for CCX43 (leave cores for other processes)
                    '-thread_type', 'slice',  // Better threading for x264
                    '-deadline', 'realtime',  // Real-time encoding priority
                    '-cpu-used', '6',  // Balanced speed/quality for CCX43
                    '-probesize', '1M',  // Larger probe size (more RAM available)
                    '-analyzeduration', '2000000',  // 2 second analysis for better detection
                    '-y',
                    'pipe:1'
                ], { stdio: ['pipe', 'pipe', 'pipe'] });
                
                // Enhanced FFmpeg error handling with proper stream cleanup
                let streamClosed = false;
                
                ffmpeg.on('error', (err) => {
                    if (streamClosed) return;
                    streamClosed = true;
                    console.error(`🎬 FFmpeg process error: ${err.message}`);
                    if (err.code === 'ENOENT') {
                        console.error('🎬 FFmpeg not found. Please install ffmpeg and ensure it is in your PATH.');
                    }
                    // Cleanup streams on error
                    try {
                        if (ffmpeg.stdin && !ffmpeg.stdin.destroyed) ffmpeg.stdin.destroy();
                        if (ffmpeg.stdout && !ffmpeg.stdout.destroyed) ffmpeg.stdout.destroy();
                        if (ffmpeg.stderr && !ffmpeg.stderr.destroyed) ffmpeg.stderr.destroy();
                    } catch (cleanupErr) {
                        console.error('🎬 Error cleaning up FFmpeg streams:', cleanupErr.message);
                    }
                });
                
                ffmpeg.stderr.on('data', (data) => {
                    const errorMsg = data.toString();
                    if (errorMsg.includes('error') || errorMsg.includes('failed')) {
                        console.error(`🎬 FFmpeg stderr: ${errorMsg.substring(0, 200)}`);
                    }
                });
                
                ffmpeg.on('close', (code) => {
                    if (streamClosed) return;
                    if (code !== 0) {
                        console.error(`🎬 FFmpeg process exited with code ${code}`);
                    }
                });
                
                // Test if FFmpeg process started successfully
                if (!ffmpeg.pid) {
                    throw new Error('FFmpeg process failed to start');
                }
                
                let fileStream;
                if (file.isLocalFile) {
                    // Use Node.js fs.createReadStream for local files
                    fileStream = fs.createReadStream(file.path);
                } else {
                    // Use WebTorrent's createReadStream for torrented files
                    fileStream = file.createReadStream();
                }
                
                // Handle file stream errors
                fileStream.on('error', (err) => {
                    if (streamClosed) return;
                    streamClosed = true;
                    console.error('🎬 File stream error:', err.message);
                    try {
                        if (ffmpeg.stdin && !ffmpeg.stdin.destroyed) ffmpeg.stdin.destroy();
                        ffmpeg.kill('SIGKILL');
                    } catch (killErr) {
                        console.error('🎬 Error killing FFmpeg process:', killErr.message);
                    }
                });
                
                // Pipe with error handling
                fileStream.pipe(ffmpeg.stdin).on('error', (err) => {
                    if (streamClosed) return;
                    streamClosed = true;
                    console.error('🎬 Pipe error:', err.message);
                });
            } catch (err) {
                console.error('🎬 Failed to start FFmpeg transcoding:', err.message);
                if (err.code === 'ENOENT' || err.message.includes('not found')) {
                    return { error: 'FFmpeg not found. Please install ffmpeg and ensure it is in your PATH.' };
                }
                return { error: 'Failed to start video transcoding: ' + err.message };
            }
            return {
                stream: ffmpeg.stdout,
                start: 0,
                end: fileSize - 1,
                fileSize,
                chunksize: fileSize,
                mimeType: 'video/mp4',
                transcoding: true
            };
        }
    } else {
        if (!requiresTranscoding) {
            console.log(`🎬 Streaming ${file.name} directly without transcoding (full file)`);
            let stream;
            if (file.isLocalFile) {
                // Use Node.js fs.createReadStream for local files
                stream = fs.createReadStream(file.path);
            } else {
                // Use WebTorrent's createReadStream for torrented files
                stream = file.createReadStream();
            }
            return { stream, start: 0, end: fileSize - 1, fileSize, chunksize: fileSize, mimeType };
        } else {
            console.log(`🎬 Transcoding ${file.name} to browser-compatible H.264/MP4 format (full file)`);
            let ffmpeg;
            try {
                ffmpeg = spawn('ffmpeg', [
                    '-i', 'pipe:0',
                    // REAL-TIME STREAMING OPTIMIZATIONS
                    '-f', 'mp4',
                    '-movflags', 'frag_keyframe+empty_moov+faststart+dash+delay_moov',
                    '-frag_duration', '500000',  // Smaller fragments for faster startup
                    '-min_frag_duration', '500000',
                    
                    // VIDEO ENCODING - Optimized for SPEED and LOW LATENCY
                    '-vcodec', 'libx264',
                    '-preset', 'superfast',  // Faster than ultrafast for better compatibility
                    '-tune', 'zerolatency',
                    '-profile:v', 'baseline',  // More compatible than main
                    '-level', '3.1',  // Lower level for better compatibility
                    '-pix_fmt', 'yuv420p',
                    '-vf', 'scale=trunc(iw/2)*2:trunc(ih/2)*2',
                    
                    // REAL-TIME BITRATE CONTROL
                    '-crf', '28',  // Slightly lower quality for speed
                    '-maxrate', '3000k',  // Lower max bitrate for stability
                    '-bufsize', '1500k',  // Much smaller buffer for low latency
                    '-g', '15',  // More frequent keyframes for seeking
                    '-keyint_min', '15',
                    '-sc_threshold', '0',
                    
                    // AUDIO ENCODING - Fast settings
                    '-acodec', 'aac',
                    '-ac', '2',  // Force stereo
                    '-ar', '44100',  // Standard sample rate
                    '-ab', '128k',  // Lower audio bitrate
                    
                    // STREAMING OPTIMIZATIONS
                    '-flush_packets', '1',  // Immediate packet flushing
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts+flush_packets',  // Generate PTS and flush packets
                    '-threads', '0',  // Use all CPU cores
                    '-deadline', 'realtime',  // Real-time encoding priority
                    '-cpu-used', '8',  // Fastest CPU usage for VP8/9 (ignored by x264 but safe)
                    '-probesize', '32',  // Faster input probing
                    '-analyzeduration', '1000000',  // 1 second analysis
                    '-y',
                    'pipe:1'
                ], { stdio: ['pipe', 'pipe', 'pipe'] });
                
                // Enhanced FFmpeg error handling with proper stream cleanup
                let streamClosed = false;
                
                ffmpeg.on('error', (err) => {
                    if (streamClosed) return;
                    streamClosed = true;
                    console.error(`🎬 FFmpeg process error: ${err.message}`);
                    if (err.code === 'ENOENT') {
                        console.error('🎬 FFmpeg not found. Please install ffmpeg and ensure it is in your PATH.');
                    }
                    // Cleanup streams on error
                    try {
                        if (ffmpeg.stdin && !ffmpeg.stdin.destroyed) ffmpeg.stdin.destroy();
                        if (ffmpeg.stdout && !ffmpeg.stdout.destroyed) ffmpeg.stdout.destroy();
                        if (ffmpeg.stderr && !ffmpeg.stderr.destroyed) ffmpeg.stderr.destroy();
                    } catch (cleanupErr) {
                        console.error('🎬 Error cleaning up FFmpeg streams:', cleanupErr.message);
                    }
                });
                
                ffmpeg.stderr.on('data', (data) => {
                    const errorMsg = data.toString();
                    if (errorMsg.includes('error') || errorMsg.includes('failed')) {
                        console.error(`🎬 FFmpeg stderr: ${errorMsg.substring(0, 200)}`);
                    }
                });
                
                ffmpeg.on('close', (code) => {
                    if (streamClosed) return;
                    if (code !== 0) {
                        console.error(`🎬 FFmpeg process exited with code ${code}`);
                    }
                });
                
                // Test if FFmpeg process started successfully
                if (!ffmpeg.pid) {
                    throw new Error('FFmpeg process failed to start');
                }
                
                let fileStream;
                if (file.isLocalFile) {
                    // Use Node.js fs.createReadStream for local files
                    fileStream = fs.createReadStream(file.path);
                } else {
                    // Use WebTorrent's createReadStream for torrented files
                    fileStream = file.createReadStream();
                }
                
                // Handle file stream errors
                fileStream.on('error', (err) => {
                    if (streamClosed) return;
                    streamClosed = true;
                    console.error('🎬 File stream error:', err.message);
                    try {
                        if (ffmpeg.stdin && !ffmpeg.stdin.destroyed) ffmpeg.stdin.destroy();
                        ffmpeg.kill('SIGKILL');
                    } catch (killErr) {
                        console.error('🎬 Error killing FFmpeg process:', killErr.message);
                    }
                });
                
                // Pipe with error handling
                fileStream.pipe(ffmpeg.stdin).on('error', (err) => {
                    if (streamClosed) return;
                    streamClosed = true;
                    console.error('🎬 Pipe error:', err.message);
                });
            } catch (err) {
                console.error('🎬 Failed to start FFmpeg transcoding:', err.message);
                if (err.code === 'ENOENT' || err.message.includes('not found')) {
                    return { error: 'FFmpeg not found. Please install ffmpeg and ensure it is in your PATH.' };
                }
                return { error: 'Failed to start video transcoding: ' + err.message };
            }
            return {
                stream: ffmpeg.stdout,
                start: 0,
                end: fileSize - 1,
                fileSize,
                chunksize: fileSize,
                mimeType: 'video/mp4',
                transcoding: true
            };
        }
    }
}

// NEW: Get available subtitle files for a torrent
export function getAvailableSubtitles(infoHash) {
    console.log(`🎬 Getting available subtitles for infoHash: ${infoHash}`);
    
    const activeStream = activeTorrents[infoHash];
    if (!activeStream || !activeStream.file) {
        console.log(`🎬 No active stream found for infoHash: ${infoHash}`);
        return [];
    }

    const streamInfo = activeStream.file;
    const subtitles = [];
    
    try {
        // For local files, look for subtitle files in the same directory or Subs folder
        if (streamInfo.isLocalFile && streamInfo.path) {
            const videoDir = path.dirname(streamInfo.path);
            const videoBaseName = path.basename(streamInfo.name, path.extname(streamInfo.name));
            
            // Check for subtitle files in the main directory
            const mainDirFiles = fs.readdirSync(videoDir);
            mainDirFiles.forEach(file => {
                if (file.endsWith('.srt')) {
                    const subtitlePath = path.join(videoDir, file);
                    const language = extractLanguageFromSubtitle(file);
                    subtitles.push({
                        path: file,
                        fullPath: subtitlePath,
                        language: language.name,
                        code: language.code,
                        isSDH: language.isSDH,
                        isForced: language.isForced
                    });
                }
            });
            
            // Check for Subs subdirectory (common in YTS releases)
            const subsDir = path.join(videoDir, 'Subs');
            if (fs.existsSync(subsDir) && fs.statSync(subsDir).isDirectory()) {
                const subFiles = fs.readdirSync(subsDir);
                subFiles.forEach(file => {
                    if (file.endsWith('.srt')) {
                        const subtitlePath = path.join(subsDir, file);
                        const language = extractLanguageFromSubtitle(file);
                        subtitles.push({
                            path: `Subs/${file}`,
                            fullPath: subtitlePath,
                            language: language.name,
                            code: language.code,
                            isSDH: language.isSDH,
                            isForced: language.isForced
                        });
                    }
                });
            }
        }
        
        // For torrented files, look for subtitle files in the torrent
        else if (streamInfo.torrent && streamInfo.torrent.files) {
            streamInfo.torrent.files.forEach(file => {
                if (file.name.endsWith('.srt')) {
                    const language = extractLanguageFromSubtitle(file.name);
                    subtitles.push({
                        path: file.path,
                        fullPath: file.path,
                        language: language.name,
                        code: language.code,
                        isSDH: language.isSDH,
                        isForced: language.isForced,
                        file: file // Keep reference to torrent file
                    });
                }
            });
        }
        
    } catch (error) {
        console.error(`🎬 Error scanning for subtitles:`, error);
    }
    
    console.log(`🎬 Found ${subtitles.length} subtitle files for ${infoHash}`);
    return subtitles;
}

// NEW: Extract language information from subtitle filename
function extractLanguageFromSubtitle(filename) {
    const name = filename.toLowerCase();
    
    // Common language mappings
    const languageMap = {
        'english': { name: 'English', code: 'en' },
        'eng': { name: 'English', code: 'en' },
        'spanish': { name: 'Spanish', code: 'es' },
        'spa': { name: 'Spanish', code: 'es' },
        'french': { name: 'French', code: 'fr' },
        'fre': { name: 'French', code: 'fr' },
        'german': { name: 'German', code: 'de' },
        'ger': { name: 'German', code: 'de' },
        'italian': { name: 'Italian', code: 'it' },
        'ita': { name: 'Italian', code: 'it' },
        'portuguese': { name: 'Portuguese', code: 'pt' },
        'por': { name: 'Portuguese', code: 'pt' },
        'japanese': { name: 'Japanese', code: 'ja' },
        'jpn': { name: 'Japanese', code: 'ja' },
        'korean': { name: 'Korean', code: 'ko' },
        'kor': { name: 'Korean', code: 'ko' },
        'chinese': { name: 'Chinese', code: 'zh' },
        'chi': { name: 'Chinese', code: 'zh' },
        'russian': { name: 'Russian', code: 'ru' },
        'rus': { name: 'Russian', code: 'ru' },
        'arabic': { name: 'Arabic', code: 'ar' },
        'ara': { name: 'Arabic', code: 'ar' },
        'dutch': { name: 'Dutch', code: 'nl' },
        'dut': { name: 'Dutch', code: 'nl' },
        'swedish': { name: 'Swedish', code: 'sv' },
        'swe': { name: 'Swedish', code: 'sv' },
        'finnish': { name: 'Finnish', code: 'fi' },
        'fin': { name: 'Finnish', code: 'fi' },
        'norwegian': { name: 'Norwegian', code: 'no' },
        'nob': { name: 'Norwegian', code: 'no' },
        'danish': { name: 'Danish', code: 'da' },
        'dan': { name: 'Danish', code: 'da' },
        'polish': { name: 'Polish', code: 'pl' },
        'pol': { name: 'Polish', code: 'pl' },
        'hungarian': { name: 'Hungarian', code: 'hu' },
        'hun': { name: 'Hungarian', code: 'hu' },
        'czech': { name: 'Czech', code: 'cs' },
        'cze': { name: 'Czech', code: 'cs' },
        'slovak': { name: 'Slovak', code: 'sk' },
        'turkish': { name: 'Turkish', code: 'tr' },
        'tur': { name: 'Turkish', code: 'tr' },
        'hebrew': { name: 'Hebrew', code: 'he' },
        'heb': { name: 'Hebrew', code: 'he' },
        'greek': { name: 'Greek', code: 'el' },
        'gre': { name: 'Greek', code: 'el' },
        'thai': { name: 'Thai', code: 'th' },
        'vietnamese': { name: 'Vietnamese', code: 'vi' },
        'vie': { name: 'Vietnamese', code: 'vi' },
        'ukrainian': { name: 'Ukrainian', code: 'uk' },
        'ukr': { name: 'Ukrainian', code: 'uk' },
        'romanian': { name: 'Romanian', code: 'ro' },
        'rum': { name: 'Romanian', code: 'ro' },
        'croatian': { name: 'Croatian', code: 'hr' },
        'hrv': { name: 'Croatian', code: 'hr' },
        'indonesian': { name: 'Indonesian', code: 'id' },
        'ind': { name: 'Indonesian', code: 'id' },
        'malay': { name: 'Malay', code: 'ms' },
        'may': { name: 'Malay', code: 'ms' },
        'filipino': { name: 'Filipino', code: 'tl' },
        'fil': { name: 'Filipino', code: 'tl' },
        'catalan': { name: 'Catalan', code: 'ca' },
        'cat': { name: 'Catalan', code: 'ca' },
        'basque': { name: 'Basque', code: 'eu' },
        'baq': { name: 'Basque', code: 'eu' },
        'galician': { name: 'Galician', code: 'gl' },
        'glg': { name: 'Galician', code: 'gl' }
    };
    
    // Check if it's SDH (Subtitles for Deaf and Hard of hearing)
    const isSDH = name.includes('sdh') || name.includes('(sdh)');
    
    // Check if it's forced subtitles
    const isForced = name.includes('forced') || name.includes('(forced)');
    
    // Try to extract language from filename
    for (const [key, value] of Object.entries(languageMap)) {
        if (name.includes(key)) {
            return {
                name: value.name,
                code: value.code,
                isSDH,
                isForced
            };
        }
    }
    
    // If no language detected, try to extract from the file extension pattern
    const parts = filename.split('.');
    if (parts.length >= 3) {
        const langPart = parts[parts.length - 2].toLowerCase();
        const langInfo = languageMap[langPart];
        if (langInfo) {
            return {
                name: langInfo.name,
                code: langInfo.code,
                isSDH,
                isForced
            };
        }
    }
    
    // Default to Unknown if no language detected
    return {
        name: 'Unknown',
        code: 'unknown',
        isSDH,
        isForced
    };
}

// NEW: Get subtitle file content
export function getSubtitleFile(infoHash, subtitlePath) {
    console.log(`🎬 Getting subtitle file: ${subtitlePath} for infoHash: ${infoHash}`);
    
    const activeStream = activeTorrents[infoHash];
    if (!activeStream || !activeStream.file) {
        console.log(`🎬 No active stream found for infoHash: ${infoHash}`);
        return null;
    }

    const streamInfo = activeStream.file;
    
    try {
        // For local files
        if (streamInfo.isLocalFile && streamInfo.path) {
            const videoDir = path.dirname(streamInfo.path);
            const fullSubtitlePath = path.join(videoDir, subtitlePath);
            
            if (!fs.existsSync(fullSubtitlePath)) {
                console.error(`🎬 Subtitle file not found: ${fullSubtitlePath}`);
                return { error: 'Subtitle file not found' };
            }
            
            const content = fs.readFileSync(fullSubtitlePath, 'utf8');
            console.log(`🎬 Successfully loaded subtitle: ${subtitlePath}`);
            return { content };
        }
        
        // For torrented files
        else if (streamInfo.torrent && streamInfo.torrent.files) {
            const subtitleFile = streamInfo.torrent.files.find(file => file.path === subtitlePath);
            
            if (!subtitleFile) {
                console.error(`🎬 Subtitle file not found in torrent: ${subtitlePath}`);
                return { error: 'Subtitle file not found in torrent' };
            }
            
            // For torrent files, we need to read the file differently
            // This is a simplified approach - in a real implementation, you'd need to handle torrent file reading
            console.log(`🎬 Torrent subtitle access not fully implemented yet: ${subtitlePath}`);
            return { error: 'Torrent subtitle access not implemented' };
        }
        
    } catch (error) {
        console.error(`🎬 Error reading subtitle file:`, error);
        return { error: error.message };
    }
    
    return { error: 'Unknown subtitle source' };
}