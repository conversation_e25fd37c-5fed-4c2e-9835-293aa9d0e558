import React, { useState, useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import MediaCard from '../components/MediaCard';
import EnhancedVideoPlayer from '../components/EnhancedVideoPlayer';
import TrailerPlayer from '../components/TrailerPlayer';
import AdvancedSearch from '../components/AdvancedSearch';
import Fuse from 'fuse.js';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import { watchlistStorage } from '../utils/watchlistStorage';
import { TypewriterText, funnyLoadingPhrases, getRandomPhrase } from '../utils/loadingPhrases.jsx';
import { apiFetch } from '../utils/api';
import TrailerModal from '../components/TrailerModal';

const TMDB_API_KEY = import.meta.env.VITE_TMDB_API_KEY;

function useQuery() {
  return new URLSearchParams(useLocation().search);
}

const SearchResults = () => {
  const query = useQuery().get('q') || '';
  const { profile: currentProfile, isProfileLoaded } = useProfile();
  const { user } = useAuth();
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingType, setLoadingType] = useState('');
  const [error, setError] = useState(null);
  const [searchStats, setSearchStats] = useState({ total: 0, movies: 0, shows: 0 });
  const [videoPlayer, setVideoPlayer] = useState({ isOpen: false, magnetLink: null, title: null });
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, trailer: null, content: null });
  const [watchlist, setWatchlist] = useState([]);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [searchFilters, setSearchFilters] = useState({});
  const navigate = useNavigate();

  // Scroll to top on initial load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Enhanced search function with advanced filters
  const performSearch = async (searchQuery, filters = {}) => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    setError(null);
    setResults([]);
    setSearchFilters(filters);
    
    try {
      console.log(`🔍 Starting enhanced search for: "${searchQuery}" with filters:`, filters);
      
      // Use advanced search endpoint if filters are applied, otherwise use comprehensive search
      const hasFilters = Object.values(filters).some(value => value && value !== 'all' && value !== '');
      const endpoint = hasFilters ? '/api/search-advanced/advanced' : '/api/search-comprehensive';
      
      const searchParams = new URLSearchParams({
        query: searchQuery.trim(),
        ...filters
      });
      
      const response = await apiFetch(`${endpoint}?${searchParams.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Search API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Search failed');
      }
      
      console.log(`🔍 Enhanced search returned: ${data.stats.total} total results (${data.stats.movies} movies, ${data.stats.shows} shows)`);
      
      if (data.results && data.results.length > 0) {
        // Apply fuzzy search for better matching if needed
        let finalResults = data.results;
        
        // If the search query is very specific, apply fuzzy filtering for better relevance
        if (searchQuery.length > 3) {
          const fuse = new Fuse(data.results, {
            keys: [
              { name: 'title', weight: 0.3 },
              { name: 'name', weight: 0.3 },
              { name: 'original_title', weight: 0.2 },
              { name: 'original_name', weight: 0.2 },
              { name: 'overview', weight: 0.1 }
            ],
            threshold: 0.7,
            includeScore: true,
            includeMatches: true,
            minMatchCharLength: 2,
            ignoreLocation: true,
            findAllMatches: true
          });
          
          const fuzzyResults = fuse.search(searchQuery);
          
          if (fuzzyResults.length > 0) {
            finalResults = fuzzyResults.map(result => ({
              ...result.item,
              searchScore: 1 - (result.score || 0)
            })).sort((a, b) => b.searchScore - a.searchScore);
            
            console.log(`🔍 Applied fuzzy filtering: ${finalResults.length} relevant results`);
          }
        }
        
        // Smart scoring and prioritization for final ranking
        const scoredResults = finalResults.map(item => {
          let score = item.searchScore || (item.popularity || 0) * 0.001;
          
          // Boost exact matches
          const title = item.title || item.name || '';
          const originalTitle = item.original_title || item.original_name || '';
          if (title.toLowerCase() === searchQuery.toLowerCase() || 
              originalTitle.toLowerCase() === searchQuery.toLowerCase()) {
            score += 1.0;
          }
          
          // Boost partial matches at start of title
          if (title.toLowerCase().startsWith(searchQuery.toLowerCase()) ||
              originalTitle.toLowerCase().startsWith(searchQuery.toLowerCase())) {
            score += 0.5;
          }
          
          // Boost by ratings and popularity
          score += (item.vote_average || 0) * 0.02;
          score += (item.vote_count || 0) * 0.0001;
          
          // Boost recent content
          const releaseDate = item.release_date || item.first_air_date;
          if (releaseDate) {
            const year = new Date(releaseDate).getFullYear();
            const currentYear = new Date().getFullYear();
            if (year >= currentYear - 3) score += 0.2;
          }
          
          return { ...item, finalScore: score };
        });
        
        // Sort by final score
        scoredResults.sort((a, b) => b.finalScore - a.finalScore);
        
        // Set results and stats
        setResults(scoredResults);
        setSearchStats({
          total: scoredResults.length,
          movies: data.stats.movies,
          shows: data.stats.shows,
          totalAvailable: data.stats.totalMovieResults + data.stats.totalShowResults
        });
        
        console.log(`🔍 Final results: ${scoredResults.length} items displayed`);
        
      } else {
        setError(`No results found for &quot;${searchQuery}&quot;. Try different keywords or check for typos.`);
      }
      
    } catch (error) {
      console.error('🔍 Search error:', error);
      
      // Fallback to direct TMDB search if advanced search fails
      console.log('🔍 Falling back to direct TMDB search...');
      try {
        const fallbackPromises = [
          apiFetch(`/api/search/movie?query=${encodeURIComponent(searchQuery)}&include_adult=false&page=1`)
            .then(res => res.json())
            .then(data => (data.results || []).map(item => ({ ...item, media_type: 'movie' }))),
          
          apiFetch(`/api/search/tv?query=${encodeURIComponent(searchQuery)}&include_adult=false&page=1`)
            .then(res => res.json())
            .then(data => (data.results || []).map(item => ({ ...item, media_type: 'tv' })))
        ];
        
        const fallbackResults = await Promise.allSettled(fallbackPromises);
        let fallbackItems = [];
        
        fallbackResults.forEach((result) => {
          if (result.status === 'fulfilled' && Array.isArray(result.value)) {
            fallbackItems = fallbackItems.concat(result.value);
          }
        });
        
        if (fallbackItems.length > 0) {
          fallbackItems.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
          setResults(fallbackItems);
          const movies = fallbackItems.filter(item => item.media_type === 'movie').length;
          const shows = fallbackItems.filter(item => item.media_type === 'tv').length;
          setSearchStats({ total: fallbackItems.length, movies, shows });
          console.log(`🔍 Fallback search returned ${fallbackItems.length} results`);
        } else {
          setError('Search failed. Please check your connection and try again.');
        }
        
      } catch (fallbackError) {
        console.error('🔍 Fallback search also failed:', fallbackError);
        setError('Search failed. Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Trigger search when query changes
  useEffect(() => {
    if (query) {
      performSearch(query);
    }
  }, [query]);

  // Update watchlist when profile changes
  useEffect(() => {
    if (!isProfileLoaded) return;
    
    const loadWatchlist = async () => {
      if (user && currentProfile && currentProfile.id) {
        const stored = await watchlistStorage.getWatchlist(user.id, currentProfile.id);
        setWatchlist(stored);
      } else {
        setWatchlist([]);
      }
    };
    
    loadWatchlist();
  }, [user, currentProfile, isProfileLoaded]);

  // Watchlist helpers
  const isInWatchlist = (id) => watchlist.some(item => String(item.id) === String(id));
  
  const toggleWatchlist = async (media) => {
    if (!user || !currentProfile || !currentProfile.id) return;
    
    const exists = watchlist.some(item => String(item.id) === String(media.id));
    let success;
    
    if (exists) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, media.id, media.media_type);
      if (success) {
        setWatchlist(prev => prev.filter(item => String(item.id) !== String(media.id)));
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, media);
      if (success) {
        setWatchlist(prev => [...prev, media]);
      }
    }
  };

  // Play handler with robust torrent search
  const handlePlayMedia = async (media) => {
    if (!media) return;

    try {
      const type = media.media_type === 'tv' ? 'tv' : 'movie';
      const searchTitle = media.title || media.name;
      console.log(`🎬 Searching torrents for: ${searchTitle} (${type})`);
      
      const response = await apiFetch(`/api/search?query=${encodeURIComponent(searchTitle)}&type=${type}`);
      const data = await response.json();
      
      if (data.success && data.results && data.results.length > 0) {
        const validTorrents = data.results
          .filter(torrent => torrent.magnetLink && torrent.seeders > 0)
          .sort((a, b) => (b.seeders || 0) - (a.seeders || 0))
          .slice(0, 5);
          
        if (validTorrents.length > 0) {
          const bestTorrent = validTorrents[0];
          setVideoPlayer({ 
            isOpen: true, 
            magnetLink: bestTorrent.magnetLink, 
            title: searchTitle,
            fallbackTorrents: validTorrents.slice(1),
            contentId: `${type}_${media.id}`, // Use consistent format: movie_123 or tv_123
            contentType: type,
            posterUrl: media.poster_path ? `https://image.tmdb.org/t/p/w500${media.poster_path}` : null,
            episodeInfo: null
          });
        } else {
          alert('No playable torrent found. Try a different title.');
        }
      } else {
        alert(`No torrents found for &quot;${searchTitle}&quot;. This title may not be available for streaming.`);
      }
    } catch (error) {
      console.error('🎬 Error searching torrents:', error);
      alert('Failed to search for streaming sources. Please try again.');
    }
  };

  // Trailer handler with better error handling
  const handleTrailer = async (media) => {
    if (!media) return;
    try {
      const endpoint = media.media_type === 'movie'
        ? `/api/movies/${media.id}/videos`
        : `/api/tv/${media.id}/videos`;
      const response = await apiFetch(endpoint);
      const data = await response.json();
      const trailers = data.results?.filter(video =>
        video.site === 'YouTube' &&
        (video.type === 'Trailer' || video.type === 'Teaser')
      ) || [];
      if (trailers.length > 0) {
        setTrailerModal({ isOpen: true, trailer: trailers[0], content: media });
      } else {
        alert('No trailer available for this title.');
      }
    } catch (error) {
      alert('Failed to load trailer.');
    }
  };

  const closeVideoPlayer = () => {
    setVideoPlayer({ isOpen: false, magnetLink: null, title: null });
  };

  const closeTrailerModal = () => setTrailerModal({ isOpen: false, trailer: null, content: null });

  const categorizedResults = useMemo(() => {
    const movies = results.filter(item => item.media_type === 'movie');
    const shows = results.filter(item => item.media_type === 'tv');
    return { movies, shows };
  }, [results]);

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-4xl font-bold text-white">
              Search Results for &quot;{query}&quot;
            </h1>
            <button
              onClick={() => setShowAdvancedSearch(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
              </svg>
              <span>Advanced Search</span>
            </button>
          </div>
          {!loading && !error && searchStats.total > 0 && (
            <div className="space-y-3">
              <div className="flex items-center space-x-6 text-gray-400">
                <span className="text-lg">
                  <span className="text-white font-semibold">{searchStats.total}</span> results displayed
                </span>
                {searchStats.movies > 0 && (
                  <span>
                    <span className="text-blue-400 font-semibold">{searchStats.movies}</span> movies
                  </span>
                )}
                {searchStats.shows > 0 && (
                  <span>
                    <span className="text-green-400 font-semibold">{searchStats.shows}</span> shows
                  </span>
                )}
                {Object.keys(searchFilters).length > 0 && (
                  <span className="text-yellow-400">
                    • Filters applied
                  </span>
                )}
              </div>
              {searchStats.totalAvailable && searchStats.totalAvailable > searchStats.total && (
                <div className="text-sm text-gray-500">
                  <span className="text-gray-400">
                    Showing top results from <span className="text-white font-semibold">{searchStats.totalAvailable.toLocaleString()}</span> total matches on TMDB
                  </span>
                  <span className="text-gray-600 ml-2">• Search more specifically to refine results</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="text-center max-w-md px-4">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-white text-xl mb-4">Searching for &quot;{query}&quot;...</p>
              
              {/* Funny Typewriter Message */}
              <div className="text-cyan-300 text-lg mb-4 min-h-[2rem]">
                <TypewriterText
                  text={getRandomPhrase(funnyLoadingPhrases)}
                  speed={40}
                  className="loading-glow"
                  showCursor={true}
                />
              </div>
              
              <p className="text-gray-500 text-sm opacity-60">Checking multiple sources for best results</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-16">
            <div className="text-red-400 text-xl mb-4">{error}</div>
            <div className="text-gray-400 mb-6">
              <p>Try these search tips:</p>
              <ul className="text-left inline-block mt-2 space-y-1">
                <li>• Check your spelling</li>
                <li>• Use different keywords</li>
                <li>• Try the original title</li>
                <li>• Remove special characters</li>
              </ul>
            </div>
            <button 
              onClick={() => performSearch(query)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Results Grid */}
        {!loading && !error && results.length > 0 && (
          <div className="space-y-12">
            {/* All Results */}
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">All Results</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 justify-items-center">
                {results.map(item => (
                  <div key={`${item.id}-${item.media_type}`} className="w-full max-w-[200px]">
                    <MediaCard
                      media={item}
                      isInWatchlist={isInWatchlist(item.id)}
                      toggleWatchlist={() => toggleWatchlist(item)}
                      onClick={() => {
                        sessionStorage.setItem('lastSearchQuery', query);
                        const path = item.media_type === 'movie' ? `/movie/${item.id}` : `/show/${item.id}`;
                        navigate(path);
                      }}
                      onPlay={(media) => handlePlayMedia(media)}
                      onMoreInfo={(media) => handleTrailer(media)}
                      showActions={true}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Movies Section */}
            {categorizedResults.movies.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">
                  Movies ({categorizedResults.movies.length})
                </h2>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 justify-items-center">
                  {categorizedResults.movies.slice(0, 12).map(item => (
                    <div key={`movie-${item.id}`} className="w-full max-w-[200px]">
                      <MediaCard
                        media={item}
                        isInWatchlist={isInWatchlist(item.id)}
                        toggleWatchlist={() => toggleWatchlist(item)}
                        onClick={() => {
                          sessionStorage.setItem('lastSearchQuery', query);
                          navigate(`/movie/${item.id}`);
                        }}
                        onPlay={(media) => handlePlayMedia(media)}
                        onMoreInfo={(media) => handleTrailer(media)}
                        showActions={true}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* TV Shows Section */}
            {categorizedResults.shows.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-6">
                  TV Shows ({categorizedResults.shows.length})
                </h2>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 justify-items-center">
                  {categorizedResults.shows.slice(0, 12).map(item => (
                    <div key={`tv-${item.id}`} className="w-full max-w-[200px]">
                      <MediaCard
                        media={item}
                        isInWatchlist={isInWatchlist(item.id)}
                        toggleWatchlist={() => toggleWatchlist(item)}
                        onClick={() => {
                          sessionStorage.setItem('lastSearchQuery', query);
                          navigate(`/show/${item.id}`);
                        }}
                        onPlay={(media) => handlePlayMedia(media)}
                        onMoreInfo={(media) => handleTrailer(media)}
                        showActions={true}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* No Results State */}
        {!loading && !error && results.length === 0 && query && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h2 className="text-2xl text-white mb-4">No results found</h2>
            <p className="text-gray-400 text-lg mb-6">
              We couldn&apos;t find anything for &quot;{query}&quot;
            </p>
            <div className="text-gray-400 mb-8">
              <p className="mb-4">Search suggestions:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto text-left">
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-white font-semibold mb-2">Check your spelling</h3>
                  <p className="text-sm">Make sure all words are spelled correctly</p>
                </div>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-white font-semibold mb-2">Try different keywords</h3>
                  <p className="text-sm">Use the original title or alternative names</p>
                </div>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-white font-semibold mb-2">Be more specific</h3>
                  <p className="text-sm">Include year or additional context</p>
                </div>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-white font-semibold mb-2">Browse categories</h3>
                  <p className="text-sm">Check Movies, TV Shows, or Anime sections</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Video Player */}
      {videoPlayer.isOpen && (
        <EnhancedVideoPlayer
          magnetLink={videoPlayer.magnetLink}
          title={videoPlayer.title}
          onClose={closeVideoPlayer}
          fallbackTorrents={videoPlayer.fallbackTorrents || []}
          contentId={videoPlayer.contentId}
          contentType={videoPlayer.contentType || "movie"}
          posterUrl={videoPlayer.posterUrl}
          episodeInfo={videoPlayer.episodeInfo}
        />
      )}

      {/* Trailer Modal */}
      <TrailerModal isOpen={trailerModal.isOpen} onClose={closeTrailerModal} trailer={trailerModal.trailer} content={trailerModal.content} />

      {/* Advanced Search Modal */}
      {showAdvancedSearch && (
        <AdvancedSearch
          onSearch={performSearch}
          onClose={() => setShowAdvancedSearch(false)}
          initialQuery={query}
        />
      )}
    </div>
  );
};

export default SearchResults; 