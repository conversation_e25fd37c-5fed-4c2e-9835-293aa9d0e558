import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';

const defaultAvatars = [
  '/avatars/avatar1.png',
  '/avatars/avatar2.png',
  '/avatars/avatar3.png',
  '/avatars/avatar4.png',
];

// Animated Profile Component
const AnimatedProfile = ({ children, className, isSelected = false }) => {
  const canvasRef = useRef(null);
  const [animationTime, setAnimationTime] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const animationRef = useRef();

  useEffect(() => {
    const animate = () => {
      setAnimationTime(prev => prev + 0.02);
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (canvasRef.current && (isHovered || isSelected)) {
      renderAnimatedBorder();
    } else if (canvasRef.current) {
      // Clear canvas when not hovered
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, 102, 102);
    }
  }, [animationTime, isHovered, isSelected]);

  const renderAnimatedBorder = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const canvasSize = 102;
    const dpr = window.devicePixelRatio || 1;
    
    canvas.width = canvasSize * dpr;
    canvas.height = canvasSize * dpr;
    ctx.scale(dpr, dpr);
    
    ctx.clearRect(0, 0, canvasSize, canvasSize);

    // Center the circle perfectly around the 6rem (96px) avatar
    const centerX = canvasSize / 2;
    const centerY = canvasSize / 2;
    const radius = 48.5; // Exactly around the 96px avatar (48px radius + 0.5px border offset)
    const strokeWidth = 6; // Thicker like the Torvie logo for more prominence

    // Animation timing - clockwise rotation
    const time = animationTime * 0.5;
    
    // Same color palettes as logo
    const purpleShades = [
        [138, 43, 226],   // Blue violet
        [75, 0, 130],     // Indigo  
        [147, 0, 211],    // Dark violet
        [123, 104, 238],  // Medium slate blue
        [102, 51, 153]    // Rebecca purple
    ];
    
    const cyanShades = [
        [0, 191, 255],    // Deep sky blue
        [0, 206, 209],    // Dark turquoise
        [64, 224, 208],   // Turquoise
        [0, 255, 255],    // Cyan
        [32, 178, 170]    // Light sea green
    ];
    
    // Same interpolation function as logo
    const interpolateColor = (colorArray, time) => {
        const scaledTime = time % colorArray.length;
        const index1 = Math.floor(scaledTime);
        const index2 = (index1 + 1) % colorArray.length;
        const factor = scaledTime - index1;
        
        const color1 = colorArray[index1];
        const color2 = colorArray[index2];
        
        return [
            Math.round(color1[0] + (color2[0] - color1[0]) * factor),
            Math.round(color1[1] + (color2[1] - color1[1]) * factor),
            Math.round(color1[2] + (color2[2] - color1[2]) * factor)
        ];
    };

    // Create flowing gradient - clockwise
    ctx.lineWidth = strokeWidth;
    const rotationSpeed = time * 1.5; // Positive for clockwise
    
    const allColors = [...purpleShades, ...cyanShades];
    const color1 = interpolateColor(allColors, time);
    const color2 = interpolateColor(allColors, time + 2);
    const color3 = interpolateColor(allColors, time + 4);
    const color4 = interpolateColor(allColors, time + 6);
    const color5 = interpolateColor(allColors, time + 8);
    
    const gradient = ctx.createConicGradient(rotationSpeed, centerX, centerY);
    gradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(0.15, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.25, `rgb(${color3[0]}, ${color3[1]}, ${color3[2]})`);
    gradient.addColorStop(0.4, `rgb(${color4[0]}, ${color4[1]}, ${color4[2]})`);
    gradient.addColorStop(0.6, `rgb(${color5[0]}, ${color5[1]}, ${color5[2]})`);
    gradient.addColorStop(0.75, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    gradient.addColorStop(0.85, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    gradient.addColorStop(1, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    
    // Draw animated circular border
    ctx.beginPath();
    ctx.strokeStyle = gradient;
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.stroke();
  };

  return (
    <div 
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`${className} relative inline-block`}
    >
      <canvas
        ref={canvasRef}
        style={{ 
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '102px',
          height: '102px',
          pointerEvents: 'none',
          zIndex: 1,
          opacity: isHovered || isSelected ? 1 : 0,
          transition: 'opacity 0.3s ease'
        }}
      />
      {children}
    </div>
  );
};

// Removed DeleteConfirmModal - now using edit functionality instead

const CreateProfileModal = ({ isOpen, onClose, onSubmit, newProfileName, setNewProfileName, newProfileAvatar, setNewProfileAvatar, creating }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <h2 className="modal-title">Create Profile</h2>
        <form onSubmit={onSubmit} className="create-form">
          <div className="form-group">
            <label className="form-label">Profile Name</label>
            <input
              type="text"
              placeholder="Profile Name"
              value={newProfileName}
              onChange={e => setNewProfileName(e.target.value)}
              className="form-input"
              required
            />
          </div>
          <div className="form-group">
            <label className="form-label">Choose Avatar</label>
            <div className="avatar-grid">
              {defaultAvatars.map((avatar, _idx) => (
                <button
                  key={avatar}
                  type="button"
                  onClick={() => setNewProfileAvatar(avatar)}
                  className={`avatar-option ${newProfileAvatar === avatar ? 'selected' : ''}`}
                >
                  <img
                    src={avatar}
                    alt={`Avatar ${_idx+1}`}
                    className="avatar-option-image"
                    onError={e => { e.target.style.display = 'none'; }}
                  />
                </button>
              ))}
            </div>
          </div>
          <div className="modal-buttons">
            <button
              type="submit"
              className="modal-button modal-button-primary"
              disabled={creating}
            >
              {creating ? 'Creating...' : 'Create Profile'}
            </button>
            <button
              type="button"
              className="modal-button modal-button-cancel"
              onClick={onClose}
              disabled={creating}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const Profiles = () => {
  const { profiles, setProfiles, setProfile, loading, profile: currentProfile, isProfileLoaded, saveProfilesManually } = useProfile();
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newProfileName, setNewProfileName] = useState('');
  const [newProfileAvatar, setNewProfileAvatar] = useState(defaultAvatars[0]);
  const [creating, setCreating] = useState(false);
  // Removed delete modal state - now using edit instead
  const navigate = useNavigate();
  
  // Debug logging and state synchronization
  useEffect(() => {
    console.log('🔄 Profiles page - State change detected:', {
      loading,
      isProfileLoaded,
      profilesCount: profiles?.length || 0,
      activeProfilesCount: profiles?.filter(p => p.active)?.length || 0,
      currentProfile: currentProfile?.name || 'none'
    });
  }, [loading, isProfileLoaded, profiles, currentProfile]);

  const handleSelectProfile = async (profile) => {
    // Clear scroll positions from sessionStorage
    sessionStorage.removeItem('dashboard_scroll_position');
    sessionStorage.removeItem('tvshows_scroll_position');
    sessionStorage.removeItem('search_scroll_position');
    sessionStorage.removeItem('watchlist_scroll_position');
    
    // Clear any other page-specific scroll positions
    Object.keys(sessionStorage).forEach(key => {
      if (key.includes('scroll_position')) {
        sessionStorage.removeItem(key);
      }
    });
    
    // Use race-condition-safe manual save for current profile only
    const saveSuccess = await saveProfilesManually(profiles, profile);
    
    if (saveSuccess) {
      console.log('✅ Profile selection saved to standalone storage');
      // Set the new profile AFTER successful save
      setProfile(profile);
    } else {
      console.error('❌ Failed to save profile selection to standalone storage');
      // Still set the profile for immediate UI feedback, but warn user
      setProfile(profile);
      alert('Profile selected but failed to save. Changes may not persist.');
    }
    
    // Navigate to dashboard after profile selection (Netflix-like flow)
    navigate('/dashboard', { replace: true });
  };

  const handleAddProfile = () => {
    setShowCreateForm(true);
  };

  const handleCreateProfile = async (e) => {
    e.preventDefault();
    setCreating(true);
    try {
      console.log('🏠 Creating profile - Current profiles:', profiles);
      console.log('🏠 Creating profile - Active count:', profiles.filter(p => p.active).length);
      
      // Find the next inactive profile slot
      const nextSlot = profiles.find(p => !p.active);
      console.log('🏠 Creating profile - Next slot:', nextSlot);
      
      if (!nextSlot) {
        console.error('❌ No inactive slots available');
        alert('Maximum number of profiles reached.');
        setCreating(false);
        return;
      }
      
      // Update the inactive slot to become an active profile
      const updatedProfiles = profiles.map(p => 
        p.id === nextSlot.id 
          ? { ...p, name: newProfileName, avatar: newProfileAvatar, active: true }
          : p
      );
      
      console.log('🏠 Creating profile - Updated profiles:', updatedProfiles);
      
      const newProfile = updatedProfiles.find(p => p.id === nextSlot.id);
      
      // Use race-condition-safe manual save
      const saveSuccess = await saveProfilesManually(updatedProfiles, newProfile);
      
      if (saveSuccess) {
        console.log('✅ Creating profile - Successfully saved to standalone storage');
        // Update React state AFTER successful save
        setProfiles(updatedProfiles);
        setProfile(newProfile);
      } else {
        console.error('❌ Creating profile - Failed to save to standalone storage');
        alert('Failed to create profile. Please try again.');
        setCreating(false);
        return;
      }
      
      setShowCreateForm(false);
      setNewProfileName('');
      setNewProfileAvatar(defaultAvatars[0]);
    } catch (error) {
      console.error('❌ Error creating profile:', error);
      alert('Failed to create profile');
    } finally {
      setCreating(false);
    }
  };



  // Show active profiles and calculate available slots
  const activeProfiles = profiles ? profiles.filter(p => p.active) : [];
  const inactiveProfiles = profiles ? profiles.filter(p => !p.active) : [];
  const maxProfiles = 6;

  if (loading || !isProfileLoaded) {
    return (
      <div className="profiles-container">
        <div className="profiles-loading">Loading profiles...</div>
      </div>
    );
  }

  return (
    <div className="profiles-container">
      <style>{`
        /* Profile Container Styles */
        .profiles-container { 
          min-height: 100vh; 
          width: 100%; 
          display: flex; 
          flex-direction: column; 
          align-items: center; 
          justify-content: center; 
          position: relative; 
          overflow: hidden; 
          background: black; 
        }
        
        .profiles-content { 
          position: relative; 
          z-index: 10; 
          background: transparent; 
          padding: 2.5rem; 
          width: 100%; 
          max-width: 48rem; 
          display: flex; 
          flex-direction: column; 
          align-items: center; 
        }
        
        .profiles-title { 
          font-size: 3rem; 
          font-weight: 700; 
          color: transparent; 
          background: linear-gradient(to right, #60a5fa, #a78bfa, #f472b6); 
          background-clip: text; 
          -webkit-background-clip: text; 
          margin-bottom: 2.5rem; 
          letter-spacing: -0.04em; 
          font-family: 'Poppins', 'Inter', 'Segoe UI', Arial, sans-serif; 
        }
        
        .profiles-loading { 
          color: #9ca3af; 
        }
        
        .profiles-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
        }
        
        .profiles-grid { 
          display: grid; 
          grid-template-columns: repeat(2, 1fr); 
          place-items: center; 
          gap: 1rem; 
          justify-content: center;
          max-width: fit-content;
          margin-left: auto;
          margin-right: auto;
        }
        

        
        @media (min-width: 640px) { 
          .profiles-grid { 
            grid-template-columns: repeat(3, 1fr); 
          } 
        }
        
        .profile-item { 
          position: relative; 
        }
        
        .profile-button { 
          display: flex; 
          flex-direction: column; 
          align-items: center; 
          outline: none; 
          width: 100%; 
          transition: transform 0.2s; 
          background: none; 
          border: none; 
          cursor: pointer; 
        }
        
        .profile-button:hover { 
          transform: scale(1.05); 
        }
        
        .profile-button.selected { 
          /* No scaling for selected profile */
        }
        
        .profile-avatar-container { 
          position: relative; 
          width: 6rem; 
          height: 6rem; 
          border-radius: 50%;
          border: none; 
          transition: all 0.3s ease; 
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
          overflow: hidden; 
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .profile-avatar-container.selected { 
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
        }
        
        .profile-avatar-letter { 
          width: 100%; 
          height: 100%; 
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 2rem;
          font-weight: 700;
          font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
        }
        
        .profile-1, .profile-2, .profile-3, .profile-4, .profile-5, .profile-6 { 
          background: rgba(31, 41, 55, 0.8);
          border: 1px solid rgba(55, 65, 81, 0.5);
        }
        

        
        .profile-name { 
          font-size: 1.125rem; 
          font-weight: 600; 
          transition: color 0.3s; 
          margin-top: 0.75rem; 
          color: #9ca3af; 
        }
        
        .profile-name.selected { 
          color: #60a5fa; 
        }
        
        .profile-current-label { 
          color: #60a5fa; 
          font-size: 0.875rem; 
          margin-top: 0.25rem; 
        }
        
        .add-profile-slot-button { 
          border-style: dashed;
          border-color: #6b7280; 
          background-color: rgba(31, 41, 55, 0.5); 
          transition: all 0.3s ease;
        }
        
        .add-profile-slot-icon { 
          font-size: 2.25rem; 
          color: #6b7280; 
          transition: color 0.3s ease; 
        }
        
        .add-profile-slot-text { 
          color: #9ca3af; 
          transition: color 0.3s ease;
        }
        
        /* Modal Styles */
        .modal-overlay { 
          position: fixed; 
          top: 0; 
          left: 0; 
          right: 0; 
          bottom: 0; 
          background-color: rgba(0, 0, 0, 0.75); 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          z-index: 50; 
          padding: 1rem; 
        }
        
        .modal-content { 
          background-color: #111827; 
          border-radius: 1rem; 
          padding: 2rem; 
          max-width: 28rem; 
          width: 100%; 
          border: 1px solid rgba(59, 130, 246, 0.4); 
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); 
        }
        
        .modal-title { 
          font-size: 1.5rem; 
          font-weight: 700; 
          color: white; 
          margin-bottom: 1.5rem; 
          text-align: center; 
        }
        
        .modal-text { 
          color: #d1d5db; 
          text-align: center; 
          margin-bottom: 1.5rem; 
        }
        
        .modal-buttons { 
          display: flex; 
          gap: 0.75rem; 
        }
        
        .modal-button { 
          flex: 1; 
          padding: 0.75rem 1rem; 
          border-radius: 0.5rem; 
          font-weight: 700; 
          transition: background-color 0.2s; 
          border: none; 
          cursor: pointer; 
        }
        
        .modal-button:disabled { 
          opacity: 0.5; 
          cursor: not-allowed; 
        }
        
        .modal-button-cancel { 
          background-color: #374151; 
          color: white; 
        }
        
        .modal-button-cancel:hover:not(:disabled) { 
          background-color: #4b5563; 
        }
        
        .modal-button-delete { 
          background-color: #dc2626; 
          color: white; 
        }
        
        .modal-button-delete:hover:not(:disabled) { 
          background-color: #b91c1c; 
        }
        
        .modal-button-primary { 
          background-color: #2563eb; 
          color: white; 
        }
        
        .modal-button-primary:hover:not(:disabled) { 
          background-color: #1d4ed8; 
        }
        
        /* Form Styles */
        .create-form { 
          display: flex; 
          flex-direction: column; 
          gap: 1.5rem; 
        }
        
        .form-group { 
          display: flex; 
          flex-direction: column; 
        }
        
        .form-label { 
          color: #d1d5db; 
          font-size: 0.875rem; 
          font-weight: 500; 
          margin-bottom: 0.5rem; 
        }
        
        .form-input { 
          width: 100%; 
          padding: 0.75rem 1rem; 
          border-radius: 0.5rem; 
          background-color: #1f2937; 
          color: white; 
          border: 1px solid #374151; 
          outline: none; 
          transition: border-color 0.2s; 
        }
        
        .form-input:focus { 
          border-color: #2563eb; 
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); 
        }
        
        .avatar-grid { 
          display: grid; 
          grid-template-columns: repeat(4, 1fr); 
          gap: 0.75rem; 
        }
        
        .avatar-option { 
          width: 4rem; 
          height: 4rem; 
          border-radius: 50%; 
          border: 4px solid #4b5563; 
          transition: all 0.3s; 
          background: none; 
          cursor: pointer; 
          padding: 0; 
        }
        
        .avatar-option:hover { 
          border-color: #6b7280; 
        }
        
        .avatar-option.selected { 
          border-color: #2563eb; 
          transform: scale(1.1); 
        }
        
        .avatar-option-image { 
          width: 100%; 
          height: 100%; 
          border-radius: 50%; 
          object-fit: cover; 
          background-color: #1f2937; 
        }
      `}</style>
      
      <div className="profiles-content">
        <h1 className="profiles-title">
          Who&apos;s watching?
        </h1>
        
        {activeProfiles.length === 0 ? (
          <div className="no-profiles-container">
            <p className="no-profiles-text">No profiles found. Add a profile to get started!</p>
            <button
              onClick={handleAddProfile}
              className="add-profile-button"
            >
              Add Profile
            </button>
          </div>
        ) : (
          <div className="profiles-wrapper">
            <div className="profiles-grid">
              {/* Show active profiles */}
              {activeProfiles.map((profile, idx) => {
                const isSelected = currentProfile && currentProfile.id === profile.id;
                return (
                  <div key={profile.id} className="profile-item">
                    <button
                      onClick={() => handleSelectProfile(profile)}
                      className={`profile-button ${isSelected ? 'selected' : ''}`}
                    >
                      <AnimatedProfile
                        isSelected={isSelected}
                        className="relative"
                      >
                        <div className={`profile-avatar-container ${isSelected ? 'selected' : ''}`}>
                          <div className={`profile-avatar-letter profile-${profile.id}`}>
                            {profile.name.charAt(0).toUpperCase()}
                          </div>
                        </div>
                      </AnimatedProfile>
                      <span className={`profile-name ${isSelected ? 'selected' : ''}`}>
                        {profile.name.replace(' - Main Account', '')}
                      </span>
                    </button>
                  </div>
                );
              })}
              
              {/* Show Add Profile button in the next grid slot if there are available slots */}
              {activeProfiles.length < maxProfiles && (
                <div className="profile-item">
                  <button
                    onClick={handleAddProfile}
                    className="profile-button"
                  >
                    <AnimatedProfile className="relative">
                      <div className="profile-avatar-container add-profile-slot-button">
                        <span className="add-profile-slot-icon">+</span>
                      </div>
                    </AnimatedProfile>
                    <span className="profile-name add-profile-slot-text">Add Profile</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Removed delete modal - now using edit instead */}

      <CreateProfileModal
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSubmit={handleCreateProfile}
        newProfileName={newProfileName}
        setNewProfileName={setNewProfileName}
        newProfileAvatar={newProfileAvatar}
        setNewProfileAvatar={setNewProfileAvatar}
        creating={creating}
      />
    </div>
  );
};

export default Profiles; 