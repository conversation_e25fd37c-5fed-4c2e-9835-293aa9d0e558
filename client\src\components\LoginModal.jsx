import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const LoginModal = ({ isOpen, onClose, onSwitchToRegister }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();

  // Load saved credentials if remember me was used
  useEffect(() => {
    if (isOpen) {
      const savedUsername = localStorage.getItem('torvie_saved_username');
      const savedRememberMe = localStorage.getItem('torvie_remember_me') === 'true';
      
      if (savedUsername && savedRememberMe) {
        setUsername(savedUsername);
        setRememberMe(true);
      } else {
        setUsername('');
        setRememberMe(false);
      }
      setError('');
    }
  }, [isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(username, password, rememberMe);
    
    if (result.success) {
      // Save username for quick login if remember me is checked
      if (rememberMe) {
        localStorage.setItem('torvie_saved_username', username);
      } else {
        localStorage.removeItem('torvie_saved_username');
      }
      
      onClose();
      // Redirect to profiles page after successful login
      window.location.href = '/profiles';
    } else {
      setError(result.error || 'Login failed');
    }
    setLoading(false);
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4 cursor-pointer"
      onClick={onClose}
      style={{ minHeight: '100vh' }}
    >
      <div
        className="bg-black/95 backdrop-blur-xl border border-purple-500/30 rounded-2xl p-8 w-full max-w-md relative mt-20 max-h-[90vh] overflow-y-auto cursor-default"
        onClick={e => e.stopPropagation()}
        style={{ boxShadow: '0 8px 32px 0 rgba(139, 92, 246, 0.3)' }}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-cyan-400 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 6L6 18M6 6l12 12"/>
          </svg>
        </button>

        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">Welcome Back</h2>
          <p className="text-gray-300">Sign in to your Torvie account</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-400 text-sm">
              {error}
            </div>
          )}

          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">
              Username
            </label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-4 py-3 bg-black/50 border border-purple-500/30 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
              placeholder="Enter your username"
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 bg-black/50 border border-purple-500/30 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="flex items-center">
            <input
              id="remember-me"
              type="checkbox"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
              className="w-4 h-4 text-cyan-500 bg-black/50 border-purple-500/30 rounded focus:ring-cyan-500 focus:ring-2"
            />
            <label htmlFor="remember-me" className="ml-2 text-sm text-gray-300">
              Remember me
            </label>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-cyan-500 to-purple-600 text-black font-bold py-3 px-6 rounded-lg hover:from-cyan-400 hover:to-purple-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-black transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
                Signing in...
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Quick Login for Saved Users */}
        {username && rememberMe && !loading && (
          <div className="mt-4 p-3 bg-cyan-500/10 border border-cyan-500/30 rounded-lg">
            <p className="text-cyan-400 text-sm text-center">
              ✓ Quick sign-in enabled for {username}
            </p>
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-gray-300">
            Don&apos;t have an account?{' '}
            <button
              onClick={onSwitchToRegister}
              className="text-cyan-400 hover:text-cyan-300 font-medium transition-colors"
            >
              Sign up
            </button>
          </p>
        </div>

        {/* Demo Accounts Info */}
        <div className="mt-6 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
          <h3 className="text-sm font-medium text-gray-300 mb-2">Demo Accounts:</h3>
          <div className="text-xs text-gray-400 space-y-1">
            <div>Username: <span className="text-blue-400">Backwood</span> | Password: <span className="text-blue-400">Backwood420$</span></div>
            <div>Username: <span className="text-blue-400">user2-6</span> | Password: <span className="text-blue-400">user123</span></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal; 