import React, { useState, useEffect, useRef, useMemo } from 'react';
import { CyclingTypewriter, funnyLoadingPhrases } from '../utils/loadingPhrases.jsx';

export const AnimatedTorvieLogo = ({ width = 600, height = 200, scale = 1 }) => {
  const canvasRef = useRef(null);
  const [fontLoaded, setFontLoaded] = useState(false);
  const [animationTime, setAnimationTime] = useState(0);
  const animationRef = useRef();

  useEffect(() => {
    // Load Poppins font
    const poppinsFont = new FontFace('Poppins', 'url(https://fonts.gstatic.com/s/poppins/v21/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2)', {
      weight: '700'
    });

    poppinsFont.load().then((loadedFont) => {
      document.fonts.add(loadedFont);
      setFontLoaded(true);
    }).catch((error) => {
      console.error('Font could not be loaded:', error);
      setFontLoaded(true);
    });
  }, []);

  useEffect(() => {
    if (fontLoaded && canvasRef.current) {
      const animate = () => {
        setAnimationTime(prev => prev + 0.02);
        animationRef.current = requestAnimationFrame(animate);
      };
      animate();
      
      return () => {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    }
  }, [fontLoaded]);

  useEffect(() => {
    if (fontLoaded && canvasRef.current) {
      renderTorvieLogo();
    }
  }, [fontLoaded, width, height, animationTime, scale]);

  const renderTorvieLogo = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    // Set actual canvas size for high DPI
    const dpr = window.devicePixelRatio || 1;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    ctx.scale(dpr, dpr);
    
    // Clear the canvas
    ctx.clearRect(0, 0, width, height);

    // Scale everything for loading screen
    const logoScale = Math.min(width / 600, height / 600) * scale;
    ctx.save();
    ctx.translate(width / 2, height / 2);
    ctx.scale(logoScale, logoScale);
    ctx.translate(-300, -300);

    // Define constants for the logo's geometry and style
    const centerX = 300;
    const centerY = 300;
    const strokeWidth = 14;
    const verticalSeparation = 5;

    // Font and Text Style
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '700 80px Poppins';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const text = 'TORVIE';
    const letterSpacing = 2;
    const rScaleFactor = 1.3;

    // Dynamic radius calculation
    let totalTextWidth = 0;
    for (let i = 0; i < text.length; i++) {
      let width = ctx.measureText(text[i]).width;
      if (text[i] === 'R') {
        width *= rScaleFactor;
      }
      totalTextWidth += width;
    }
    totalTextWidth += (text.length - 1) * letterSpacing;

    const radius = ((totalTextWidth / 2 + 15) * 0.5) * 1.1;

    // Animation timing for gradient movement
    const time = animationTime * 0.5;
    
    // Purple shades palette
    const purpleShades = [
        [138, 43, 226],   // Blue violet
        [75, 0, 130],     // Indigo  
        [147, 0, 211],    // Dark violet
        [123, 104, 238],  // Medium slate blue
        [102, 51, 153]    // Rebecca purple
    ];
    
    // Cyan shades palette
    const cyanShades = [
        [0, 191, 255],    // Deep sky blue
        [0, 206, 209],    // Dark turquoise
        [64, 224, 208],   // Turquoise
        [0, 255, 255],    // Cyan
        [32, 178, 170]    // Light sea green
    ];
    
    // Smooth interpolation function
    const interpolateColor = (colorArray, time) => {
        const scaledTime = time % colorArray.length;
        const index1 = Math.floor(scaledTime);
        const index2 = (index1 + 1) % colorArray.length;
        const factor = scaledTime - index1;
        
        const color1 = colorArray[index1];
        const color2 = colorArray[index2];
        
        return [
            Math.round(color1[0] + (color2[0] - color1[0]) * factor),
            Math.round(color1[1] + (color2[1] - color1[1]) * factor),
            Math.round(color1[2] + (color2[2] - color1[2]) * factor)
        ];
    };

    // Draw high-tech flowing gradient rings
    ctx.lineWidth = strokeWidth;
    
    // Moving gradient parameters
    const rotationSpeed = time * 1.5; // Clockwise rotation speed
    
    // Get animated colors for the flowing gradients
    const allColors = [...purpleShades, ...cyanShades];
    const color1 = interpolateColor(allColors, time);
    const color2 = interpolateColor(allColors, time + 2);
    const color3 = interpolateColor(allColors, time + 4);
    
    // Create flowing gradients that rotate around the ring
    const topGradient = ctx.createConicGradient(rotationSpeed, centerX, centerY - verticalSeparation);
    topGradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    topGradient.addColorStop(0.15, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    topGradient.addColorStop(0.25, '#00BFFF'); // Concentrated cyan highlight
    topGradient.addColorStop(0.35, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    topGradient.addColorStop(0.65, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    topGradient.addColorStop(0.75, '#00FFFF'); // Another cyan highlight
    topGradient.addColorStop(0.85, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    topGradient.addColorStop(1, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);

    const bottomGradient = ctx.createConicGradient(rotationSpeed + Math.PI, centerX, centerY + verticalSeparation);
    bottomGradient.addColorStop(0, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    bottomGradient.addColorStop(0.1, '#64E0D0'); // Turquoise highlight
    bottomGradient.addColorStop(0.2, `rgb(${color3[0]}, ${color3[1]}, ${color3[2]})`);
    bottomGradient.addColorStop(0.4, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
    bottomGradient.addColorStop(0.6, '#00BFFF'); // Concentrated cyan highlight
    bottomGradient.addColorStop(0.7, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    bottomGradient.addColorStop(1, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
    
    // Top ring - smooth flowing gradient
    ctx.beginPath();
    ctx.strokeStyle = topGradient;
    ctx.arc(centerX, centerY - verticalSeparation, radius, Math.PI, 2 * Math.PI);
    ctx.stroke();
    
    // Bottom ring - smooth flowing gradient
    ctx.beginPath();
    ctx.strokeStyle = bottomGradient;
    ctx.arc(centerX, centerY + verticalSeparation, radius, 0, Math.PI);
    ctx.stroke();

    // Draw black background behind text
    const rectWidth = totalTextWidth + 20;
    const rectHeight = 100;
    ctx.fillStyle = '#000';
    ctx.fillRect(centerX - rectWidth / 2, centerY - rectHeight / 2, rectWidth, rectHeight);

    // Draw static white text
    const textYOffset = 9;
    ctx.fillStyle = '#FFFFFF';
    let currentX = centerX - totalTextWidth / 2;

    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      let charWidth = ctx.measureText(char).width;

      if (char === 'R') {
        charWidth *= rScaleFactor;
      }

      const charCenterX = currentX + charWidth / 2;

      if (char === 'R') {
        ctx.save();
        ctx.translate(charCenterX, centerY + textYOffset);
        ctx.scale(rScaleFactor, 1);
        ctx.fillText('R', 0, 0);
        ctx.globalCompositeOperation = 'destination-out';
        ctx.fillRect(-23, -25, 17, 14);
        ctx.restore();
      } else {
        ctx.fillText(char, charCenterX, centerY + textYOffset);
      }
      currentX += charWidth + letterSpacing;
    }

    ctx.restore();
  };

  return (
    <canvas
      ref={canvasRef}
      style={{ width: `${width}px`, height: `${height}px` }}
      className="drop-shadow-2xl"
    />
  );
};

// Animated Progress Bar Component
const AnimatedProgressBar = ({ progress, width = 400, height = 8 }) => {
  const canvasRef = useRef(null);
  const [animationTime, setAnimationTime] = useState(0);
  const animationRef = useRef();

  useEffect(() => {
    const animate = () => {
      setAnimationTime(prev => prev + 0.02);
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (canvasRef.current) {
      renderProgressBar();
    }
  }, [progress, animationTime, width, height]);

  const renderProgressBar = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    // Set actual canvas size for high DPI
    const dpr = window.devicePixelRatio || 1;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    ctx.scale(dpr, dpr);
    
    // Clear the canvas
    ctx.clearRect(0, 0, width, height);

    // Animation timing matching the logo
    const time = animationTime * 0.5;
    
    // Same color palettes as logo
    const purpleShades = [
        [138, 43, 226],   // Blue violet
        [75, 0, 130],     // Indigo  
        [147, 0, 211],    // Dark violet
        [123, 104, 238],  // Medium slate blue
        [102, 51, 153]    // Rebecca purple
    ];
    
    const cyanShades = [
        [0, 191, 255],    // Deep sky blue
        [0, 206, 209],    // Dark turquoise
        [64, 224, 208],   // Turquoise
        [0, 255, 255],    // Cyan
        [32, 178, 170]    // Light sea green
    ];
    
    // Same interpolation function as logo
    const interpolateColor = (colorArray, time) => {
        const scaledTime = time % colorArray.length;
        const index1 = Math.floor(scaledTime);
        const index2 = (index1 + 1) % colorArray.length;
        const factor = scaledTime - index1;
        
        const color1 = colorArray[index1];
        const color2 = colorArray[index2];
        
        return [
            Math.round(color1[0] + (color2[0] - color1[0]) * factor),
            Math.round(color1[1] + (color2[1] - color1[1]) * factor),
            Math.round(color1[2] + (color2[2] - color1[2]) * factor)
        ];
    };

    // Get animated colors
    const allColors = [...purpleShades, ...cyanShades];
    const color1 = interpolateColor(allColors, time);
    const color2 = interpolateColor(allColors, time + 2);
    const color3 = interpolateColor(allColors, time + 4);

    // Draw background track
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.fillRect(0, 0, width, height);

    // Create flowing gradient for progress fill
    const progressWidth = width * (progress / 100);
    if (progressWidth > 0) {
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
      gradient.addColorStop(0.25, '#00BFFF');
      gradient.addColorStop(0.5, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
      gradient.addColorStop(0.75, '#00FFFF');
      gradient.addColorStop(1, `rgb(${color3[0]}, ${color3[1]}, ${color3[2]})`);
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, progressWidth, height);
      
      // Add glowing effect
      ctx.shadowColor = '#00BFFF';
      ctx.shadowBlur = 10;
      ctx.fillRect(0, 0, progressWidth, height);
      ctx.shadowBlur = 0;
    }
  };

  return (
    <canvas
      ref={canvasRef}
      style={{ width: `${width}px`, height: `${height}px` }}
      className="rounded-full"
    />
  );
};

const LoadingScreen = ({ onComplete, progress = 0, loadingMessage = "Initializing Torvie..." }) => {
  // eslint-disable-next-line no-unused-vars
  const [internalLoadingMessage, setInternalLoadingMessage] = useState('');
  
  // Debug logging
  console.log('LoadingScreen: Progress:', progress, 'onComplete:', !!onComplete);
  
  useEffect(() => {
    // Complete when progress reaches 100%
    if (progress >= 100 && onComplete) {
      console.log('LoadingScreen: Progress reached 100%, calling onComplete in 500ms');
      // Small delay to show 100% briefly
      const timer = setTimeout(() => {
        console.log('LoadingScreen: Calling onComplete callback');
        onComplete();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [progress, onComplete]);

  // Memoize the typewriter so it doesn't restart on every progress update
  const memoizedTypewriter = useMemo(() => (
    <CyclingTypewriter
      phrases={funnyLoadingPhrases}
      speed={40}
      pauseBetween={4000}
      className="loading-glow"
      showCursor={true}
    />
  ), []); // Empty dependency array means it only creates once

  return (
    <div className="fixed inset-0 z-50 bg-black flex flex-col items-center justify-center overflow-hidden">
      {/* Main logo - taking up most of the space */}
      <div className="flex-1 flex items-center justify-center min-h-0">
        <AnimatedTorvieLogo width={1620} height={810} scale={1} />
      </div>
      
      {/* Progress bar and messages - closer to logo */}
      <div className="flex flex-col items-center space-y-4 pb-16 -mt-20">
        <AnimatedProgressBar progress={progress} width={400} height={8} />
        
        {/* Funny Typewriter Loading Messages - bolder text */}
        <div className="text-cyan-300 text-lg font-bold tracking-wide opacity-90 min-h-[60px] text-center max-w-lg px-4">
          {memoizedTypewriter}
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen; 