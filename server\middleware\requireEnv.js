export function assertEnv(varNames = []) {
  if (!Array.isArray(varNames)) {
    throw new TypeError('assertEnv expects an array of variable names');
  }
  const missing = varNames.filter((name) => !(process.env[name] && process.env[name].trim() !== ''));
  if (missing.length) {
    // Fail fast – mis-configuration should never reach runtime handlers.
    console.error(`❌  Missing required environment variables: ${missing.join(', ')}`);
    // Use exit code 1 so container/orchestrator restarts the service.
    process.exit(1);
  }
}

// Optional middleware – can be mounted on routes that need specific env vars
export function requireEnv(varNames = []) {
  assertEnv(varNames); // Will exit if missing
  return (req, res, next) => next();
} 