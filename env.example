# Torvie Environment Configuration
# Copy this file to .env and update the values

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# TMDB API Configuration
TMDB_API_KEY=your-tmdb-api-key-here

# CORS Configuration
CORS_ORIGIN=http://localhost

# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (for future PostgreSQL migration)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=torvie
# DB_USER=torvie_user
# DB_PASSWORD=your-db-password

# Redis Configuration (for future caching)
# REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=info

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100 