// IMPORTANT: This page uses TMDB data for all display elements (posters, titles, info)
// Torrent data is ONLY used for the play button functionality
// - TMDB: Posters, titles, cast, info, trailers
// - Torrents: Only for the left play button to stream content

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import MediaCard from '../components/MediaCard';
import EnhancedVideoPlayer from '../components/EnhancedVideoPlayer';
import TrailerPlayer from '../components/TrailerPlayer';
import { watchlistStorage } from '../utils/watchlistStorage';
import { apiFetch } from '../utils/api';
import OptimizedMediaCard from '../components/OptimizedMediaCard';
import HorizontalRow from '../components/HorizontalRow';
import LoadingSpinner from '../components/LoadingSpinner';

const FeaturedHero = ({ anime, onWatchNow, isPlayingTrailer, trailer, _onStopTrailer }) => {
    if (!anime) return <div className="w-full h-[60vh] bg-black animate-pulse rounded-lg"></div>;
    const backdropUrl = `https://image.tmdb.org/t/p/original${anime.backdrop_path}`;
    
    return (
        <div className="w-full flex justify-center py-8">
            <div className="relative max-w-4xl aspect-video w-full rounded-lg overflow-hidden shadow-2xl bg-black">
                {isPlayingTrailer ? (
                    <div className="absolute inset-0 w-full h-full">
                        <iframe
                            src={`https://www.youtube.com/embed/${trailer.key}?autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&iv_load_policy=3&fs=0&vq=hd1080`}
                            title={`${anime.name || anime.title} Trailer`}
                            className="absolute inset-0 w-full h-full"
                            frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                        ></iframe>
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent pointer-events-none"></div>
                    </div>
                ) : (
                    <div className="absolute inset-0 w-full h-full">
                        <div
                            className="w-full h-full bg-cover bg-center"
                            style={{ backgroundImage: `url(${backdropUrl})` }}
                        ></div>
                        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
                    </div>
                )}
                <div className="relative z-10 p-8 max-w-2xl">
                    <h1 className="text-5xl font-black tracking-tighter">{anime.name || anime.title}</h1>
                    <p className="mt-2 text-gray-300">{anime.overview}</p>
                    <div className="flex gap-4 mt-4">
                        <button 
                            className="w-16 h-16 bg-white/10 backdrop-blur-sm text-white rounded-full flex items-center justify-center transform hover:scale-110 hover:bg-white/20 transition-all shadow-lg"
                            onClick={() => onWatchNow && onWatchNow(anime)}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="ml-1">
                                <path d="M8 5v14l11-7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

const Anime = () => {
  const { profile: currentProfile, isProfileLoaded } = useProfile();
  const { user } = useAuth();
  // Remove trendingAnime and popularAnime state, use a single animeList state
  const [animeList, setAnimeList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadingTrending, setLoadingTrending] = useState(true);
  const [trendingError, setTrendingError] = useState(null);
  const [heroTrailer, setHeroTrailer] = useState({ isPlaying: false, anime: null, trailer: null });
  const [hasPlayedHeroTrailer, setHasPlayedHeroTrailer] = useState(false);
  const [prefetchedPages, setPrefetchedPages] = useState(new Set());
  const [backgroundLoading, setBackgroundLoading] = useState(false);
  const [watchlist, setWatchlist] = useState([]);
  const [videoPlayer, setVideoPlayer] = useState({ isOpen: false, magnetLink: null, title: null, contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  const [trailerModal, setTrailerModal] = useState({ isOpen: false, anime: null, trailer: null });
  const lastAnimeRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Fetch trending anime for hero section - check cache first
  useEffect(() => {
    const fetchTrendingAnime = async () => {
      // Check for cached data first
      const cachedTrendingAnime = localStorage.getItem('torvie_cached_trending_anime');
      if (cachedTrendingAnime) {
        try {
          const cachedData = JSON.parse(cachedTrendingAnime);
          if (cachedData && cachedData.length > 0) {
            console.log(`✅ Anime: Using cached trending anime (${cachedData.length} items)`);
            // setTrendingAnime(cachedData); // This state is removed
            setLoadingTrending(false);
            return; // Skip API call if we have cached data
          }
        } catch (error) {
          console.warn('Failed to parse cached trending anime, fetching fresh data:', error);
        }
      }

      setLoadingTrending(true);
      setTrendingError(null);
      
      try {
        console.log('🎌 Anime: Fetching trending anime from TMDB API...');
        const response = await apiFetch('/api/trending/anime?page=1');
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        const animeArray = data.results && Array.isArray(data.results) ? data.results : data;
        
        if (Array.isArray(animeArray) && animeArray.length > 0) {
          // setTrendingAnime(animeArray); // This state is removed
          // Cache the fresh data
          localStorage.setItem('torvie_cached_trending_anime', JSON.stringify(animeArray));
          console.log(`🎌 Anime: Successfully loaded ${animeArray.length} trending anime`);
        } else {
          console.warn('🎌 Anime: No trending anime found in API response');
          // setTrendingAnime([]); // This state is removed
        }
      } catch (error) {
        console.error('🎌 Anime: Error fetching trending anime:', error);
        setTrendingError(error.message);
        // setTrendingAnime([]); // This state is removed
      } finally {
        setLoadingTrending(false);
      }
    };
    
    fetchTrendingAnime();
  }, []);

  // Fetch initial anime (most popular) and handle infinite scroll
  useEffect(() => {
    const fetchAnime = async (page = 1) => {
      if (isLoadingMore) return;
      setIsLoadingMore(true);
      try {
        const res = await apiFetch(`/api/discover-anime?sort_by=popularity.desc&page=${page}`);
        const data = await res.json();
        const newAnime = data.results || [];
        setAnimeList(prev => page === 1 ? newAnime : [...prev, ...newAnime]);
        setHasMore(newAnime.length > 0);
        setCurrentPage(page);
      } catch {
        setHasMore(false);
      }
      setIsLoadingMore(false);
    };
    fetchAnime(1);
  }, []);

  // Infinite scroll: load more when near bottom
  useEffect(() => {
    const handleScroll = () => {
      if (!hasMore || isLoadingMore) return;
      const scrollY = window.scrollY || window.pageYOffset;
      const windowHeight = window.innerHeight;
      const docHeight = document.documentElement.scrollHeight;
      if (docHeight - (scrollY + windowHeight) < 400) {
        fetchMore();
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, isLoadingMore, currentPage]);

  const fetchMore = () => {
    if (!hasMore || isLoadingMore) return;
    const nextPage = currentPage + 1;
    // Use the same fetch logic as above
    apiFetch(`/api/discover-anime?sort_by=popularity.desc&page=${nextPage}`)
      .then(res => res.json())
      .then(data => {
        const newAnime = data.results || [];
        setAnimeList(prev => [...prev, ...newAnime]);
        setHasMore(newAnime.length > 0);
        setCurrentPage(nextPage);
      })
      .catch(() => setHasMore(false));
  };

  // Background prefetching function
  const backgroundPrefetch = useCallback(async (pagesToFetch) => {
    if (backgroundLoading) return;
    
    setBackgroundLoading(true);
    const newPages = pagesToFetch.filter(page => !prefetchedPages.has(page));
    
    if (newPages.length === 0) {
      setBackgroundLoading(false);
      return;
    }
    
    try {
      console.log(`🎌 Background prefetching pages: ${newPages.join(', ')}`);
      const pagePromises = newPages.map(page => 
        apiFetch(`/api/discover-anime?page=${page}`).then(r => r.json()).catch(() => null)
      );
      
      const pageResults = await Promise.all(pagePromises);
      const allNewAnime = [];
      const successfulPages = new Set();
      
      pageResults.forEach((data, index) => {
        if (data && data.results) {
          const pageAnime = data.results || [];
          if (pageAnime.length > 0) {
            allNewAnime.push(...pageAnime);
            successfulPages.add(newPages[index]);
          }
        }
      });
      
      if (allNewAnime.length > 0) {
        setAnimeList(prev => {
          const existingIds = new Set(prev.map(a => a.id));
          const uniqueNewAnime = allNewAnime.filter(anime => 
            anime.id && !existingIds.has(anime.id)
          );
          
          console.log(`🎌 Background prefetched ${uniqueNewAnime.length} new anime`);
          return uniqueNewAnime.length > 0 ? [...prev, ...uniqueNewAnime] : prev;
        });
        
        setPrefetchedPages(prev => new Set([...prev, ...successfulPages]));
        setCurrentPage(prev => Math.max(prev, Math.max(...successfulPages)));
      }
      
    } catch (error) {
      console.error('🎌 Background prefetch error:', error);
    } finally {
      setBackgroundLoading(false);
    }
  }, [backgroundLoading, prefetchedPages]);

  // Load more anime for infinite scroll (now uses prefetched data)
  const loadMoreAnime = useCallback(async () => {
    if (isLoadingMore || !hasMore) {
      console.log(`🎌 Skipping loadMoreAnime - loading: ${isLoadingMore}, hasMore: ${hasMore}`);
      return;
    }
    
    // Check if we have prefetched content already available
    const nextPage = currentPage + 1;
    if (prefetchedPages.has(nextPage)) {
      console.log(`🎌 Using prefetched data for page ${nextPage}`);
      // Data is already loaded, just trigger background prefetch for future pages
      backgroundPrefetch([nextPage + 3, nextPage + 4, nextPage + 5]);
      return;
    }
    
    setIsLoadingMore(true);
    
    try {
      console.log(`🎌 Loading more anime - page ${nextPage} (fallback)`);
      const response = await apiFetch(`/api/discover-anime?page=${nextPage}`);
      const data = await response.json();
      const newAnime = data.results || [];
      
      if (newAnime.length > 0) {
        setAnimeList(prev => {
          const existingIds = new Set(prev.map(a => a.id));
          const uniqueNewAnime = newAnime.filter(anime => {
            if (!anime.id) return false;
            return !existingIds.has(anime.id);
          });
          
          console.log(`🎌 Adding ${uniqueNewAnime.length} new unique anime (fallback load)`);
          return uniqueNewAnime.length > 0 ? [...prev, ...uniqueNewAnime] : prev;
        });
        
        setCurrentPage(nextPage);
        setPrefetchedPages(prev => new Set([...prev, nextPage]));
        setHasMore(newAnime.length > 0);
        
        // Start prefetching next batch
        backgroundPrefetch([nextPage + 1, nextPage + 2, nextPage + 3]);
      } else {
        console.log('🎌 No more anime found, ending infinite scroll');
        setHasMore(false);
      }
      
    } catch (error) {
      console.error('🎌 Error loading more anime:', error);
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMore, currentPage, prefetchedPages, backgroundPrefetch]);

  // Proactive infinite scroll with background prefetching
  useEffect(() => {
    let scrollTimer = null;
    let prefetchTimer = null;
    
    const handleScroll = () => {
      // Clear existing timers
      if (scrollTimer) clearTimeout(scrollTimer);
      if (prefetchTimer) clearTimeout(prefetchTimer);
      
      // Throttle scroll events
      scrollTimer = setTimeout(() => {
        const scrollPosition = window.innerHeight + document.documentElement.scrollTop;
        const documentHeight = document.documentElement.offsetHeight;
        const scrollPercent = scrollPosition / documentHeight;
        
        // Early trigger when user is 2000px from bottom (much more proactive)
        const scrollThreshold = 2000;
        const nearBottom = scrollPosition >= documentHeight - scrollThreshold;
        
        // Background prefetch when user reaches 50% of current content
        const prefetchPoint = scrollPercent > 0.5;
        
        if (nearBottom && !isLoadingMore && hasMore && !loadingTrending) {
          console.log('🎌 Proactive scroll triggered - loading more anime');
          loadMoreAnime();
        } else if (prefetchPoint && !backgroundLoading && hasMore) {
          // Start background prefetching early
          prefetchTimer = setTimeout(() => {
            const nextPages = [currentPage + 1, currentPage + 2, currentPage + 3];
            backgroundPrefetch(nextPages);
          }, 200);
        }
      }, 50); // Faster response time (50ms instead of 100ms)
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimer) clearTimeout(scrollTimer);
      if (prefetchTimer) clearTimeout(prefetchTimer);
    };
  }, [loadMoreAnime, backgroundPrefetch, isLoadingMore, hasMore, loadingTrending, backgroundLoading, currentPage]);

  // Auto-play trailer for featured anime
  useEffect(() => {
    if (animeList.length > 0 && !loadingTrending && !heroTrailer.isPlaying && !hasPlayedHeroTrailer) {
      // Check if user is returning to the page (has saved scroll position)
      const savedScrollPosition = sessionStorage.getItem('anime_scroll_position');
      const isReturningUser = savedScrollPosition && parseInt(savedScrollPosition) > 0;
      
      // Only auto-play if this is a fresh page load (not returning from details)
      if (!isReturningUser) {
        const featuredAnime = animeList[0];
        setTimeout(() => {
          handleHeroTrailer(featuredAnime);
          setHasPlayedHeroTrailer(true);
        }, 1000);
      } else {
        // Mark as played so it doesn't auto-play when returning
        setHasPlayedHeroTrailer(true);
      }
    }
  }, [animeList, loadingTrending, hasPlayedHeroTrailer]);

  // Handle scroll to stop trailer
  useEffect(() => {
    const handleScroll = () => {
      if (heroTrailer.isPlaying && window.scrollY > 100) {
        stopHeroTrailer();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [heroTrailer.isPlaying]);

  const handleHeroTrailer = async (anime) => {
    try {
      console.log('Fetching trailer for:', anime.name || anime.title);
      // Fetch trailer data for the anime (use TV endpoint since anime are usually TV series)
      const response = await apiFetch(`/api/tv/${anime.id}/videos`);
      const data = await response.json();
      
      // Find the best trailer - prioritize official cinematic trailers
      const trailers = data.results?.filter(video => 
        video.site === 'YouTube' && 
        (video.type === 'Trailer' || video.type === 'Teaser') &&
        // Filter out portrait/vertical videos (like YouTube Shorts)
        (!video.name?.toLowerCase().includes('short') && 
         !video.name?.toLowerCase().includes('vertical') &&
         !video.name?.toLowerCase().includes('portrait'))
      ) || [];
      
      console.log('Found trailers:', trailers.length);
      
      // Sort by priority: Official Trailer first, then Teaser, then others
      const sortedTrailers = trailers.sort((a, b) => {
        const aPriority = a.type === 'Trailer' ? 1 : 2;
        const bPriority = b.type === 'Trailer' ? 1 : 2;
        
        // If same type, prefer "Official" in the name
        if (aPriority === bPriority) {
          const aIsOfficial = a.name?.toLowerCase().includes('official');
          const bIsOfficial = b.name?.toLowerCase().includes('official');
          if (aIsOfficial && !bIsOfficial) return -1;
          if (!aIsOfficial && bIsOfficial) return 1;
        }
        
        return aPriority - bPriority;
      });
      
      const trailer = sortedTrailers[0];
      
      if (trailer) {
        console.log('Playing trailer:', trailer.name);
        setHeroTrailer({ isPlaying: true, anime, trailer });
      } else {
        console.log('No trailer available for:', anime.name || anime.title);
      }
    } catch (error) {
      console.error('Error fetching trailer:', error);
    }
  };

  const stopHeroTrailer = () => {
    setHeroTrailer({ isPlaying: false, anime: null, trailer: null });
  };

  const handlePlayAnime = async (anime) => {
    if (!anime) {
      return;
    }

    try {
      console.log(`🎬 Searching torrents for anime: ${anime.name || anime.title}`);
      const response = await apiFetch(`/api/search?query=${encodeURIComponent(anime.name || anime.title)}&type=anime`);
      const data = await response.json();
      
      if (data.results && data.results.length > 0) {
        console.log(`🎬 Found ${data.results.length} torrents for ${anime.name || anime.title}`);
        
        // Try torrents in order of seeders (highest first)
        const sortedTorrents = data.results.sort((a, b) => (b.seeders || 0) - (a.seeders || 0));
        
        for (let i = 0; i < Math.min(3, sortedTorrents.length); i++) {
          const torrent = sortedTorrents[i];
          console.log(`🎬 Trying torrent ${i + 1}: ${torrent.title} (${torrent.seeders} seeders)`);
          
          if (!torrent.magnetLink) {
            console.warn(`🎬 Skipping torrent ${i + 1}: No magnet link`);
            continue;
          }
          
          // Try this torrent
          setVideoPlayer({ 
            isOpen: true, 
            magnetLink: torrent.magnetLink, 
            title: anime.name || anime.title,
            contentId: `anime_${anime.id}`,
            contentType: 'anime',
            posterUrl: anime.poster_path ? `https://image.tmdb.org/t/p/w500${anime.poster_path}` : null
          });
          return; // Exit after trying the first valid torrent
        }
        
        console.error(`🎬 No valid torrents found for ${anime.name || anime.title}`);
      } else {
        console.log(`🎬 No torrents found for: ${anime.name || anime.title}`);
      }
    } catch (error) {
      console.error('🎬 Error searching for torrents:', error);
    }
  };

  const handleTrailer = async (anime) => {
    if (!anime) {
      console.warn('🎌 No anime provided to handleTrailer');
      return;
    }

    try {
      console.log('🎌 Fetching trailer for anime:', anime.name || anime.title);
      const response = await apiFetch(`/api/tv/${anime.id}/videos`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch videos: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('🎌 Video data received:', data);
      
      // More flexible trailer filtering
      const trailers = data.results?.filter(video => 
        video.site === 'YouTube' && 
        (video.type === 'Trailer' || video.type === 'Teaser') &&
        // Filter out portrait/vertical videos
        (!video.name?.toLowerCase().includes('short') && 
         !video.name?.toLowerCase().includes('vertical'))
      ) || [];
      
      console.log(`🎌 Found ${trailers.length} potential trailers:`, trailers.map(t => t.name));
      
      if (trailers.length > 0) {
        const trailer = trailers[0];
        console.log('🎌 Opening trailer modal with:', trailer.name);
        setTrailerModal({ isOpen: true, anime, trailer });
      } else {
        console.log('🎌 No trailers found for:', anime.name || anime.title);
        // Could show a notification here
        alert(`No trailer available for ${anime.name || anime.title}`);
      }
    } catch (error) {
      console.error('🎌 Error fetching trailer:', error);
      alert(`Failed to load trailer: ${error.message}`);
    }
  };

  const handleAnimeClick = (anime) => {
    // Save current scroll position before navigating
    sessionStorage.setItem('anime_scroll_position', window.scrollY.toString());
    navigate(`/show/${anime.id}`);
  };

  // Update watchlist when profile changes
  useEffect(() => {
    if (!isProfileLoaded) {
      console.log('🎌 Anime: Waiting for profile to load...');
      return;
    }
    
    const loadWatchlist = async () => {
      console.log('🎌 Anime: Loading watchlist data from standalone app storage');
      
      if (user && currentProfile && currentProfile.id) {
        const stored = await watchlistStorage.getWatchlist(user.id, currentProfile.id);
        console.log(`✅ Anime: Loaded ${stored.length} watchlist items from local app storage`);
        setWatchlist(stored);
      } else {
        console.log(`👤 Anime: No user or profile, clearing watchlist`);
        setWatchlist([]);
      }
    };
    
    loadWatchlist();
  }, [user, currentProfile, isProfileLoaded]);

  const isInWatchlist = (id) => watchlist.some(item => String(item.id) === String(id));

  const toggleWatchlist = async (anime) => {
    if (!user || !currentProfile || !currentProfile.id) {
      console.log('❌ Anime: No user or current profile for toggleWatchlist');
      return;
    }
    
    console.log(`🎌 Anime: Toggling watchlist for anime "${anime.name || anime.title}"`);
    
    const exists = watchlist.some(item => String(item.id) === String(anime.id));
    let success;
    
    if (exists) {
      success = await watchlistStorage.removeFromWatchlist(user.id, currentProfile.id, anime.id, anime.media_type || 'tv');
      if (success) {
        setWatchlist(prev => prev.filter(item => String(item.id) !== String(anime.id)));
        console.log(`➖ Anime: Removed "${anime.name || anime.title}" from watchlist`);
      }
    } else {
      success = await watchlistStorage.addToWatchlist(user.id, currentProfile.id, { ...anime, media_type: anime.media_type || 'tv' });
      if (success) {
        setWatchlist(prev => [...prev, anime]);
        console.log(`➕ Anime: Added "${anime.name || anime.title}" to watchlist`);
      }
    }
    
    if (!success) {
      console.error(`❌ Anime: Failed to toggle watchlist for "${anime.name || anime.title}"`);
    }
  };

  const closeVideoPlayer = () => {
    setVideoPlayer({ isOpen: false, magnetLink: null, title: null, contentId: null, contentType: null, posterUrl: null, episodeInfo: null });
  };

  const closeTrailerModal = () => {
    setTrailerModal({ isOpen: false, anime: null, trailer: null });
  };

  return (
    <div className="min-h-screen px-6 py-8">
      <div className="max-w-7xl mx-auto">
        {/* Featured Hero */}
        {loadingTrending ? (
          <div className="w-full flex justify-center py-8 mb-8">
            <div className="relative max-w-4xl aspect-video w-full rounded-lg overflow-hidden shadow-2xl bg-black animate-pulse">
              <div className="absolute bottom-8 left-8 space-y-4">
                <div className="h-12 bg-gray-700 rounded w-80 animate-pulse"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-700 rounded w-96 animate-pulse"></div>
                  <div className="h-4 bg-gray-700 rounded w-80 animate-pulse"></div>
                  <div className="h-4 bg-gray-700 rounded w-64 animate-pulse"></div>
                </div>
                <div className="w-16 h-16 bg-gray-700 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        ) : trendingError ? (
          <div className="w-full flex justify-center py-8 mb-8">
            <div className="relative max-w-4xl aspect-video w-full rounded-lg overflow-hidden shadow-2xl bg-red-900/20 border border-red-500/30">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-red-400 text-xl mb-2">Failed to load trending anime</div>
                  <div className="text-gray-400 text-sm">{trendingError}</div>
                </div>
              </div>
            </div>
          </div>
        ) : animeList.length > 0 ? (
          <div className="mb-8">
            <FeaturedHero 
              anime={animeList[0]}
              onWatchNow={handlePlayAnime}
              isPlayingTrailer={heroTrailer.isPlaying}
              trailer={heroTrailer.trailer}
              _onStopTrailer={stopHeroTrailer}
            />
          </div>
        ) : (
          <div className="w-full flex justify-center py-8 mb-8">
            <div className="relative max-w-4xl aspect-video w-full rounded-lg overflow-hidden shadow-2xl bg-black">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-gray-400 text-xl mb-2">No trending anime available</div>
                  <div className="text-gray-500 text-sm">Check back later for updates</div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Trending Anime Grid */}
        {!loadingTrending && animeList.length > 0 && (
          <div className="mb-10">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-5 gap-x-32 gap-y-8 justify-items-center px-8 py-8">
              {animeList.map((anime, idx) => (
                <OptimizedMediaCard
                  key={anime.id}
                  item={anime}
                  isInWatchlist={isInWatchlist(anime.id)}
                  toggleWatchlist={() => toggleWatchlist(anime)}
                  onClick={() => handleAnimeClick(anime)}
                  onPlay={() => handlePlayAnime(anime)}
                  onTrailer={() => handleTrailer(anime)}
                  className="media-card"
                />
              ))}
            </div>
            {isLoadingMore && (
              <div className="flex justify-center py-8">
                <LoadingSpinner text="Loading more anime..." />
              </div>
            )}
          </div>
        )}

        {/* Video Player */}
        {videoPlayer.isOpen && (
          <EnhancedVideoPlayer
            magnetLink={videoPlayer.magnetLink}
            title={videoPlayer.title}
            onClose={closeVideoPlayer}
            contentId={videoPlayer.contentId}
            contentType={videoPlayer.contentType}
            posterUrl={videoPlayer.posterUrl}
            episodeInfo={videoPlayer.episodeInfo}
          />
        )}

        {/* Trailer Modal */}
        {trailerModal.isOpen && trailerModal.anime && trailerModal.trailer && (
          <div 
            className="fixed inset-0 z-40 cursor-pointer"
            onClick={closeTrailerModal}
          >
            {/* Glass overlay that covers ENTIRE viewport */}
            <div 
              className="fixed backdrop-blur-xl bg-black/70" 
              style={{ 
                top: 0,
                left: 0, 
                right: 0, 
                bottom: 0,
                width: '100vw',
                height: '100vh'
              }}
            />
            {/* Header spacer to keep header visible and clickable */}
            <div 
              className="fixed bg-transparent z-[60]" 
              style={{ 
                top: 0,
                left: 0, 
                right: 0, 
                height: '80px',
                width: '100vw'
              }}
            />
            {/* Trailer container positioned below header */}
            <div 
              className="fixed flex items-center justify-center z-[51]" 
              style={{ 
                top: '80px',
                left: 0,
                right: 0,
                bottom: 0,
                width: '100vw',
                height: 'calc(100vh - 80px)'
              }}
            >
              <div className="w-[80vw] h-[70vh] max-w-5xl flex flex-col rounded-xl overflow-hidden shadow-2xl bg-black relative" onClick={(e) => e.stopPropagation()}>
                {/* Close button in top right corner */}
                <button
                  onClick={closeTrailerModal}
                  className="absolute top-4 right-4 z-10 p-2 text-white hover:bg-black/50 rounded-full transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                {/* Trailer Video - Full container */}
                <div className="w-full h-full bg-black">
                  <TrailerPlayer videoId={trailerModal.trailer.key} title={trailerModal.anime.name || trailerModal.anime.title} posterUrl={trailerModal.anime.poster_path ? `https://image.tmdb.org/t/p/w500${trailerModal.anime.poster_path}` : undefined} startMuted={false} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Anime; 