# Torvie Data Source Guidelines

## IMPORTANT: Data Source Separation

This document outlines the strict separation between data sources in the Torvie app to ensure proper functionality and user experience.

## Data Sources

### 1. TMDB (The Movie Database) - MOVIES & TV SHOWS
**Used for ALL movie/TV display elements:**
- Movie/Show posters and images
- Titles and names
- Cast and crew information
- Plot summaries and descriptions
- Release dates and ratings
- Trailers and video content
- Genre information
- Backdrop images

**Endpoints:**
- `/api/trending/movies` - Trending movies
- `/api/movies/:id` - Movie details
- `/api/tv/:id` - TV show details
- `/api/genres` - Genre lists
- `/api/genre-movies/:genreId` - Movies by genre
- `/api/media/genre-tvshows/:genreId` - TV shows by genre
- `/api/movies/:id/videos` - Movie trailers
- `/api/tv/:id/videos` - TV show trailers
- `/api/movies/:id/credits` - Movie cast/crew
- `/api/tv/:id/credits` - TV show cast/crew

### 2. MUSICBRAINZ & COVER ART ARCHIVE - MUSIC
**Used for ALL music display elements:**
- Album cover artwork (high-resolution via Cover Art Archive)
- Album and artist names
- Artist information and biography
- Release dates and metadata
- Genre classifications
- Music metadata

**Endpoints:**
- `/api/music/top-albums` - Popular albums across genres
- `/api/music/top-artists` - Popular artists
- `/api/music/genre-albums/:genre` - Albums by genre
- External: `https://musicbrainz.org/ws/2/` - MusicBrainz API
- External: `https://coverartarchive.org/release-group/{mbid}` - Cover Art Archive

**Cover Art Archive Integration:**
- **Multi-Resolution Support**: 1200px → 500px → original → 250px
- **Smart Prioritization**: Front covers and approved images first
- **API Compliance**: Proper User-Agent headers required
- **Error Handling**: 404 detection, rate limiting, network failure recovery
- **Fallback System**: Color-coded placeholders when artwork unavailable

### 3. Torrent Sources (YTS, EZTV, etc.) - PLAYBACK ONLY
**Used ONLY for:**
- Magnet links for streaming (movies, TV, music)
- Torrent metadata (size, seeders, quality)

**NEVER used for:**
- Display posters or artwork
- Titles or names
- Cast information
- Plot summaries or descriptions
- Any visual elements

**Endpoints:**
- `/api/search?query=...&type=movie` - Movie torrent search
- `/api/search?query=...&type=tv` - TV show torrent search  
- `/api/search?query=...&type=music` - Music torrent search

## Component Guidelines

### MediaCard Component
- **MUST** only use the `media` prop for display (TMDB for movies/TV, MusicBrainz for music)
- **MUST NOT** use any torrent data for visual elements
- **MUST** use proper image sources (TMDB poster_path, Cover Art Archive URLs)
- **MUST** use original source titles/names for text

### Play Button Handlers
- **MUST** use original source title/name for player title (TMDB/MusicBrainz)
- **MUST** only use torrent magnetLink for streaming
- **MUST NOT** use torrent title for display

### Page Components (Dashboard, TVShows, Music)
- **MUST** fetch display data from appropriate endpoints (TMDB/MusicBrainz)
- **MUST** only use torrent search for play button functionality
- **MUST NOT** mix torrent and display source data

## Code Examples

### ✅ CORRECT - Using proper display sources
```javascript
// Movies/TV: Display uses TMDB data
const imageUrl = `https://image.tmdb.org/t/p/w500${movie.poster_path}`;
const title = movie.title; // TMDB title

// Music: Display uses MusicBrainz/Cover Art Archive data
const albumImageUrl = album.coverArt || generateColorPlaceholder(album.title); // Cover Art Archive URL or fallback
const albumTitle = album.title; // MusicBrainz title

// Play button uses torrent data only for magnet link
const bestTorrent = torrentResults[0];
setVideoPlayer({ 
  magnetLink: bestTorrent.magnetLink, // Torrent data
  title: movie.title || album.name // Display source data for title
});
```

### ❌ WRONG - Mixing sources for display
```javascript
// NEVER do this - using torrent data for display
const imageUrl = torrentResult.poster; // WRONG
const title = torrentResult.title; // WRONG
```

## Enforcement

1. All components have been documented with clear comments
2. Search service is clearly marked as torrent-only
3. Display endpoints are clearly marked as TMDB-only
4. Play button handlers use TMDB titles with torrent magnet links

## Testing

To verify correct separation:
1. Check that movie/TV posters come from `image.tmdb.org`
2. Check that music album art comes from `coverartarchive.org` or proper fallbacks
3. Check that all titles match display source data (TMDB/MusicBrainz)
4. Check that play buttons only use torrent magnet links
5. Verify no torrent data appears in UI elements

## Maintenance

When adding new features:
1. Always use proper display sources (TMDB for movies/TV, MusicBrainz for music)
2. Only use torrent sources for playback
3. Add comments to new code following these guidelines
4. Test to ensure no mixing of data sources
5. Ensure new content types follow the same separation pattern 