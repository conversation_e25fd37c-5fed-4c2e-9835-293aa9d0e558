# Torvie - AI-Powered Streaming Discovery

A modern, responsive streaming platform with AI-powered movie and TV show discovery features. Built with React, Tailwind CSS, and Express.js.

## 🎬 Features

- **Dark Gemini Theme**: Beautiful dark UI with gradient effects and interactive animations
- **AI Discovery**: Chat interface for AI-powered movie recommendations
- **Trending Movies**: Display of current trending movies with detailed information from TMDB API
- **Responsive Design**: Fully responsive layout that works on all devices
- **Interactive UI**: Hover effects, smooth transitions, and engaging animations
- **Movie Cards**: Interactive movie cards with genre tags and action buttons
- **Loading States**: Elegant loading screens and skeleton components
- **Error Handling**: Graceful error handling with user-friendly messages
- **Navigation**: React Router for seamless page navigation
- **Movie Details**: Comprehensive movie detail pages with full information

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher) and npm/yarn (for local development)
- **OR** Docker and Docker Compose (for containerized deployment)

### Installation & Running

#### Option 1: Docker Deployment (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Torvie
   ```

2. **Start with Docker**
   ```bash
   # Using the management script
   ./docker-manage.bat start
   
   # Or using docker-compose directly
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost
   - Backend API: http://localhost:3000

#### Option 2: Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Torvie
   ```

2. **Install client dependencies**
   ```bash
   cd client
   npm install
   ```

3. **Install server dependencies**
   ```bash
   cd ../server
   npm install
   ```

4. **Start the application**
   ```bash
   # Using the startup script (Recommended)
   ./start-app.bat
   
   # Or manually:
   # Terminal 1 - Backend
   cd server
   npm run dev
   
   # Terminal 2 - Frontend
   cd client
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000` to see the application

### Docker Management

Use the provided management script for easy Docker operations:

```bash
# Start containers
./docker-manage.bat start

# Stop containers
./docker-manage.bat stop

# Restart containers
./docker-manage.bat restart

# Rebuild and start containers
./docker-manage.bat rebuild

# View logs
./docker-manage.bat logs

# Check status
./docker-manage.bat status

# Clean up Docker resources
./docker-manage.bat clean
```

## 🏗️ Project Structure

```
Torvie/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   │   ├── Header.jsx
│   │   │   ├── Footer.jsx
│   │   │   ├── MediaCard.jsx
│   │   │   └── BackgroundEffects.jsx
│   │   ├── pages/          # Main views
│   │   │   ├── Dashboard.jsx
│   │   │   └── MovieDetails.jsx
│   │   ├── App.jsx         # Main application component
│   │   ├── main.jsx        # Application entry point
│   │   └── index.css       # Global styles and Tailwind imports
│   ├── index.html          # HTML template
│   ├── package.json        # Frontend dependencies
│   ├── vite.config.js      # Vite configuration
│   ├── tailwind.config.js  # Tailwind CSS configuration
│   ├── postcss.config.js   # PostCSS configuration
│   ├── Dockerfile          # Frontend Docker configuration
│   ├── Dockerfile.dev      # Development Docker configuration
│   ├── nginx.conf          # Nginx configuration for production
│   └── .dockerignore       # Docker ignore file
├── server/                 # Express.js backend
│   ├── server.js           # Main server file
│   ├── .env                # Environment variables (TMDB API key)
│   ├── package.json        # Backend dependencies
│   ├── Dockerfile          # Backend Docker configuration
│   ├── Dockerfile.dev      # Development Docker configuration
│   └── .dockerignore       # Docker ignore file
├── docker-compose.yml      # Docker Compose configuration
├── docker-manage.bat       # Docker management script
├── setup.bat               # Windows setup script
├── start-app.bat           # Windows startup script
├── .gitignore              # Git ignore file
└── README.md               # This file
```

## 🎨 UI Components

### Background Effects
- Animated gradient background
- Mouse-following aurora effect
- Smooth color transitions

### Header
- Sticky navigation with backdrop blur
- TORVIE logo with gradient effects
- Navigation links for Movies, TV Shows, and Games
- React Router integration for seamless navigation

### Featured Hero
- Large hero section with movie backdrop
- Movie title, overview, and call-to-action button
- Gradient overlay for text readability

### AI Discovery
- Chat interface for AI recommendations
- Sample conversation display
- Input field for user queries

### Movie Grid
- Responsive grid layout
- Interactive movie cards with hover effects
- Genre tags and action buttons
- Loading skeleton states

### Movie Details Page
- Comprehensive movie information
- Large backdrop image with poster
- Genre tags and action buttons
- Placeholder sections for cast, similar movies, and reviews
- Back navigation to dashboard

## 🔧 Configuration

### TMDB API Integration ✅

The application is now configured with a TMDB API key and will fetch real movie data:
- **API Key**: Configured in `server/.env`
- **Real Data**: Trending movies and movie details from TMDB
- **Fallback**: Mock data available if API is unavailable

### Customization

- **Colors**: Modify the gradient colors in `App.jsx` and `index.css`
- **Animations**: Adjust animation durations and effects in the CSS
- **Layout**: Customize the grid layout and spacing in the components

## 🛠️ Development

### Available Scripts

**Client (Frontend)**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

**Server (Backend)**
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server

### Adding New Features

1. **New Components**: Add them to `src/components/` directory
2. **New Pages**: Add them to `src/pages/` directory and update routes in `App.jsx`
3. **API Endpoints**: Add new routes in `server/server.js`
4. **Styling**: Use Tailwind CSS classes or add custom CSS

## 🎯 Key Features Explained

### AI Discovery Interface
The AI chat interface allows users to ask for movie recommendations in natural language. The interface includes:
- Sample conversation to demonstrate functionality
- Input field for user queries
- Gradient styling matching the overall theme

### Interactive Movie Cards
Each movie card features:
- Movie poster with fallback image handling
- Hover effects with action buttons (Add to Watchlist, Play, More Info)
- Genre tags displayed as pills
- Smooth scaling and border effects
- Click navigation to movie details page

### Responsive Design
The application is fully responsive with:
- Mobile-first approach
- Flexible grid layouts
- Adaptive typography
- Touch-friendly interactions

### Navigation System
React Router provides seamless navigation:
- Dashboard (home page) with trending movies
- Individual movie detail pages
- Placeholder pages for TV Shows and Games
- 404 page for invalid routes

## 📱 Current Status

### ✅ Completed (Phase 1 & 2)
- Backend server with Express.js
- TMDB API integration with real movie data
- React frontend with Tailwind CSS
- Component-based architecture
- React Router navigation
- Dashboard with trending movies from TMDB
- Movie details page with real data
- Interactive UI with hover effects
- Loading states and error handling
- Responsive design

### 🔄 In Progress (Phase 3)
- AI chat functionality
- Search integration
- Torrent search backend

### 📋 Planned (Phase 4-6)
- Video streaming functionality
- User authentication
- Trakt.tv integration
- Gaming features
- Watch parties
- Community features

## 🔮 Future Enhancements

- User authentication and profiles
- Watchlist functionality
- Search functionality
- TV show integration
- Real AI recommendation engine
- User ratings and reviews
- Social features
- Video streaming with WebTorrent
- Subtitle integration
- Discord Rich Presence
- Real-Debrid integration

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

---

**Built with ❤️ for the future of streaming** 