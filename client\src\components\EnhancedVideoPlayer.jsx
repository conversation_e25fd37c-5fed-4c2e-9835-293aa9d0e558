import React, { useState, useEffect, useRef, useCallback } from 'react';
import StreamLoadingModal from './StreamLoadingModal';
import AdTestControls from './AdTestControls';
import AD_CONFIG from '../utils/adConfig';
import { useAuth } from '../contexts/AuthContext';
import { useProfile } from '../contexts/ProfileContext';
import { watchProgressStorage } from '../utils/watchProgressStorage';
import { TypewriterText, streamingLoadingPhrases, getRandomPhrase } from '../utils/loadingPhrases.jsx';

const EnhancedVideoPlayer = ({ 
  magnetLink, 
  title, 
  onClose, 
  fallbackTorrents = [], 
  contentId, 
  contentType = 'movie', 
  posterUrl = null, 
  episodeInfo = null 
}) => {
  const { user } = useAuth();
  const { profile } = useProfile();
  
  // Core video state
  const [streamUrl, setStreamUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [streamInfo, setStreamInfo] = useState(null);
  const [showLoadingModal, setShowLoadingModal] = useState(false);
  const [currentTorrentIndex, setCurrentTorrentIndex] = useState(0);
  const [allTorrents, setAllTorrents] = useState([]);
  const videoRef = useRef(null);

  // Enhanced controls state
  const [showControls, setShowControls] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isSeeking, setIsSeeking] = useState(false);

  // Menu states
  const [showQualityMenu, setShowQualityMenu] = useState(false);
  const [showSpeedMenu, setShowSpeedMenu] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);

  // Subtitle state
  const [availableSubtitles, setAvailableSubtitles] = useState([]);
  const [selectedSubtitle, setSelectedSubtitle] = useState(null);
  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false);
  const [subtitleText, setSubtitleText] = useState('');

  // Watch progress state
  const [watchProgress, setWatchProgress] = useState(null);
  const [isResuming, setIsResuming] = useState(false);
  const [showResumePrompt, setShowResumePrompt] = useState(false);
  const progressSaveTimeoutRef = useRef(null);

  // Ad state
  const [isAdPlaying, setIsAdPlaying] = useState(false);
  const [showAdTestControls, setShowAdTestControls] = useState(false);
  const adsManagerRef = useRef(null);
  const adsLoaderRef = useRef(null);
  const adDisplayContainerRef = useRef(null);
  const adContainerRef = useRef(null);

  // Torrent state
  const [isPaused, setIsPaused] = useState(false);
  const [pauseResumeInProgress, setPauseResumeInProgress] = useState(false);
  const [connectionLost, setConnectionLost] = useState(false);

  // Quality and speed options
  const qualityOptions = [
    { label: 'Auto', value: 'auto' },
    { label: '1080p', value: '1080p' },
    { label: '720p', value: '720p' },
    { label: '480p', value: '480p' }
  ];

  const speedOptions = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2];

  // Auto-hide controls
  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeout) clearTimeout(controlsTimeout);
    setShowControls(true);
    const timeout = setTimeout(() => {
      if (!videoRef.current?.paused) {
        setShowControls(false);
      }
    }, 3000);
    setControlsTimeout(timeout);
  }, [controlsTimeout]);

  // Format time for display
  const formatTime = useCallback((seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Handle playback speed change
  const handleSpeedChange = useCallback((speed) => {
    setPlaybackSpeed(speed);
    if (videoRef.current) {
      videoRef.current.playbackRate = speed;
    }
    setShowSpeedMenu(false);
    resetControlsTimeout();
  }, [resetControlsTimeout]);

  // Handle quality change
  const handleQualityChange = useCallback((quality) => {
    console.log(`🎬 Quality changed to: ${quality}`);
    setShowQualityMenu(false);
    resetControlsTimeout();
  }, [resetControlsTimeout]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
    resetControlsTimeout();
  }, [resetControlsTimeout]);

  // Handle volume change
  const handleVolumeChange = useCallback((newVolume) => {
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      videoRef.current.muted = newVolume === 0;
      setIsMuted(newVolume === 0);
    }
    resetControlsTimeout();
  }, [resetControlsTimeout]);

  // Toggle mute
  const toggleMute = useCallback(() => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    if (videoRef.current) {
      videoRef.current.muted = newMuted;
    }
    resetControlsTimeout();
  }, [isMuted, resetControlsTimeout]);

  // Skip forward/backward
  const skipTime = useCallback((seconds) => {
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(videoRef.current.currentTime + seconds, videoRef.current.duration));
      videoRef.current.currentTime = newTime;
    }
    resetControlsTimeout();
  }, [resetControlsTimeout]);

  // Handle seek
  const handleSeek = useCallback((e) => {
    if (!videoRef.current || !duration) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;
    
    videoRef.current.currentTime = newTime;
    setCurrentTime(newTime);
    resetControlsTimeout();
  }, [duration, resetControlsTimeout]);

  // Enhanced keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!videoRef.current) return;

      switch (e.key) {
        case ' ':
          e.preventDefault();
          if (videoRef.current.paused) {
            videoRef.current.play();
          } else {
            videoRef.current.pause();
          }
          break;
        case 'ArrowRight':
          e.preventDefault();
          skipTime(10);
          break;
        case 'ArrowLeft':
          e.preventDefault();
          skipTime(-10);
          break;
        case 'ArrowUp':
          e.preventDefault();
          handleVolumeChange(Math.min(1, volume + 0.1));
          break;
        case 'ArrowDown':
          e.preventDefault();
          handleVolumeChange(Math.max(0, volume - 0.1));
          break;
        case 'm':
        case 'M':
          e.preventDefault();
          toggleMute();
          break;
        case 'f':
        case 'F':
          e.preventDefault();
          toggleFullscreen();
          break;
        case 'c':
        case 'C':
          e.preventDefault();
          if (availableSubtitles.length > 0) {
            setShowSubtitleMenu(!showSubtitleMenu);
          }
          break;
        case 'q':
        case 'Q':
          e.preventDefault();
          setShowQualityMenu(!showQualityMenu);
          break;
        case 's':
        case 'S':
          e.preventDefault();
          setShowSpeedMenu(!showSpeedMenu);
          break;
        case 'Escape':
          if (isFullscreen) {
            document.exitFullscreen();
            setIsFullscreen(false);
          }
          break;
      }
      resetControlsTimeout();
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    videoRef, 
    volume, 
    isMuted, 
    isFullscreen, 
    showSubtitleMenu, 
    showQualityMenu, 
    showSpeedMenu, 
    availableSubtitles.length,
    skipTime,
    handleVolumeChange,
    toggleMute,
    toggleFullscreen,
    resetControlsTimeout
  ]);

  // Load existing watch progress
  useEffect(() => {
    const loadWatchProgress = async () => {
      if (user && profile && contentId) {
        console.log(`📺 Loading watch progress for content: ${contentId}`);
        const progress = await watchProgressStorage.getContentProgress(user.id, profile.id, contentId);
        
        if (progress && progress.currentTime > 30) {
          console.log(`📺 Found existing progress: ${Math.round(progress.watchedPercentage)}% watched`);
          setWatchProgress(progress);
          setShowResumePrompt(true);
        }
      }
    };

    loadWatchProgress();
  }, [user, profile, contentId]);

  // Handle resume choice
  const handleResumeChoice = (shouldResume) => {
    setShowResumePrompt(false);
    if (shouldResume && watchProgress) {
      setIsResuming(true);
      console.log(`📺 Resuming from ${watchProgress.currentTime}s`);
    } else {
      setWatchProgress(null);
    }
  };

  // Save watch progress
  const saveWatchProgress = async (currentTime, duration, isCompleted = false) => {
    if (!user || !profile || !contentId || !duration) return;
    
    const watchedPercentage = watchProgressStorage.calculateWatchPercentage(currentTime, duration);
    
    if (watchedPercentage > 0 || isCompleted) {
      const progressData = {
        contentId,
        contentType,
        title,
        poster: posterUrl,
        currentTime,
        duration,
        watchedPercentage,
        lastWatched: new Date().toISOString(),
        isCompleted: isCompleted || watchProgressStorage.shouldMarkAsCompleted(currentTime, duration),
        episodeInfo
      };
      
      await watchProgressStorage.saveProgress(user.id, profile.id, progressData);
    }
  };

  // Handle time update
  const handleTimeUpdate = () => {
    if (!videoRef.current) return;
    
    const current = videoRef.current.currentTime;
    const total = videoRef.current.duration;
    
    setCurrentTime(current);
    setDuration(total);
    
    // Save progress every 5 seconds
    if (progressSaveTimeoutRef.current) clearTimeout(progressSaveTimeoutRef.current);
    progressSaveTimeoutRef.current = setTimeout(() => {
      saveWatchProgress(current, total);
    }, 5000);
  };

  // Handle video end
  const handleVideoEnd = () => {
    if (videoRef.current) {
      saveWatchProgress(videoRef.current.currentTime, videoRef.current.duration, true);
    }
  };

  // Resume to saved position
  const resumeToSavedPosition = () => {
    if (isResuming && watchProgress && videoRef.current) {
      videoRef.current.currentTime = watchProgress.currentTime;
      setIsResuming(false);
    }
  };

  // Handle video play
  const handleVideoPlay = () => {
    resetControlsTimeout();
  };

  // Handle close
  const handleClose = () => {
    if (progressSaveTimeoutRef.current) {
      clearTimeout(progressSaveTimeoutRef.current);
    }
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    onClose();
  };

  // Loading state
  if (isLoading || showLoadingModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[999999]">
        <div className="text-center max-w-md px-4">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-white mb-4">Starting Stream</h2>
          
          <div className="text-cyan-300 text-lg mb-4 min-h-[2rem]">
            <TypewriterText
              text={getRandomPhrase(streamingLoadingPhrases)}
              speed={40}
              className="loading-glow"
              showCursor={true}
            />
          </div>
          
          <p className="text-gray-400 text-sm">Connecting to torrent and preparing video...</p>
          <p className="text-xs text-gray-500 mt-2 opacity-60">This may take a few moments</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return null; // Error handled by modal
  }

  // Resume prompt
  if (showResumePrompt && watchProgress) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[999999]">
        <div className="bg-gray-900 p-8 rounded-lg max-w-md mx-4 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Resume Watching?</h2>
          <p className="text-gray-300 mb-6">
            You were watching &quot;{title}&quot; and have {Math.round(watchProgress.watchedPercentage)}% remaining.
          </p>
          <div className="flex gap-4">
            <button
              onClick={() => handleResumeChoice(true)}
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition-colors"
            >
              Resume
            </button>
            <button
              onClick={() => handleResumeChoice(false)}
              className="flex-1 bg-gray-700 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-600 transition-colors"
            >
              Start Over
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="fixed bg-black flex items-center justify-center" 
      style={{ 
        position: 'fixed',
        top: 0, 
        left: 0, 
        right: 0, 
        bottom: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 999999,
        margin: 0,
        padding: 0
      }}
    >
      {/* Full Screen Video Container */}
      <div className="w-full h-full flex flex-col bg-black">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute right-6 top-6 p-3 text-white hover:bg-black hover:bg-opacity-50 rounded-full transition-all duration-200 backdrop-blur-sm z-[1000000]"
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Enhanced Controls Overlay */}
        {showControls && (
          <div className="absolute inset-0 pointer-events-none z-[1001]">
            {/* Top Controls Bar */}
            <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-4 pointer-events-auto">
              <div className="flex justify-between items-center">
                <h1 className="text-white text-lg font-semibold truncate">{title}</h1>
                <div className="flex items-center space-x-2">
                  {/* Quality Button */}
                  <div className="relative">
                    <button
                      onClick={() => setShowQualityMenu(!showQualityMenu)}
                      className="bg-black/60 text-white px-3 py-1 rounded hover:bg-black/80 transition-all text-sm"
                    >
                      {qualityOptions[0].label}
                    </button>
                    {showQualityMenu && (
                      <div className="absolute top-full right-0 mt-1 bg-gray-900 rounded-lg border border-gray-700 min-w-32">
                        {qualityOptions.map((option) => (
                          <button
                            key={option.value}
                            onClick={() => handleQualityChange(option.value)}
                            className="w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition-colors text-gray-300 text-sm"
                          >
                            {option.label}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Speed Button */}
                  <div className="relative">
                    <button
                      onClick={() => setShowSpeedMenu(!showSpeedMenu)}
                      className="bg-black/60 text-white px-3 py-1 rounded hover:bg-black/80 transition-all text-sm"
                    >
                      {playbackSpeed}x
                    </button>
                    {showSpeedMenu && (
                      <div className="absolute top-full right-0 mt-1 bg-gray-900 rounded-lg border border-gray-700 min-w-20">
                        {speedOptions.map((speed) => (
                          <button
                            key={speed}
                            onClick={() => handleSpeedChange(speed)}
                            className="w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition-colors text-gray-300 text-sm"
                          >
                            {speed}x
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom Controls Bar */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 pointer-events-auto">
              {/* Progress Bar */}
              <div className="mb-4">
                <div 
                  className="w-full h-1 bg-gray-700 rounded-full cursor-pointer relative"
                  onClick={handleSeek}
                >
                  <div 
                    className="h-full bg-blue-500 rounded-full transition-all"
                    style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                  />
                  <div 
                    className="absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full shadow-lg"
                    style={{ left: `${duration ? (currentTime / duration) * 100 : 0}%`, marginLeft: '-6px' }}
                  />
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Play/Pause */}
                  <button
                    onClick={() => {
                      if (videoRef.current?.paused) {
                        videoRef.current.play();
                      } else {
                        videoRef.current.pause();
                      }
                    }}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    {videoRef.current?.paused ? (
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    ) : (
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                      </svg>
                    )}
                  </button>

                  {/* Skip Backward */}
                  <button
                    onClick={() => skipTime(-10)}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                    </svg>
                  </button>

                  {/* Skip Forward */}
                  <button
                    onClick={() => skipTime(10)}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z" transform="scale(-1, 1) translate(-24, 0)"/>
                    </svg>
                  </button>

                  {/* Volume Control */}
                  <div className="relative flex items-center space-x-2">
                    <button
                      onClick={toggleMute}
                      onMouseEnter={() => setShowVolumeSlider(true)}
                      className="text-white hover:text-blue-400 transition-colors"
                    >
                      {isMuted || volume === 0 ? (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                        </svg>
                      ) : volume < 0.5 ? (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"/>
                        </svg>
                      ) : (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                        </svg>
                      )}
                    </button>
                    {showVolumeSlider && (
                      <div 
                        className="absolute bottom-full left-0 mb-2 bg-gray-900 rounded-lg border border-gray-700 p-2"
                        onMouseLeave={() => setShowVolumeSlider(false)}
                      >
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={volume}
                          onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                          className="w-20 h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                          style={{
                            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${volume * 100}%, #374151 ${volume * 100}%, #374151 100%)`
                          }}
                        />
                      </div>
                    )}
                  </div>

                  {/* Time Display */}
                  <div className="text-white text-sm">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Subtitle Button */}
                  {availableSubtitles.length > 0 && (
                    <div className="relative">
                      <button
                        onClick={() => setShowSubtitleMenu(!showSubtitleMenu)}
                        className="bg-black/60 text-white px-3 py-1 rounded hover:bg-black/80 transition-all text-sm flex items-center space-x-1"
                      >
                        <span>🔤</span>
                        <span>{selectedSubtitle ? selectedSubtitle.language : 'Off'}</span>
                      </button>
                      {showSubtitleMenu && (
                        <div className="absolute bottom-full right-0 mb-2 bg-gray-900 rounded-lg border border-gray-700 min-w-48">
                          <div className="p-2">
                            <button
                              onClick={() => {
                                setSelectedSubtitle(null);
                                setSubtitleText('');
                                setShowSubtitleMenu(false);
                              }}
                              className={`w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm ${
                                !selectedSubtitle ? 'bg-blue-600 text-white' : 'text-gray-300'
                              }`}
                            >
                              Off
                            </button>
                            {availableSubtitles.map((subtitle, index) => (
                              <button
                                key={index}
                                onClick={() => {
                                  setSelectedSubtitle(subtitle);
                                  // loadSubtitleFile(streamInfo.infoHash, subtitle.path);
                                  setShowSubtitleMenu(false);
                                }}
                                className={`w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition-colors text-sm ${
                                  selectedSubtitle?.path === subtitle.path ? 'bg-blue-600 text-white' : 'text-gray-300'
                                }`}
                              >
                                {subtitle.language}
                                {subtitle.isSDH && <span className="text-xs text-blue-400 ml-2">SDH</span>}
                                {subtitle.isForced && <span className="text-xs text-yellow-400 ml-2">Forced</span>}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Fullscreen Button */}
                  <button
                    onClick={toggleFullscreen}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    {isFullscreen ? (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                      </svg>
                    ) : (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                      </svg>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Video Player */}
        <div className="w-full h-full relative">
          {/* Ad Container */}
          <div
            ref={adContainerRef}
            className="absolute top-0 left-0 w-full h-full pointer-events-none"
            style={{ zIndex: isAdPlaying ? 1000 : -1 }}
          />

          {streamUrl && (
            <video
              ref={videoRef}
              className="w-full h-full object-contain"
              controls={false}
              preload="auto"
              playsInline
              muted={isMuted}
              style={{ background: 'black' }}
              onLoadStart={() => console.log('🎬 Video load started')}
              onLoadedMetadata={() => {
                console.log('🎬 Video metadata loaded');
                resumeToSavedPosition();
              }}
              onCanPlay={() => {
                console.log('🎬 Video can start playing');
                if (!AD_CONFIG.SETTINGS.ENABLE_PREROLL && !isAdPlaying) {
                  videoRef.current?.play().catch(e => console.log('🎬 Auto-play failed:', e));
                }
              }}
              onTimeUpdate={handleTimeUpdate}
              onEnded={handleVideoEnd}
              onPlay={handleVideoPlay}
              onClick={() => {
                if (!isAdPlaying) {
                  if (videoRef.current?.paused) {
                    videoRef.current.play();
                  } else {
                    videoRef.current.pause();
                  }
                }
              }}
              onMouseMove={resetControlsTimeout}
            >
              <source src={streamUrl} type="video/mp4" />
              {selectedSubtitle && subtitleText && (
                <track
                  kind="subtitles"
                  srcLang={selectedSubtitle.code}
                  label={selectedSubtitle.language}
                  src={`data:text/vtt;base64,${btoa(subtitleText)}`}
                  default
                />
              )}
              Your browser does not support the video tag.
            </video>
          )}

          {/* Status Indicators */}
          {isAdPlaying && (
            <div className="absolute top-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm z-[1001]">
              Ad Playing...
            </div>
          )}

          {!streamInfo?.isLocalFile && streamInfo?.infoHash && !isAdPlaying && (
            <div className="absolute top-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm z-[1001] flex items-center space-x-2">
              <span>📥</span>
              <span>Downloading in Background</span>
            </div>
          )}

          {/* Controls Hint */}
          {!showControls && (
            <div className="absolute bottom-4 right-4 bg-black bg-opacity-60 text-white px-3 py-2 rounded text-xs z-[1001]">
              <div className="space-y-1">
                <div>Space: Play/Pause</div>
                <div>← →: Skip 10s</div>
                <div>↑ ↓: Volume</div>
                <div>M: Mute</div>
                <div>F: Fullscreen</div>
                <div>C: Subtitles</div>
                <div>Q: Quality</div>
                <div>S: Speed</div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Ad Test Controls */}
      {showAdTestControls && (
        <AdTestControls onClose={() => setShowAdTestControls(false)} />
      )}

      {/* Custom CSS for slider */}
      <style>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 12px;
          width: 12px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
        }
        .slider::-moz-range-thumb {
          height: 12px;
          width: 12px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: none;
        }
      `}</style>
    </div>
  );
};

export default EnhancedVideoPlayer; 