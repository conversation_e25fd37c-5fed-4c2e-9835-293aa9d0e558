import React, { useEffect, useState } from "react";

export const funnyLoadingPhrases = [
  "Hold up, I had to finish shaving my fish.",
  "Rewiring the flux capacitor... again.",
  "Counting how many chickens it takes to cross the road.",
  "Still teaching AI how to blink.",
  "Feeding the hamsters that power this app.",
  "Untangling the spaghetti code… send help.",
  "Warming up the lasers. Pew pew pew!",
  "Looking for the Any key...",
  "Almost there. No idea where 'there' is.",
  "Still faster than your ex texting back.",
  "Synthesizing gravity particles...",
  "Downloading data from Area 51.",
  "Summoning the loading dragons...",
  "Casting 'Loadicus Maximus'...",
  "Compiling nonsense...",
  "Deploying caffeine to production...",
  "Reticulating splines with extra sass...",
  "Convincing electrons to behave...",
  "Teaching pixels how to dance...",
  "Bribing the internet gods...",
  "Downloading more RAM from the cloud...",
  "Converting coffee to code...",
  "Debugging the matrix...",
  "Asking nicely for the data to load...",
  "Turning it off and on again...",
  "Consulting the ancient scrolls of StackOverflow...",
  "Waking up the server hamsters...",
  "Optimizing for maximum procrastination...",
  "Loading... or is it buffering? Who knows.",
  "Making the magic happen... eventually."
];

export const streamingLoadingPhrases = [
  "Reticulating splines with extra swagger...",
  "Negotiating with digital overlords...",
  "Waking up the seeders from their nap...",
  "Convincing torrents to share their goodies...",
  "Connecting to the peer-to-peer matrix...",
  "Gathering movie magic from the internet void...",
  "Unspooling digital film reels...",
  "Buffering the good bits (and skipping ads)...",
  "Teaching magnets how to link properly...",
  "Summoning the streaming spirits...",
  "Downloading pixels one by one...",
  "Converting zeros and ones to entertainment...",
  "Asking the internet very nicely...",
  "Warming up the video compression gnomes...",
  "Synchronizing with satellite dish fairies..."
];

export const getRandomPhrase = (phraseArray = funnyLoadingPhrases) => {
  return phraseArray[Math.floor(Math.random() * phraseArray.length)];
};

// Typewriter Effect Component
export const TypewriterText = ({ 
  text, 
  speed = 50, 
  className = "", 
  showCursor = true,
  onComplete = null 
}) => {
  const [displayText, setDisplayText] = useState("");
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    setDisplayText("");
    setIsComplete(false);
    let index = 0;
    
    const interval = setInterval(() => {
      if (index <= text.length) {
        setDisplayText(text.slice(0, index));
        index++;
      } else {
        setIsComplete(true);
        if (onComplete) onComplete();
        clearInterval(interval);
      }
    }, speed);
    
    return () => clearInterval(interval);
  }, [text, speed, onComplete]);

  return (
    <span className={className}>
      {displayText}
      {showCursor && <span className="typewriter-cursor">|</span>}
    </span>
  );
};

// Cycling Typewriter Component for multiple phrases
export const CyclingTypewriter = ({ 
  phrases = funnyLoadingPhrases, 
  speed = 50, 
  pauseBetween = 4000,
  className = "",
  showCursor = true 
}) => {
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [currentPhrase, setCurrentPhrase] = useState("");
  const [isTyping, setIsTyping] = useState(true);

  useEffect(() => {
    // Start with a random phrase
    const randomIndex = Math.floor(Math.random() * phrases.length);
    setCurrentPhraseIndex(randomIndex);
    setCurrentPhrase(phrases[randomIndex]);
    setIsTyping(true);
  }, [phrases]);

  const getNextRandomPhrase = (currentIndex, phraseArray) => {
    if (phraseArray.length <= 1) return 0;
    
    let nextIndex;
    do {
      nextIndex = Math.floor(Math.random() * phraseArray.length);
    } while (nextIndex === currentIndex);
    
    return nextIndex;
  };

  const handleComplete = () => {
    setIsTyping(false);
    // Wait for the full pause duration before starting next phrase
    setTimeout(() => {
      const nextIndex = getNextRandomPhrase(currentPhraseIndex, phrases);
      setCurrentPhraseIndex(nextIndex);
      setCurrentPhrase(phrases[nextIndex]);
      setIsTyping(true);
    }, pauseBetween);
  };

  return (
    <div className={className}>
      {isTyping ? (
        <TypewriterText
          text={currentPhrase}
          speed={speed}
          showCursor={showCursor}
          onComplete={handleComplete}
        />
      ) : (
        <span>
          {currentPhrase}
          {showCursor && <span className="typewriter-cursor">|</span>}
        </span>
      )}
    </div>
  );
};

export default {
  funnyLoadingPhrases,
  streamingLoadingPhrases,
  getRandomPhrase,
  TypewriterText,
  CyclingTypewriter
}; 