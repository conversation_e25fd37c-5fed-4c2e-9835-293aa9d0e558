import React from 'react';
import TrailerPlayer from './TrailerPlayer';

const TrailerModal = ({ isOpen, onClose, trailer, content }) => {
  if (!isOpen || !trailer || !content) return null;
  return (
    <div 
      className="fixed inset-0 z-40 cursor-pointer"
      onClick={onClose}
    >
      {/* Glass overlay that covers ENTIRE viewport */}
      <div 
        className="fixed backdrop-blur-xl bg-black/70" 
        style={{ 
          top: 0, left: 0, right: 0, bottom: 0,
          width: '100vw', height: '100vh'
        }}
      />
      {/* Header spacer to keep header visible and clickable */}
      <div 
        className="fixed bg-transparent z-[60]" 
        style={{ 
          top: 0, left: 0, right: 0, 
          height: '80px', width: '100vw'
        }}
      />
      {/* Trailer container positioned below header */}
      <div 
        className="fixed flex items-center justify-center z-[51]" 
        style={{ 
          top: '80px', left: 0, right: 0, bottom: 0,
          width: '100vw', height: 'calc(100vh - 80px)'
        }}
      >
        <div className="w-[80vw] h-[70vh] max-w-5xl flex flex-col rounded-xl overflow-hidden shadow-2xl bg-black relative" onClick={e => e.stopPropagation()}>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 text-white hover:bg-black/50 rounded-full transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <div className="w-full h-full bg-black">
            <TrailerPlayer 
              videoId={trailer.key} 
              title={content.title || content.name} 
              posterUrl={content.poster_path ? `https://image.tmdb.org/t/p/w500${content.poster_path}` : undefined} 
              startMuted={false} 
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrailerModal; 