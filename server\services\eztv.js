import axios from 'axios';

async function searchEZTV(query) {
  const url = `https://eztv.re/api/get-torrents?limit=50&keywords=${encodeURIComponent(query)}`;
  const res = await axios.get(url);
  if (!res.data.torrents) return [];
  return res.data.torrents.map(t => ({
    title: t.title,
    magnetLink: t.magnet_url,
    seeders: t.seeds || 0,
    size: t.size_bytes,
    source: 'EZTV',
    link: t.torrent_url,
    episode: t.episode,
    season: t.season,
    imdb_id: t.imdb_id,
  }));
}

export { searchEZTV }; 