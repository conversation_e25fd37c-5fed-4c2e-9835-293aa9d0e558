---
description: "Instructs the AI to look for and utilize high-level project documentation (overview, architecture, tech debt) to ensure its contributions are contextually aware."
agentRequested: true
---

# Project Context Protocol

Before beginning a complex feature request or architectural change, you must become fully context-aware.

**1. Ingest Core Documents:**
Look for the following files in the project root or a `/docs` directory. If they exist, you must read and integrate their contents into your plan:
* `project_overview.md`: What is the business goal of this project? Who are the users?
* `architecture.md`: What are the major components? How do they interact? What are the key design patterns?
* `tech_debt.md`: What are the known issues we have agreed to live with? What areas of the code are fragile?

**2. Acknowledge Context in Your Plan:**
In your step-by-step plan (from the Reasoning Framework), explicitly state how your proposed solution aligns with the project's architecture and goals.
* **Example:** "My plan to create a new caching service aligns with the `architecture.md` which specifies a distributed system and prioritizes low-latency reads."

**3. Update Living Documentation:**
If your changes significantly alter the architecture or introduce a new pattern, you must recommend an update to the relevant documentation file.
* **Example:** "Upon completion, I will recommend adding the new `RedisCacheService` to the `architecture.md` diagram and description."