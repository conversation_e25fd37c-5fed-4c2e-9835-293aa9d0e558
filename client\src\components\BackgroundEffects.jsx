import React, { useEffect } from 'react';

const BackgroundEffects = () => {
    useEffect(() => {
        const handleMouseMove = (e) => {
            document.documentElement.style.setProperty('--x', e.clientX + 'px');
            document.documentElement.style.setProperty('--y', e.clientY + 'px');
        };
        window.addEventListener('mousemove', handleMouseMove);
        return () => window.removeEventListener('mousemove', handleMouseMove);
    }, []);

    return (
        <>
            <style>{`
                :root {
                    --x: 50%;
                    --y: 50%;
                }
                .gradient-bg {
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: black;
                    z-index: 1;
                }
                .aurora {
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    pointer-events: none;
                    background-image: radial-gradient(circle at var(--x) var(--y), rgba(6, 182, 212, 0.05), transparent 30vw);
                    z-index: 2;
                }
            `}</style>
            <div className="gradient-bg"></div>
            <div className="aurora"></div>
        </>
    );
};

export default BackgroundEffects; 