#!/usr/bin/env node

/**
 * Complete Database Setup Script for Torvie
 * Handles initial database setup, migrations, and data migration from JSON files
 */

import { logger } from '../utils/logger.js';
import { testConnection, healthCheck } from '../utils/database.js';
import { runMigrations } from './migrate.js';
import { migrateData } from './migrate-data.js';
import { createBackup } from './backup.js';

/**
 * Check if PostgreSQL tools are available
 */
async function checkPostgresTools() {
  const { spawn } = await import('child_process');
  
  return new Promise((resolve) => {
    const child = spawn('pg_dump', ['--version'], { stdio: 'pipe' });
    
    child.on('close', (code) => {
      resolve(code === 0);
    });
    
    child.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * Setup database with comprehensive initialization
 */
async function setupDatabase(options = {}) {
  const {
    skipMigration = false,
    skipDataMigration = false,
    createBackupFirst = false,
    force = false
  } = options;
  
  try {
    logger.info('🚀 Starting Torvie database setup...');
    
    // Step 1: Test database connection
    logger.info('📡 Testing database connection...');
    const connectionTest = await testConnection(3);
    
    if (!connectionTest.success) {
      logger.error('❌ Database connection failed:', connectionTest.error);
      logger.error('Please ensure:');
      logger.error('  1. PostgreSQL is running');
      logger.error('  2. DATABASE_URL environment variable is set correctly');
      logger.error('  3. Database exists and credentials are valid');
      throw new Error('Database connection failed');
    }
    
    logger.info('✅ Database connection successful');
    
    // Step 2: Check PostgreSQL tools availability
    logger.info('🔧 Checking PostgreSQL tools...');
    const hasPostgresTools = await checkPostgresTools();
    
    if (!hasPostgresTools) {
      logger.warn('⚠️ PostgreSQL client tools (pg_dump, pg_restore) not found');
      logger.warn('Backup functionality will not be available');
    } else {
      logger.info('✅ PostgreSQL tools available');
    }
    
    // Step 3: Create backup if requested and tools are available
    if (createBackupFirst && hasPostgresTools) {
      try {
        logger.info('💾 Creating pre-setup backup...');
        const backup = await createBackup({
          filename: `pre-setup-backup-${new Date().toISOString().slice(0, 10)}.dump`
        });
        logger.info(`✅ Backup created: ${backup.filename}`);
      } catch (error) {
        logger.warn('⚠️ Failed to create backup:', error.message);
        if (!force) {
          throw new Error('Backup creation failed. Use --force to continue anyway.');
        }
      }
    }
    
    // Step 4: Run database migrations
    if (!skipMigration) {
      logger.info('🔄 Running database migrations...');
      await runMigrations();
      logger.info('✅ Database migrations completed');
    } else {
      logger.info('⏭️ Skipping database migrations');
    }
    
    // Step 5: Migrate data from JSON files
    if (!skipDataMigration) {
      logger.info('📦 Migrating data from JSON files...');
      const migrationResults = await migrateData();
      
      const totalMigrated = Object.values(migrationResults).reduce(
        (sum, result) => sum + result.migrated, 0
      );
      
      if (totalMigrated > 0) {
        logger.info(`✅ Data migration completed: ${totalMigrated} records migrated`);
      } else {
        logger.info('ℹ️ No data to migrate from JSON files');
      }
    } else {
      logger.info('⏭️ Skipping data migration');
    }
    
    // Step 6: Final health check
    logger.info('🏥 Running final health check...');
    const health = await healthCheck();
    
    if (health.status === 'healthy') {
      logger.info('✅ Database health check passed');
      logger.info('📊 Database statistics:', {
        status: health.status,
        duration: `${health.duration}ms`,
        pool: health.pool,
        tests: Object.keys(health.tests || {}).length
      });
    } else {
      logger.warn('⚠️ Database health check failed:', health);
    }
    
    // Step 7: Success summary
    logger.info('🎉 Database setup completed successfully!');
    logger.info('📋 Next steps:');
    logger.info('  1. Start the server: npm run dev');
    logger.info('  2. Check health: curl http://localhost:3000/api/health');
    logger.info('  3. View database status: npm run migrate:status');
    
    if (hasPostgresTools) {
      logger.info('  4. Create backup: npm run backup:create');
    }
    
    return {
      success: true,
      connection: connectionTest,
      health,
      hasPostgresTools
    };
    
  } catch (error) {
    logger.error('❌ Database setup failed:', error);
    logger.error('💡 Troubleshooting tips:');
    logger.error('  1. Check DATABASE_URL environment variable');
    logger.error('  2. Ensure PostgreSQL is running and accessible');
    logger.error('  3. Verify database exists and user has proper permissions');
    logger.error('  4. Check server logs for detailed error information');
    
    throw error;
  }
}

/**
 * Reset database (dangerous operation)
 */
async function resetDatabase(options = {}) {
  const { confirm = false } = options;
  
  if (!confirm) {
    logger.error('❌ Database reset requires confirmation');
    logger.error('This will DELETE ALL DATA in the database!');
    logger.error('Use: npm run db:reset -- --confirm');
    return;
  }
  
  try {
    logger.warn('⚠️ DANGER: Resetting database - ALL DATA WILL BE LOST!');
    
    // Create backup first
    const hasPostgresTools = await checkPostgresTools();
    if (hasPostgresTools) {
      logger.info('💾 Creating backup before reset...');
      const backup = await createBackup({
        filename: `pre-reset-backup-${new Date().toISOString().slice(0, 10)}.dump`
      });
      logger.info(`✅ Backup created: ${backup.filename}`);
    }
    
    // Drop all tables
    const { query } = await import('../utils/database.js');
    
    logger.info('🗑️ Dropping all tables...');
    await query(`
      DROP SCHEMA public CASCADE;
      CREATE SCHEMA public;
      GRANT ALL ON SCHEMA public TO public;
    `);
    
    logger.info('✅ Database reset completed');
    
    // Run setup again
    await setupDatabase();
    
  } catch (error) {
    logger.error('❌ Database reset failed:', error);
    throw error;
  }
}

/**
 * Main CLI function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  // Parse flags
  const options = {
    skipMigration: args.includes('--skip-migration'),
    skipDataMigration: args.includes('--skip-data-migration'),
    createBackupFirst: args.includes('--backup-first'),
    force: args.includes('--force'),
    confirm: args.includes('--confirm')
  };
  
  try {
    switch (command) {
      case 'setup':
      case undefined:
        await setupDatabase(options);
        break;
        
      case 'reset':
        await resetDatabase(options);
        break;
        
      case 'health':
        const health = await healthCheck();
        console.log('Database Health:', JSON.stringify(health, null, 2));
        break;
        
      default:
        console.log('Torvie Database Setup');
        console.log('====================');
        console.log('');
        console.log('Commands:');
        console.log('  setup (default)  - Complete database setup');
        console.log('  reset           - Reset database (DANGEROUS!)');
        console.log('  health          - Check database health');
        console.log('');
        console.log('Options:');
        console.log('  --skip-migration      - Skip running migrations');
        console.log('  --skip-data-migration - Skip migrating JSON data');
        console.log('  --backup-first        - Create backup before setup');
        console.log('  --force              - Continue on non-critical errors');
        console.log('  --confirm            - Confirm dangerous operations');
        console.log('');
        console.log('Examples:');
        console.log('  npm run db:setup');
        console.log('  npm run db:setup -- --backup-first');
        console.log('  npm run db:reset -- --confirm');
        process.exit(1);
    }
    
    process.exit(0);
    
  } catch (error) {
    logger.error('Setup script failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { setupDatabase, resetDatabase };
