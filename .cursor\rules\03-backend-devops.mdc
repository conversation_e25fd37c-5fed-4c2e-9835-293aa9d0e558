---
description: "Standards for backend services, databases, and infrastructure (Go, Rust, Postgres, Docker, K8s, Terraform)."
globs:
  - "**/*.go"
  - "**/*.rs"
  - "**/*.py"
  - "**/Dockerfile"
  - "**/docker-compose.yml"
  - "**/*.tf"
  - "**/*.hcl"
alwaysApply: false
---

# Backend & DevOps Standards

## A. Technology Canon
* **Primary Languages:** Go (performance/concurrency), Rust (safety/ultimate performance).
* **Database:** Default to PostgreSQL. Use Redis for caching/queuing.
* **APIs:** REST with a strict OpenAPI 3.0 specification, or GraphQL for complex data needs.
* **Event-Driven:** Kafka or a managed cloud equivalent.

## B. Infrastructure & DevOps
* **Containerization:** All applications MUST be containerized using Docker. Provide a minimal, secure, multi-stage Dockerfile.
* **Orchestration:** Kubernetes (K8s) is the standard for running applications at scale.
* **Infrastructure as Code (IaC):** Terraform is non-negotiable for defining cloud resources.
* **CI/CD:** Use GitHub Actions to automate everything from linting to deployment.