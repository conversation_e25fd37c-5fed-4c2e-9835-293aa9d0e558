﻿import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { useAuth } from './AuthContext';
import { localAppStorage } from '../utils/localStorage';

const ProfileContext = createContext();

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (!context) throw new Error('useProfile must be used within a ProfileProvider');
  return context;
};

// Browser localStorage backup for profile persistence
const PROFILE_BACKUP_KEY = 'torvie_current_profile_backup';

const saveProfileBackup = (userId, profile) => {
  try {
    const backup = { userId, profile, timestamp: Date.now() };
    localStorage.setItem(PROFILE_BACKUP_KEY, JSON.stringify(backup));
    console.log(` ProfileContext: Saved profile backup for user ${userId}: "${profile?.name}"`);
  } catch (error) {
    console.warn(' ProfileContext: Failed to save profile backup:', error);
  }
};

const loadProfileBackup = (userId) => {
  try {
    const backup = localStorage.getItem(PROFILE_BACKUP_KEY);
    if (backup) {
      const parsed = JSON.parse(backup);
      if (parsed.userId === userId) {
        console.log(` ProfileContext: Loaded profile backup for user ${userId}: "${parsed.profile?.name}"`);
        return parsed.profile;
      }
    }
  } catch (error) {
    console.warn(' ProfileContext: Failed to load profile backup:', error);
  }
  return null;
};

export const ProfileProvider = ({ children }) => {
  const { user } = useAuth();
  const [profile, setProfile] = useState(null);
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isProfileLoaded, setIsProfileLoaded] = useState(false);
  const initializationRef = useRef(null);
  const clearTimeoutRef = useRef(null);
  const manualSaveInProgress = useRef(false);

  // Load profiles from local app storage when user is authenticated
  useEffect(() => {
    console.log(' ProfileContext: useEffect triggered, user:', user);
    
    if (!user) {
      // Only clear if we actually had a user before (not just initial mount)
      if (initializationRef.current !== null) {
        console.log(' ProfileContext: User logged out, clearing profile state. Previous user ID:', initializationRef.current);
        setProfile(null);
        setProfiles([]);
        setIsProfileLoaded(true);
        initializationRef.current = null;
      } else {
        console.log(' ProfileContext: No user on mount, setting loaded state');
        setIsProfileLoaded(true);
      }
      return;
    }

    // Don't re-initialize if we already have profiles for this user
    if (initializationRef.current === user.id && profiles.length > 0) {
      console.log(' ProfileContext: Already initialized for user:', user.id, 'with', profiles.length, 'profiles');
      return;
    }

    const initializeProfiles = async () => {
      console.log(' ProfileContext: Initializing profiles for user:', user.username, 'ID:', user.id);
      setLoading(true);
      
      try {
        // Check if backend is available
        const isAvailable = await localAppStorage.isAvailable();
        if (!isAvailable) {
          console.error(' ProfileContext: Backend storage not available, using backup profile');
          const backupProfile = loadProfileBackup(user.id);
          if (backupProfile) {
            setProfile(backupProfile);
          }
          setIsProfileLoaded(true);
          setLoading(false);
          return;
        }
        
        // Load profiles from local app storage
        let profilesArr = await localAppStorage.getProfiles(user.id);
        
        if (profilesArr && Array.isArray(profilesArr) && profilesArr.length === 6) {
          console.log(' ProfileContext: Found valid stored profiles:', profilesArr.filter(p => p.active).map(p => p.name));
        } else {
          console.log(' ProfileContext: No valid stored profiles found, creating fresh ones...');
          profilesArr = [
            { id: 1, name: user.displayName || user.username, avatar: '/avatars/avatar1.png', active: true },
            { id: 2, name: '', avatar: '/avatars/avatar2.png', active: false },
            { id: 3, name: '', avatar: '/avatars/avatar3.png', active: false },
            { id: 4, name: '', avatar: '/avatars/avatar4.png', active: false },
            { id: 5, name: '', avatar: '/avatars/avatar1.png', active: false },
            { id: 6, name: '', avatar: '/avatars/avatar2.png', active: false },
          ];
          
          // Save new profiles to local app storage
          const saveSuccess = await localAppStorage.saveProfiles(user.id, profilesArr);
          console.log(' ProfileContext: Initial profiles save result:', saveSuccess);
          
          // Set the first (main) profile as current
          const currentProfileSuccess = await localAppStorage.saveCurrentProfile(user.id, profilesArr[0]);
          console.log(' ProfileContext: Current profile save result:', currentProfileSuccess);
          setProfile(profilesArr[0]);
          saveProfileBackup(user.id, profilesArr[0]);
        }
        
        if (profilesArr && profilesArr.length > 0) {
          // IMPROVED: Try browser backup FIRST for instant restoration
          console.log(' ProfileContext: Trying browser backup first for instant restoration...');
          let currentProfile = loadProfileBackup(user.id);
          
          if (currentProfile) {
            console.log(` ProfileContext: Fast-restored from backup: "${currentProfile.name}" (ID: ${currentProfile.id})`);
            // Verify the profile still exists in the profiles array
            const matchingProfile = profilesArr.find(p => p.id === currentProfile.id);
            if (matchingProfile) {
              // Set profile immediately from backup for instant UI update
              setProfile(matchingProfile);
              saveProfileBackup(user.id, matchingProfile);
              console.log(` ProfileContext: Profile instantly restored: "${matchingProfile.name}"`);
            } else {
              console.warn(` ProfileContext: Backup profile ID ${currentProfile.id} no longer exists`);
              currentProfile = null;
            }
          }
          
          // Then try API to sync/verify (but don\'t override backup unless necessary)
          if (!currentProfile) {
            console.log(' ProfileContext: No backup found, trying API...');
            try {
              const apiProfile = await localAppStorage.getCurrentProfile(user.id);
              if (apiProfile) {
                currentProfile = apiProfile;
                console.log(` ProfileContext: Loaded from API: "${currentProfile.name}"`);
              }
            } catch (error) {
              console.warn(` ProfileContext: API call failed:`, error);
            }
          }
          
          if (currentProfile && currentProfile.id) {
            // Find the profile in the profiles array to ensure it still exists
            const matchingProfile = profilesArr.find(p => p.id === currentProfile.id);
            if (matchingProfile) {
              console.log(` ProfileContext: Successfully loaded existing profile: "${matchingProfile.name}" (ID: ${matchingProfile.id})`);
              // Only set profile if we haven\'t already set it from backup
              if (!profile || profile.id !== matchingProfile.id) {
                setProfile(matchingProfile);
              }
              // Update backup with latest data
              saveProfileBackup(user.id, matchingProfile);
            } else {
              console.log(` ProfileContext: Saved profile ID ${currentProfile.id} no longer exists, using first active`);
              const firstActive = profilesArr.find(p => p.active);
              if (firstActive) {
                setProfile(firstActive);
                await localAppStorage.saveCurrentProfile(user.id, firstActive);
                saveProfileBackup(user.id, firstActive);
              }
            }
          } else {
            console.log(' ProfileContext: No valid current profile found, using first active profile');
            // If current profile is missing and we haven\'t set one from backup, use first active profile
            if (!profile) {
              const firstActive = profilesArr.find(p => p.active);
              if (firstActive) {
                console.log(' ProfileContext: Setting to first active:', firstActive.name);
                setProfile(firstActive);
                await localAppStorage.saveCurrentProfile(user.id, firstActive);
                saveProfileBackup(user.id, firstActive);
              }
            }
          }
          
          setProfiles(profilesArr);
          initializationRef.current = user.id;
          console.log(' ProfileContext: Initialization complete. Active profiles:', profilesArr.filter(p => p.active).length);
        }
        
      } catch (error) {
        console.error(' ProfileContext: Error initializing profiles:', error);
        // Try to use backup profile if everything fails
        const backupProfile = loadProfileBackup(user.id);
        if (backupProfile) {
          console.log(` ProfileContext: Using backup profile due to error: "${backupProfile.name}"`);
          setProfile(backupProfile);
        }
      } finally {
        console.log(' ProfileContext: Setting isProfileLoaded to true');
        setIsProfileLoaded(true);
        setLoading(false);
      }
    };
    
    initializeProfiles();
  }, [user]);

  // Save selected profile to local app storage
  useEffect(() => {
    if (!user) {
      console.log(' ProfileContext: No user, skipping profile save');
      return;
    }
    
    if (profile && profile.id) {
      console.log(` ProfileContext: Saving current profile: "${profile.name}" (ID: ${profile.id})`);
      // Save to both API and backup
      localAppStorage.saveCurrentProfile(user.id, profile).then(success => {
        if (success) {
          console.log(` ProfileContext: Successfully saved current profile: "${profile.name}"`);
        } else {
          console.error(` ProfileContext: Failed to save current profile: "${profile.name}"`);
        }
      });
      // Always save backup regardless of API success
      saveProfileBackup(user.id, profile);
    } else if (profile === null) {
      console.log(' ProfileContext: Profile explicitly set to null');
      localAppStorage.saveCurrentProfile(user.id, null);
    } else {
      console.log(' ProfileContext: Profile state unclear, not modifying storage. Profile:', profile);
    }
  }, [profile, user]);

  // Save profiles to local app storage when they change (automatic save)
  useEffect(() => {
    if (!user) {
      console.log(' ProfileContext: Skipping auto-save - no user');
      return;
    }
    
    if (!profiles.length) {
      console.log(' ProfileContext: Skipping auto-save - empty profiles array (likely during initialization)');
      return;
    }
    
    // Skip automatic save if manual save is in progress
    if (manualSaveInProgress.current) {
      console.log(' ProfileContext: Skipping auto-save - manual save in progress');
      return;
    }
    
    // Additional check: ensure we have valid profile data
    const hasValidProfiles = profiles.some(p => p && p.id && typeof p.active === 'boolean');
    if (!hasValidProfiles) {
      console.log(' ProfileContext: Skipping auto-save - profiles array contains invalid data');
      return;
    }
    
    const saveProfiles = async () => {
      console.log(' ProfileContext: Auto-saving profiles:', {
        profileCount: profiles.length,
        activeProfiles: profiles.filter(p => p.active).map(p => p.name),
        userId: user.id
      });
      
      // Use the local app storage system
      const saveSuccess = await localAppStorage.saveProfiles(user.id, profiles);
      
      if (saveSuccess) {
        console.log(' ProfileContext: Auto-save successful');
      } else {
        console.error(' ProfileContext: Auto-save failed');
      }
    };
    
    // Debounce the auto-save to prevent rapid consecutive saves
    const timeoutId = setTimeout(saveProfiles, 500);
    return () => clearTimeout(timeoutId);
  }, [profiles, user]);

  // Manual save functions for Profiles.jsx to use
  const saveProfilesManually = async (updatedProfiles, currentProfile = null) => {
    if (!user) return false;
    
    console.log(' ProfileContext: Manual save requested');
    manualSaveInProgress.current = true;
    
    try {
      const profilesSaveSuccess = await localAppStorage.saveProfiles(user.id, updatedProfiles);
      let currentProfileSaveSuccess = true;
      
      if (currentProfile !== null) {
        currentProfileSaveSuccess = await localAppStorage.saveCurrentProfile(user.id, currentProfile);
        // Also save backup
        saveProfileBackup(user.id, currentProfile);
      }
      
      if (profilesSaveSuccess && currentProfileSaveSuccess) {
        console.log(' ProfileContext: Manual save successful');
        return true;
      } else {
        console.error(' ProfileContext: Manual save failed');
        return false;
      }
    } catch (error) {
      console.error(' ProfileContext: Manual save error:', error);
      return false;
    } finally {
      // Clear the flag after a short delay to allow React state updates to complete
      setTimeout(() => {
        manualSaveInProgress.current = false;
      }, 1000);
    }
  };

  const value = {
    profile,
    setProfile,
    profiles,
    setProfiles,
    loading,
    setLoading,
    isProfileLoaded,
    saveProfilesManually,
  };
  
  // Debug the context values being provided
  useEffect(() => {
    console.log(' ProfileContext: Context value changed:', {
      profilesCount: profiles?.length || 0,
      activeProfilesCount: profiles?.filter(p => p.active)?.length || 0,
      currentProfile: profile ? `${profile.name} (ID: ${profile.id})` : 'none',
      isProfileLoaded
    });
  }, [profiles, profile, isProfileLoaded]);

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};
