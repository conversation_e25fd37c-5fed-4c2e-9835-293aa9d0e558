import axios from 'axios';

async function searchPirateBay(query, type = 'general') {
  try {
    // The Pirate Bay search API
    const searchUrl = `https://apibay.org/q.php?q=${encodeURIComponent(query)}&cat=`;
    
    // Add category filter based on type
    let category = '';
    if (type === 'movie') {
      category = '200'; // Movies category
    } else if (type === 'tv') {
      category = '500'; // TV Shows category
    }
    
    const url = category ? `${searchUrl}${category}` : searchUrl;
    
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.data || !Array.isArray(response.data)) {
      console.log('No results from The Pirate Bay');
      return [];
    }

    const torrents = response.data
      .filter(torrent => torrent.name && torrent.info_hash && torrent.seeders > 0)
      .map(torrent => ({
        title: torrent.name,
        magnetLink: `magnet:?xt=urn:btih:${torrent.info_hash}&dn=${encodeURIComponent(torrent.name)}&tr=udp://tracker.opentrackr.org:1337/announce&tr=udp://tracker.openbittorrent.com:80`,
        seeders: parseInt(torrent.seeders) || 0,
        leechers: parseInt(torrent.leechers) || 0,
        size: formatBytes(parseInt(torrent.size) || 0),
        source: 'The Pirate Bay'
      }))
      .sort((a, b) => b.seeders - a.seeders)
      .slice(0, 10); // Take top 10 by seeders

    console.log(`Found ${torrents.length} torrents from The Pirate Bay`);
    return torrents;
    
  } catch (error) {
    console.error('Error searching The Pirate Bay:', error.message);
    return [];
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export { searchPirateBay }; 