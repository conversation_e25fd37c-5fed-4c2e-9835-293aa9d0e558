---
description: "A protocol for making small, safe, opportunistic improvements to any code being worked on, such as improving clarity, adding comments, or minor refactoring."
globs:
  - "**/*.js"
  - "**/*.ts"
  - "**/*.tsx"
  - "**/*.py"
  - "**/*.go"
  - "**/*.rs"
alwaysApply: false
---

# Opportunistic Refactoring Protocol ("The Boy Scout Rule")

When you are asked to modify an existing file, you must not limit your scope solely to the requested change. You will also act as a steward of the code you are touching.

**1. Analyze Surrounding Code:**
Before implementing your primary task, briefly analyze the function, class, or module you are working within. Look for "code smells" or areas of low clarity.

**2. Identify Safe, Minor Improvements:**
You are authorized to make the following types of safe, opportunistic improvements alongside your primary task:
* **Improve Clarity:** Rename variables or functions to be more descriptive (e.g., rename `d` to `elapsedTimeInDays`).
* **Add Explanatory Comments:** If you encounter a complex or non-obvious line of logic, add a brief comment explaining the "why."
* **Minor Refactoring:** Perform small, risk-free refactors, such as extracting a small piece of repeated logic into a private helper function within the same file.

**3. Announce Your Actions:**
When you deliver the final code, you must briefly state the opportunistic improvements you made.
* **Example:** "I have completed the feature request. As part of my work in `UserService.ts`, I also renamed several variables for clarity and added comments to the complex permission-checking logic."

**4. Boundary Condition:**
You are **NOT** authorized to perform large-scale refactoring or architectural changes without explicit instruction. This protocol is for minor, "no-brainer" improvements that increase long-term code health.