import pg from 'pg';
import { logger, dbLogger } from './logger.js';

const { Pool } = pg;

// Enhanced database configuration with environment-specific settings
const getDbConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isTest = process.env.NODE_ENV === 'test';

  const baseConfig = {
    connectionString: process.env.DATABASE_URL || 'postgresql://torvie:torvie_password@localhost:5432/torvie',
    max: parseInt(process.env.DB_POOL_MAX) || (isProduction ? 30 : 20), // Maximum number of clients in the pool
    min: parseInt(process.env.DB_POOL_MIN) || (isProduction ? 5 : 2), // Minimum number of clients in the pool
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000, // Close idle clients after 30 seconds
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || (isProduction ? 5000 : 2000),
    acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000, // Maximum time to wait for a connection
    createTimeoutMillis: parseInt(process.env.DB_CREATE_TIMEOUT) || 30000, // Maximum time to wait for connection creation
    createRetryIntervalMillis: parseInt(process.env.DB_RETRY_INTERVAL) || 200, // Retry interval for connection creation
    reapIntervalMillis: parseInt(process.env.DB_REAP_INTERVAL) || 1000, // How often to check for idle connections
    ssl: isProduction ? { rejectUnauthorized: false } : false,

    // Query timeout
    query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || (isProduction ? 30000 : 10000),

    // Statement timeout (PostgreSQL specific)
    statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT) || (isProduction ? 60000 : 30000),

    // Application name for connection tracking
    application_name: `torvie-${process.env.NODE_ENV || 'development'}-${process.pid}`,
  };

  // Test environment specific settings
  if (isTest) {
    baseConfig.max = 5;
    baseConfig.min = 1;
    baseConfig.idleTimeoutMillis = 1000;
    baseConfig.connectionTimeoutMillis = 1000;
  }

  return baseConfig;
};

// Create connection pool with retry logic
let pool;
let connectionRetries = 0;
const maxRetries = parseInt(process.env.DB_MAX_RETRIES) || 5;

const createPool = () => {
  const config = getDbConfig();
  pool = new Pool(config);

  // Enhanced error handling
  pool.on('error', (err, client) => {
    dbLogger.error({
      event: 'pool_error',
      error: err.message,
      stack: err.stack,
      client_id: client?.processID
    });

    // Attempt to recreate pool on critical errors
    if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
      setTimeout(() => {
        if (connectionRetries < maxRetries) {
          connectionRetries++;
          logger.warn(`Attempting to recreate database pool (attempt ${connectionRetries}/${maxRetries})`);
          createPool();
        } else {
          logger.error('Max database connection retries exceeded');
        }
      }, 5000 * connectionRetries); // Exponential backoff
    }
  });

  pool.on('connect', (client) => {
    connectionRetries = 0; // Reset retry counter on successful connection
    dbLogger.debug({
      event: 'client_connected',
      client_id: client.processID,
      total_count: pool.totalCount,
      idle_count: pool.idleCount,
      waiting_count: pool.waitingCount
    });
  });

  pool.on('acquire', (client) => {
    dbLogger.debug({
      event: 'client_acquired',
      client_id: client.processID,
      total_count: pool.totalCount,
      idle_count: pool.idleCount,
      waiting_count: pool.waitingCount
    });
  });

  pool.on('remove', (client) => {
    dbLogger.debug({
      event: 'client_removed',
      client_id: client.processID,
      total_count: pool.totalCount,
      idle_count: pool.idleCount,
      waiting_count: pool.waitingCount
    });
  });

  return pool;
};

// Initialize pool
createPool();

// Enhanced connection testing with health checks
export const testConnection = async (retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const client = await pool.connect();
      const start = Date.now();

      // Test basic connectivity
      const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
      const duration = Date.now() - start;

      client.release();

      logger.info({
        event: 'connection_test_success',
        attempt,
        duration,
        pg_version: result.rows[0].pg_version.split(' ')[0],
        pool_stats: {
          total: pool.totalCount,
          idle: pool.idleCount,
          waiting: pool.waitingCount
        }
      });

      return { success: true, duration, attempt };
    } catch (error) {
      dbLogger.error({
        event: 'connection_test_failed',
        attempt,
        error: error.message,
        code: error.code
      });

      if (attempt === retries) {
        return { success: false, error: error.message, attempts: retries };
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

// Database health check
export const healthCheck = async () => {
  const start = Date.now();

  try {
    const client = await pool.connect();

    // Test queries
    const queries = [
      { name: 'basic_select', query: 'SELECT 1 as test' },
      { name: 'table_check', query: "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'public'" },
      { name: 'active_connections', query: 'SELECT COUNT(*) as active_connections FROM pg_stat_activity WHERE state = \'active\'' }
    ];

    const results = {};

    for (const { name, query } of queries) {
      const queryStart = Date.now();
      const result = await client.query(query);
      results[name] = {
        duration: Date.now() - queryStart,
        result: result.rows[0]
      };
    }

    client.release();

    const totalDuration = Date.now() - start;

    return {
      status: 'healthy',
      duration: totalDuration,
      pool: {
        total: pool.totalCount,
        idle: pool.idleCount,
        waiting: pool.waitingCount
      },
      tests: results,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      code: error.code,
      duration: Date.now() - start,
      timestamp: new Date().toISOString()
    };
  }
};

// Enhanced query execution with retry logic and monitoring
export const query = async (text, params = [], options = {}) => {
  const {
    retries = 2,
    timeout = getDbConfig().query_timeout,
    logQuery = process.env.NODE_ENV !== 'production'
  } = options;

  const start = Date.now();
  let lastError;

  for (let attempt = 1; attempt <= retries + 1; attempt++) {
    let client;

    try {
      client = await pool.connect();

      // Set query timeout if specified
      if (timeout) {
        await client.query(`SET statement_timeout = ${timeout}`);
      }

      const result = await client.query(text, params);
      const duration = Date.now() - start;

      if (logQuery || duration > 1000) { // Always log slow queries
        dbLogger.debug({
          event: 'query_executed',
          query: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
          params: params.length > 0 ? params : undefined,
          duration,
          rowCount: result.rowCount,
          attempt: attempt > 1 ? attempt : undefined
        });
      }

      client.release();
      return result;

    } catch (error) {
      if (client) client.release();

      lastError = error;
      const duration = Date.now() - start;

      // Check if error is retryable
      const isRetryable = (
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.code === '53300' || // too_many_connections
        error.code === '57P01' || // admin_shutdown
        error.message.includes('connection terminated')
      );

      if (attempt <= retries && isRetryable) {
        dbLogger.warn({
          event: 'query_retry',
          query: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
          attempt,
          error: error.message,
          code: error.code,
          duration
        });

        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
        continue;
      }

      // Log final error
      dbLogger.error({
        event: 'query_failed',
        query: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
        params: params.length > 0 ? params : undefined,
        duration,
        error: error.message,
        code: error.code,
        stack: error.stack,
        attempts: attempt
      });

      throw error;
    }
  }

  throw lastError;
};

// Enhanced transaction helper with monitoring and retry logic
export const transaction = async (callback, options = {}) => {
  const {
    isolationLevel = 'READ COMMITTED',
    timeout = getDbConfig().statement_timeout,
    retries = 1
  } = options;

  let lastError;

  for (let attempt = 1; attempt <= retries + 1; attempt++) {
    const client = await pool.connect();
    const start = Date.now();

    try {
      // Set transaction isolation level and timeout
      await client.query('BEGIN');

      if (isolationLevel !== 'READ COMMITTED') {
        await client.query(`SET TRANSACTION ISOLATION LEVEL ${isolationLevel}`);
      }

      if (timeout) {
        await client.query(`SET statement_timeout = ${timeout}`);
      }

      const result = await callback(client);
      await client.query('COMMIT');

      const duration = Date.now() - start;

      dbLogger.debug({
        event: 'transaction_completed',
        duration,
        isolationLevel,
        attempt: attempt > 1 ? attempt : undefined
      });

      client.release();
      return result;

    } catch (error) {
      try {
        await client.query('ROLLBACK');
      } catch (rollbackError) {
        dbLogger.error({
          event: 'rollback_failed',
          error: rollbackError.message
        });
      }

      client.release();
      lastError = error;

      const duration = Date.now() - start;

      // Check if error is retryable (serialization failures, deadlocks)
      const isRetryable = (
        error.code === '40001' || // serialization_failure
        error.code === '40P01' || // deadlock_detected
        error.code === '25P02'    // in_failed_sql_transaction
      );

      if (attempt <= retries && isRetryable) {
        dbLogger.warn({
          event: 'transaction_retry',
          attempt,
          error: error.message,
          code: error.code,
          duration
        });

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
        continue;
      }

      dbLogger.error({
        event: 'transaction_failed',
        error: error.message,
        code: error.code,
        duration,
        attempts: attempt
      });

      throw error;
    }
  }

  throw lastError;
};

// User management functions
export const createUser = async (userData) => {
  const { username, email, password_hash, display_name } = userData;
  
  const result = await query(
    `INSERT INTO users (username, email, password_hash, display_name) 
     VALUES ($1, $2, $3, $4) 
     RETURNING id, username, email, display_name, created_at`,
    [username, email, password_hash, display_name]
  );
  
  return result.rows[0];
};

export const getUserByUsername = async (username) => {
  const result = await query(
    'SELECT * FROM users WHERE username = $1',
    [username]
  );
  
  return result.rows[0] || null;
};

export const getUserById = async (id) => {
  const result = await query(
    'SELECT id, username, email, display_name, created_at FROM users WHERE id = $1',
    [id]
  );
  
  return result.rows[0] || null;
};

// Profile management functions
export const createProfile = async (profileData) => {
  const { user_id, name, avatar_url, is_kids } = profileData;
  
  const result = await query(
    `INSERT INTO profiles (user_id, name, avatar_url, is_kids) 
     VALUES ($1, $2, $3, $4) 
     RETURNING *`,
    [user_id, name, avatar_url, is_kids]
  );
  
  return result.rows[0];
};

export const getProfilesByUserId = async (userId) => {
  const result = await query(
    'SELECT * FROM profiles WHERE user_id = $1 ORDER BY created_at',
    [userId]
  );
  
  return result.rows;
};

export const getProfileById = async (profileId, userId) => {
  const result = await query(
    'SELECT * FROM profiles WHERE id = $1 AND user_id = $2',
    [profileId, userId]
  );
  
  return result.rows[0] || null;
};

// Watchlist management functions
export const addToWatchlist = async (watchlistData) => {
  const { user_id, profile_id, media_id, media_type, title, poster_path, vote_average, release_date } = watchlistData;
  
  const result = await query(
    `INSERT INTO watchlist (user_id, profile_id, media_id, media_type, title, poster_path, vote_average, release_date) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
     ON CONFLICT (profile_id, media_id, media_type) DO NOTHING
     RETURNING *`,
    [user_id, profile_id, media_id, media_type, title, poster_path, vote_average, release_date]
  );
  
  return result.rows[0];
};

export const removeFromWatchlist = async (profileId, mediaId, mediaType) => {
  const result = await query(
    'DELETE FROM watchlist WHERE profile_id = $1 AND media_id = $2 AND media_type = $3 RETURNING *',
    [profileId, mediaId, mediaType]
  );
  
  return result.rows[0];
};

export const getWatchlist = async (profileId) => {
  const result = await query(
    'SELECT * FROM watchlist WHERE profile_id = $1 ORDER BY added_at DESC',
    [profileId]
  );
  
  return result.rows;
};

export const isInWatchlist = async (profileId, mediaId, mediaType) => {
  const result = await query(
    'SELECT COUNT(*) as count FROM watchlist WHERE profile_id = $1 AND media_id = $2 AND media_type = $3',
    [profileId, mediaId, mediaType]
  );
  
  return parseInt(result.rows[0].count) > 0;
};

// Watch progress functions
export const saveWatchProgress = async (progressData) => {
  const { 
    user_id, profile_id, media_id, media_type, progress_seconds, 
    duration_seconds, completed, season_number, episode_number 
  } = progressData;
  
  const result = await query(
    `INSERT INTO watch_progress (user_id, profile_id, media_id, media_type, progress_seconds, duration_seconds, completed, season_number, episode_number) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
     ON CONFLICT (profile_id, media_id, media_type, season_number, episode_number) 
     DO UPDATE SET 
       progress_seconds = EXCLUDED.progress_seconds,
       duration_seconds = EXCLUDED.duration_seconds,
       completed = EXCLUDED.completed,
       last_watched = CURRENT_TIMESTAMP
     RETURNING *`,
    [user_id, profile_id, media_id, media_type, progress_seconds, duration_seconds, completed, season_number, episode_number]
  );
  
  return result.rows[0];
};

export const getWatchProgress = async (profileId, mediaId, mediaType, seasonNumber = null, episodeNumber = null) => {
  let queryText = 'SELECT * FROM watch_progress WHERE profile_id = $1 AND media_id = $2 AND media_type = $3';
  let params = [profileId, mediaId, mediaType];
  
  if (seasonNumber !== null) {
    queryText += ' AND season_number = $4';
    params.push(seasonNumber);
  }
  
  if (episodeNumber !== null) {
    queryText += ' AND episode_number = $5';
    params.push(episodeNumber);
  }
  
  const result = await query(queryText, params);
  return result.rows[0] || null;
};

// Search history functions
export const saveSearchHistory = async (userId, profileId, query) => {
  await query(
    'INSERT INTO search_history (user_id, profile_id, query) VALUES ($1, $2, $3)',
    [userId, profileId, query]
  );
};

export const getSearchHistory = async (profileId, limit = 10) => {
  const result = await query(
    'SELECT * FROM search_history WHERE profile_id = $1 ORDER BY searched_at DESC LIMIT $2',
    [profileId, limit]
  );
  
  return result.rows;
};

// Viewing history functions
export const saveViewingHistory = async (historyData) => {
  const { user_id, profile_id, media_id, media_type, rating } = historyData;
  
  const result = await query(
    `INSERT INTO viewing_history (user_id, profile_id, media_id, media_type, rating) 
     VALUES ($1, $2, $3, $4, $5) 
     RETURNING *`,
    [user_id, profile_id, media_id, media_type, rating]
  );
  
  return result.rows[0];
};

export const getViewingHistory = async (profileId, limit = 20) => {
  const result = await query(
    'SELECT * FROM viewing_history WHERE profile_id = $1 ORDER BY watched_at DESC LIMIT $2',
    [profileId, limit]
  );
  
  return result.rows;
};

// Cleanup function for expired data
export const cleanupExpiredData = async () => {
  try {
    // Clean up old search history (keep last 100 per profile)
    await query(`
      DELETE FROM search_history 
      WHERE id NOT IN (
        SELECT id FROM search_history 
        WHERE profile_id = profile_id 
        ORDER BY searched_at DESC 
        LIMIT 100
      )
    `);
    
    // Clean up old viewing history (keep last 500 per profile)
    await query(`
      DELETE FROM viewing_history 
      WHERE id NOT IN (
        SELECT id FROM viewing_history 
        WHERE profile_id = profile_id 
        ORDER BY watched_at DESC 
        LIMIT 500
      )
    `);
    
    logger.info('Database cleanup completed');
  } catch (error) {
    logger.error({ 
      event: 'cleanup_failed',
      error: error.message 
    });
  }
};

// Graceful shutdown
export const closePool = async () => {
  await pool.end();
  logger.info('Database pool closed');
};

export default {
  testConnection,
  query,
  transaction,
  createUser,
  getUserByUsername,
  getUserById,
  createProfile,
  getProfilesByUserId,
  getProfileById,
  addToWatchlist,
  removeFromWatchlist,
  getWatchlist,
  isInWatchlist,
  saveWatchProgress,
  getWatchProgress,
  saveSearchHistory,
  getSearchHistory,
  saveViewingHistory,
  getViewingHistory,
  cleanupExpiredData,
  closePool
}; 