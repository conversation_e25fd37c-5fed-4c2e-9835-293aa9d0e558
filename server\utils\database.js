import pg from 'pg';
import { logger, dbLogger } from './logger.js';

const { Pool } = pg;

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  ssl: false // Force SSL off for local/dev
};

// Create connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err) => {
  dbLogger.error({ 
    event: 'pool_error',
    error: err.message,
    stack: err.stack 
  });
});

// Test database connection
export const testConnection = async () => {
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    dbLogger.error({ 
      event: 'connection_failed',
      error: error.message 
    });
    return false;
  }
};

// Execute query with error handling
export const query = async (text, params = []) => {
  const start = Date.now();
  
  try {
    const client = await pool.connect();
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    dbLogger.debug({
      event: 'query_executed',
      query: text,
      duration,
      rowCount: result.rowCount
    });
    
    client.release();
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    
    dbLogger.error({
      event: 'query_failed',
      query: text,
      params,
      duration,
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
};

// Transaction helper
export const transaction = async (callback) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// User management functions
export const createUser = async (userData) => {
  const { username, email, password_hash, display_name } = userData;
  
  const result = await query(
    `INSERT INTO users (username, email, password_hash, display_name) 
     VALUES ($1, $2, $3, $4) 
     RETURNING id, username, email, display_name, created_at`,
    [username, email, password_hash, display_name]
  );
  
  return result.rows[0];
};

export const getUserByUsername = async (username) => {
  const result = await query(
    'SELECT * FROM users WHERE username = $1',
    [username]
  );
  
  return result.rows[0] || null;
};

export const getUserById = async (id) => {
  const result = await query(
    'SELECT id, username, email, display_name, created_at FROM users WHERE id = $1',
    [id]
  );
  
  return result.rows[0] || null;
};

// Profile management functions
export const createProfile = async (profileData) => {
  const { user_id, name, avatar_url, is_kids } = profileData;
  
  const result = await query(
    `INSERT INTO profiles (user_id, name, avatar_url, is_kids) 
     VALUES ($1, $2, $3, $4) 
     RETURNING *`,
    [user_id, name, avatar_url, is_kids]
  );
  
  return result.rows[0];
};

export const getProfilesByUserId = async (userId) => {
  const result = await query(
    'SELECT * FROM profiles WHERE user_id = $1 ORDER BY created_at',
    [userId]
  );
  
  return result.rows;
};

export const getProfileById = async (profileId, userId) => {
  const result = await query(
    'SELECT * FROM profiles WHERE id = $1 AND user_id = $2',
    [profileId, userId]
  );
  
  return result.rows[0] || null;
};

// Watchlist management functions
export const addToWatchlist = async (watchlistData) => {
  const { user_id, profile_id, media_id, media_type, title, poster_path, vote_average, release_date } = watchlistData;
  
  const result = await query(
    `INSERT INTO watchlist (user_id, profile_id, media_id, media_type, title, poster_path, vote_average, release_date) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
     ON CONFLICT (profile_id, media_id, media_type) DO NOTHING
     RETURNING *`,
    [user_id, profile_id, media_id, media_type, title, poster_path, vote_average, release_date]
  );
  
  return result.rows[0];
};

export const removeFromWatchlist = async (profileId, mediaId, mediaType) => {
  const result = await query(
    'DELETE FROM watchlist WHERE profile_id = $1 AND media_id = $2 AND media_type = $3 RETURNING *',
    [profileId, mediaId, mediaType]
  );
  
  return result.rows[0];
};

export const getWatchlist = async (profileId) => {
  const result = await query(
    'SELECT * FROM watchlist WHERE profile_id = $1 ORDER BY added_at DESC',
    [profileId]
  );
  
  return result.rows;
};

export const isInWatchlist = async (profileId, mediaId, mediaType) => {
  const result = await query(
    'SELECT COUNT(*) as count FROM watchlist WHERE profile_id = $1 AND media_id = $2 AND media_type = $3',
    [profileId, mediaId, mediaType]
  );
  
  return parseInt(result.rows[0].count) > 0;
};

// Watch progress functions
export const saveWatchProgress = async (progressData) => {
  const { 
    user_id, profile_id, media_id, media_type, progress_seconds, 
    duration_seconds, completed, season_number, episode_number 
  } = progressData;
  
  const result = await query(
    `INSERT INTO watch_progress (user_id, profile_id, media_id, media_type, progress_seconds, duration_seconds, completed, season_number, episode_number) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
     ON CONFLICT (profile_id, media_id, media_type, season_number, episode_number) 
     DO UPDATE SET 
       progress_seconds = EXCLUDED.progress_seconds,
       duration_seconds = EXCLUDED.duration_seconds,
       completed = EXCLUDED.completed,
       last_watched = CURRENT_TIMESTAMP
     RETURNING *`,
    [user_id, profile_id, media_id, media_type, progress_seconds, duration_seconds, completed, season_number, episode_number]
  );
  
  return result.rows[0];
};

export const getWatchProgress = async (profileId, mediaId, mediaType, seasonNumber = null, episodeNumber = null) => {
  let queryText = 'SELECT * FROM watch_progress WHERE profile_id = $1 AND media_id = $2 AND media_type = $3';
  let params = [profileId, mediaId, mediaType];
  
  if (seasonNumber !== null) {
    queryText += ' AND season_number = $4';
    params.push(seasonNumber);
  }
  
  if (episodeNumber !== null) {
    queryText += ' AND episode_number = $5';
    params.push(episodeNumber);
  }
  
  const result = await query(queryText, params);
  return result.rows[0] || null;
};

// Search history functions
export const saveSearchHistory = async (userId, profileId, query) => {
  await query(
    'INSERT INTO search_history (user_id, profile_id, query) VALUES ($1, $2, $3)',
    [userId, profileId, query]
  );
};

export const getSearchHistory = async (profileId, limit = 10) => {
  const result = await query(
    'SELECT * FROM search_history WHERE profile_id = $1 ORDER BY searched_at DESC LIMIT $2',
    [profileId, limit]
  );
  
  return result.rows;
};

// Viewing history functions
export const saveViewingHistory = async (historyData) => {
  const { user_id, profile_id, media_id, media_type, rating } = historyData;
  
  const result = await query(
    `INSERT INTO viewing_history (user_id, profile_id, media_id, media_type, rating) 
     VALUES ($1, $2, $3, $4, $5) 
     RETURNING *`,
    [user_id, profile_id, media_id, media_type, rating]
  );
  
  return result.rows[0];
};

export const getViewingHistory = async (profileId, limit = 20) => {
  const result = await query(
    'SELECT * FROM viewing_history WHERE profile_id = $1 ORDER BY watched_at DESC LIMIT $2',
    [profileId, limit]
  );
  
  return result.rows;
};

// Cleanup function for expired data
export const cleanupExpiredData = async () => {
  try {
    // Clean up old search history (keep last 100 per profile)
    await query(`
      DELETE FROM search_history 
      WHERE id NOT IN (
        SELECT id FROM search_history 
        WHERE profile_id = profile_id 
        ORDER BY searched_at DESC 
        LIMIT 100
      )
    `);
    
    // Clean up old viewing history (keep last 500 per profile)
    await query(`
      DELETE FROM viewing_history 
      WHERE id NOT IN (
        SELECT id FROM viewing_history 
        WHERE profile_id = profile_id 
        ORDER BY watched_at DESC 
        LIMIT 500
      )
    `);
    
    logger.info('Database cleanup completed');
  } catch (error) {
    logger.error({ 
      event: 'cleanup_failed',
      error: error.message 
    });
  }
};

// Graceful shutdown
export const closePool = async () => {
  await pool.end();
  logger.info('Database pool closed');
};

export default {
  testConnection,
  query,
  transaction,
  createUser,
  getUserByUsername,
  getUserById,
  createProfile,
  getProfilesByUserId,
  getProfileById,
  addToWatchlist,
  removeFromWatchlist,
  getWatchlist,
  isInWatchlist,
  saveWatchProgress,
  getWatchProgress,
  saveSearchHistory,
  getSearchHistory,
  saveViewingHistory,
  getViewingHistory,
  cleanupExpiredData,
  closePool
}; 