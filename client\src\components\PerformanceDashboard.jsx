import React, { useState, useEffect, useCallback } from 'react';
import { performanceMonitor, imageLoader, apiCache } from '../utils/performance';

const PerformanceDashboard = ({ isOpen, onClose }) => {
  const [metrics, setMetrics] = useState({});
  const [cacheStats, setCacheStats] = useState({});
  const [imageStats, setImageStats] = useState({});
  const [webVitals, setWebVitals] = useState({});
  const [refreshInterval, setRefreshInterval] = useState(2000);

  // Update metrics
  const updateMetrics = useCallback(() => {
    setMetrics(performanceMonitor.getMetrics());
    
    // Cache statistics
    setCacheStats({
      size: apiCache.cache.size,
      maxAge: apiCache.maxAge,
      entries: Array.from(apiCache.cache.entries()).length
    });

    // Image loader statistics
    setImageStats({
      cachedImages: imageLoader.imageCache.size,
      observerActive: !!imageLoader.observer
    });

    // Web Vitals (if available)
    if (window.performance && window.performance.getEntriesByType) {
      const navigationEntries = window.performance.getEntriesByType('navigation');
      if (navigationEntries.length > 0) {
        const nav = navigationEntries[0];
        setWebVitals({
          domContentLoaded: nav.domContentLoadedEventEnd - nav.domContentLoadedEventStart,
          loadComplete: nav.loadEventEnd - nav.loadEventStart,
          domInteractive: nav.domInteractive - nav.fetchStart,
          firstPaint: window.performance.getEntriesByName('first-paint')[0]?.startTime || 0,
          firstContentfulPaint: window.performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
        });
      }
    }
  }, []);

  // Auto-refresh metrics
  useEffect(() => {
    if (!isOpen) return;

    updateMetrics();
    const interval = setInterval(updateMetrics, refreshInterval);

    return () => clearInterval(interval);
  }, [isOpen, refreshInterval, updateMetrics]);

  // Clear all caches
  const clearAllCaches = useCallback(() => {
    apiCache.clear();
    imageLoader.imageCache.clear();
    updateMetrics();
  }, [updateMetrics]);

  // Clear expired cache entries
  const clearExpiredCache = useCallback(() => {
    apiCache.clearExpired();
    updateMetrics();
  }, [updateMetrics]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-white text-xl font-bold">🚀 Performance Dashboard</h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Metrics */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                ⏱️ Performance Metrics
              </h3>
              <div className="space-y-3">
                {Object.entries(metrics).map(([name, metric]) => (
                  <div key={name} className="flex justify-between items-center">
                    <span className="text-gray-300 text-sm">{name}</span>
                    <span className="text-white font-mono text-sm">
                      {metric.duration ? `${metric.duration.toFixed(2)}ms` : 'N/A'}
                    </span>
                  </div>
                ))}
                {Object.keys(metrics).length === 0 && (
                  <p className="text-gray-500 text-sm">No metrics recorded yet</p>
                )}
              </div>
            </div>

            {/* Web Vitals */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                📊 Web Vitals
              </h3>
              <div className="space-y-3">
                {Object.entries(webVitals).map(([name, value]) => (
                  <div key={name} className="flex justify-between items-center">
                    <span className="text-gray-300 text-sm capitalize">
                      {name.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    <span className="text-white font-mono text-sm">
                      {value > 0 ? `${value.toFixed(2)}ms` : 'N/A'}
                    </span>
                  </div>
                ))}
                {Object.keys(webVitals).length === 0 && (
                  <p className="text-gray-500 text-sm">Web vitals not available</p>
                )}
              </div>
            </div>

            {/* Cache Statistics */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                💾 Cache Statistics
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">API Cache Entries</span>
                  <span className="text-white font-mono text-sm">{cacheStats.entries || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Cache Max Age</span>
                  <span className="text-white font-mono text-sm">
                    {cacheStats.maxAge ? `${Math.round(cacheStats.maxAge / 1000)}s` : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Cached Images</span>
                  <span className="text-white font-mono text-sm">{imageStats.cachedImages || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Lazy Loader Active</span>
                  <span className="text-white font-mono text-sm">
                    {imageStats.observerActive ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              
              {/* Cache Actions */}
              <div className="mt-4 space-y-2">
                <button
                  onClick={clearExpiredCache}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-3 rounded transition-colors"
                >
                  Clear Expired Cache
                </button>
                <button
                  onClick={clearAllCaches}
                  className="w-full bg-red-600 hover:bg-red-700 text-white text-sm py-2 px-3 rounded transition-colors"
                >
                  Clear All Caches
                </button>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                🧠 Memory Usage
              </h3>
              <div className="space-y-3">
                {window.performance && window.performance.memory ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Used Heap Size</span>
                      <span className="text-white font-mono text-sm">
                        {Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024)}MB
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Total Heap Size</span>
                      <span className="text-white font-mono text-sm">
                        {Math.round(window.performance.memory.totalJSHeapSize / 1024 / 1024)}MB
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Heap Size Limit</span>
                      <span className="text-white font-mono text-sm">
                        {Math.round(window.performance.memory.jsHeapSizeLimit / 1024 / 1024)}MB
                      </span>
                    </div>
                    
                    {/* Memory usage bar */}
                    <div className="mt-4">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>Memory Usage</span>
                        <span>
                          {Math.round((window.performance.memory.usedJSHeapSize / window.performance.memory.jsHeapSizeLimit) * 100)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-green-500 to-yellow-500 h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${(window.performance.memory.usedJSHeapSize / window.performance.memory.jsHeapSizeLimit) * 100}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </>
                ) : (
                  <p className="text-gray-500 text-sm">Memory API not available</p>
                )}
              </div>
            </div>

            {/* Network Status */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                🌐 Network Status
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Connection Type</span>
                  <span className="text-white font-mono text-sm">
                    {navigator.connection ? navigator.connection.effectiveType || 'Unknown' : 'Unknown'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Downlink</span>
                  <span className="text-white font-mono text-sm">
                    {navigator.connection ? `${navigator.connection.downlink}Mbps` : 'Unknown'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">RTT</span>
                  <span className="text-white font-mono text-sm">
                    {navigator.connection ? `${navigator.connection.rtt}ms` : 'Unknown'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Save Data</span>
                  <span className="text-white font-mono text-sm">
                    {navigator.connection ? (navigator.connection.saveData ? 'Yes' : 'No') : 'Unknown'}
                  </span>
                </div>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                💡 Performance Tips
              </h3>
              <div className="space-y-2 text-sm">
                <div className="text-gray-300">
                  • Use lazy loading for images below the fold
                </div>
                <div className="text-gray-300">
                  • Enable code splitting for faster initial loads
                </div>
                <div className="text-gray-300">
                  • Cache API responses to reduce network requests
                </div>
                <div className="text-gray-300">
                  • Monitor memory usage to prevent leaks
                </div>
                <div className="text-gray-300">
                  • Optimize bundle size with tree shaking
                </div>
              </div>
            </div>
          </div>

          {/* Refresh Rate Control */}
          <div className="mt-6 bg-gray-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-white font-semibold">Refresh Rate</span>
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(Number(e.target.value))}
                className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600"
              >
                <option value={1000}>1 second</option>
                <option value={2000}>2 seconds</option>
                <option value={5000}>5 seconds</option>
                <option value={10000}>10 seconds</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard; 